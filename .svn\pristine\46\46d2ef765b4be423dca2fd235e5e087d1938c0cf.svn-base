﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="OnlineNoStart" xml:space="preserve">
    <value>未启动</value>
  </data>
  <data name="SaveError" xml:space="preserve">
    <value>文件名为'{0}'的文件，已保存到服务器！</value>
  </data>
  <data name="ExcepType" xml:space="preserve">
    <value>非预期的数据类型.</value>
  </data>
  <data name="BusinessTest" xml:space="preserve">
    <value>业务测试</value>
  </data>
  <data name="LDSBusinessTest" xml:space="preserve">
    <value>LDS业务测试</value>
  </data>
  <data name="DecBusinessTest" xml:space="preserve">
    <value>DDS业务测试</value>
  </data>
  <data name="FolderDeleted" xml:space="preserve">
    <value>文件夹为'{0}'，已删除！</value>
  </data>
  <data name="Running" xml:space="preserve">
    <value>CAD任务服务: 运行中......</value>
  </data>
  <data name="Connected" xml:space="preserve">
    <value>已连接</value>
  </data>
  <data name="NotConnected" xml:space="preserve">
    <value>未连接</value>
  </data>
  <data name="TaskStop" xml:space="preserve">
    <value>  CAD任务服务: 停止</value>
  </data>
  <data name="ExitInfo" xml:space="preserve">
    <value>真的要退出吗？</value>
  </data>
  <data name="PassError" xml:space="preserve">
    <value>密码错误! 请重新输入</value>
  </data>
  <data name="GetTask" xml:space="preserve">
    <value>已得到任务，任务编号为'{0}'，但任务列表中无数据，CAD任务完成！</value>
  </data>
  <data name="BusinessFormal" xml:space="preserve">
    <value>业务正式运行</value>
  </data>
  <data name="LDSBusinessFormal" xml:space="preserve">
    <value>LDS业务运行</value>
  </data>
  <data name="DecBusinessFormal" xml:space="preserve">
    <value>DDS业务运行</value>
  </data>
  <data name="DesignTest" xml:space="preserve">
    <value>开发测试</value>
  </data>
  <data name="LDSDesignTest" xml:space="preserve">
    <value>LDS开发测试</value>
  </data>
  <data name="DecDesignTest" xml:space="preserve">
    <value>DDS开发测试</value>
  </data>
  <data name="LoginRepeat" xml:space="preserve">
    <value>用户已经登录! 请换一用户</value>
  </data>
  <data name="UserNameError" xml:space="preserve">
    <value>用户名错误!请重新输入</value>
  </data>
  <data name="OnlineStared" xml:space="preserve">
    <value>已启动</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="InsertRow" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\InsertRow.png;System.Drawing.Bitmap, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="BIMBusinessFormal" xml:space="preserve">
    <value>BIM业务运行</value>
  </data>
  <data name="BIMBusinessTest" xml:space="preserve">
    <value>BIM业务测试</value>
  </data>
  <data name="BIMDesignTest" xml:space="preserve">
    <value>BIM开发测试</value>
  </data>
  <data name="AutoQuitBtn" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\AutoQuitBtn.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="AutoQuitBtn1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\AutoQuitBtn1.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="AutoRunBtn" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\AutoRunBtn.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="AutoRunBtn1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\AutoRunBtn1.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="AutoRunBtn2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\AutoRunBtn2.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="GetTaskBtn" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\GetTaskBtn.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="GetTaskSelectBtn" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\GetTaskSelectBtn.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="QuitBtn" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\QuitBtn.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="QuitBtn1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\QuitBtn1.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="SetupBtn" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\SetupBtn.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="SetupBtn1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\SetupBtn1.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="SetupBtn2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\SetupBtn2.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="SetupBtn3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\SetupBtn3.png;System.Drawing.Bitmap, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
</root>
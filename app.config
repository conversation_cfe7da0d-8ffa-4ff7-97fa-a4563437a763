<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <system.serviceModel>
        <bindings>
            <netTcpBinding>
                <binding name="Zxtech.UpdateModelService.EdsUpdateModel" transferMode="Streamed">
                    <security mode="None"/>
                </binding>
            </netTcpBinding>
        </bindings>
        <client>
            <endpoint address="net.tcp://***********:1526/UpdateModelService/EdsUpdateModel" binding="netTcpBinding" bindingConfiguration="Zxtech.UpdateModelService.EdsUpdateModel" contract="UpdatemodelServer.IEdsUpdateModel" name="Zxtech.UpdateModelService.EdsUpdateModel">
                <identity>
                    <dns value="localhost"/>
                </identity>
            </endpoint>
        </client>
    </system.serviceModel>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.0"/></startup></configuration>

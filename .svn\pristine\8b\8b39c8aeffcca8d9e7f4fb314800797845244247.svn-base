<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:tns="http://schemas.datacontract.org/2004/07/System.Data" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/System.Data" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:complexType name="EntityKey">
    <xs:sequence>
      <xs:element minOccurs="0" name="EntityContainerName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="EntityKeyValues" nillable="true" type="tns:ArrayOfEntityKeyMember" />
      <xs:element minOccurs="0" name="EntitySetName" nillable="true" type="xs:string" />
    </xs:sequence>
    <xs:attribute ref="ser:Id" />
    <xs:attribute ref="ser:Ref" />
  </xs:complexType>
  <xs:element name="EntityKey" nillable="true" type="tns:EntityKey" />
  <xs:complexType name="ArrayOfEntityKeyMember">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EntityKeyMember" nillable="true" type="tns:EntityKeyMember" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEntityKeyMember" nillable="true" type="tns:ArrayOfEntityKeyMember" />
  <xs:complexType name="EntityKeyMember">
    <xs:sequence>
      <xs:element minOccurs="0" name="Key" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Value" nillable="true" type="xs:anyType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EntityKeyMember" nillable="true" type="tns:EntityKeyMember" />
</xs:schema>
﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="label12.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="groupBox_TaskType.Location" type="System.Drawing.Point, System.Drawing">
    <value>235, 246</value>
  </data>
  <data name="&gt;&gt;label25.Name" xml:space="preserve">
    <value>label25</value>
  </data>
  <data name="label13.Size" type="System.Drawing.Size, System.Drawing">
    <value>173, 12</value>
  </data>
  <data name="&gt;&gt;label4.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="label13.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 321</value>
  </data>
  <data name="button4.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 21</value>
  </data>
  <data name="&gt;&gt;label6.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBox_MakePDF.Location" type="System.Drawing.Point, System.Drawing">
    <value>450, 188</value>
  </data>
  <data name="label7.Text" xml:space="preserve">
    <value>板金部品	の	厚さ	出力要否</value>
  </data>
  <data name="&gt;&gt;label5.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="radioButton4.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;radioButton8.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="textBox7.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="button2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;button8.Name" xml:space="preserve">
    <value>button8</value>
  </data>
  <data name="&gt;&gt;label15.Name" xml:space="preserve">
    <value>label15</value>
  </data>
  <data name="&gt;&gt;textBox2.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;textBox_PdsPath.ZOrder" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="&gt;&gt;textBox_OutputFileType.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox4.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;textBox5.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label15.Location" type="System.Drawing.Point, System.Drawing">
    <value>77, 394</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="label10.TabIndex" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeProjectMap.Name" xml:space="preserve">
    <value>checkBox_MakeProjectMap</value>
  </data>
  <data name="&gt;&gt;radioButton7.Name" xml:space="preserve">
    <value>radioButton7</value>
  </data>
  <data name="checkBox5.TabIndex" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="textBox_EGIUserNo.TabIndex" type="System.Int32, mscorlib">
    <value>57</value>
  </data>
  <data name="label14.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;label14.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label11.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_MakePDF.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;checkBox1.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="checkBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>450, 154</value>
  </data>
  <data name="checkBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>235, 188</value>
  </data>
  <data name="&gt;&gt;textBox_SendbackUrl.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="checkBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>235, 155</value>
  </data>
  <data name="&gt;&gt;label22.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="button5.Location" type="System.Drawing.Point, System.Drawing">
    <value>469, 52</value>
  </data>
  <data name="&gt;&gt;EgiUrl.Name" xml:space="preserve">
    <value>EgiUrl</value>
  </data>
  <data name="&gt;&gt;button6.ZOrder" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="radioButton2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;textBox_EGIUserNo.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;button8.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="checkBox_MakeDWG.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="textBox_SendbackUrl.TabIndex" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="label11.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="&gt;&gt;radioButton7.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label18.TabIndex" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="&gt;&gt;label15.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label19.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label27.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;textBox3.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;button6.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>692, 426</value>
  </data>
  <data name="textBox6.TabIndex" type="System.Int32, mscorlib">
    <value>57</value>
  </data>
  <data name="&gt;&gt;label27.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;groupBox_Language.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label24.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label20.Name" xml:space="preserve">
    <value>label20</value>
  </data>
  <data name="&gt;&gt;label23.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton5.Parent" xml:space="preserve">
    <value>groupBox_Language</value>
  </data>
  <data name="&gt;&gt;button8.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeSpreadMap.Name" xml:space="preserve">
    <value>checkBox_MakeSpreadMap</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog4.Name" xml:space="preserve">
    <value>folderBrowserDialog4</value>
  </data>
  <data name="label12.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 12</value>
  </data>
  <data name="&gt;&gt;textBox_EGIPsw.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox3.Name" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;textBox_EGIUserNo.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_MakePDF.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label4.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;checkBox_UseDocumentDbResult.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="EgiUrl.Text" xml:space="preserve">
    <value>系统服务地址</value>
  </data>
  <data name="label6.Location" type="System.Drawing.Point, System.Drawing">
    <value>129, 156</value>
  </data>
  <data name="label23.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeSpreadMap.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="&gt;&gt;label10.Name" xml:space="preserve">
    <value>label10</value>
  </data>
  <data name="SendbackUrl.Location" type="System.Drawing.Point, System.Drawing">
    <value>93, 87</value>
  </data>
  <data name="button3.Text" xml:space="preserve">
    <value>...</value>
  </data>
  <data name="&gt;&gt;label5.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="SendbackUrl.TabIndex" type="System.Int32, mscorlib">
    <value>70</value>
  </data>
  <data name="&gt;&gt;textBox_SendbackUrl.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="textBox5.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 318</value>
  </data>
  <data name="&gt;&gt;label25.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="checkBox3.TabIndex" type="System.Int32, mscorlib">
    <value>67</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 12</value>
  </data>
  <data name="button7.Location" type="System.Drawing.Point, System.Drawing">
    <value>545, 448</value>
  </data>
  <data name="&gt;&gt;textBox_OutputFileType.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label28.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label8.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="button6.Location" type="System.Drawing.Point, System.Drawing">
    <value>469, 17</value>
  </data>
  <data name="groupBox2.TabIndex" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="&gt;&gt;textBox4.Name" xml:space="preserve">
    <value>textBox4</value>
  </data>
  <data name="&gt;&gt;label19.Name" xml:space="preserve">
    <value>label19</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="button3.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 21</value>
  </data>
  <data name="&gt;&gt;radioButton2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label26.TabIndex" type="System.Int32, mscorlib">
    <value>70</value>
  </data>
  <data name="&gt;&gt;label26.Name" xml:space="preserve">
    <value>label26</value>
  </data>
  <data name="&gt;&gt;label29.Name" xml:space="preserve">
    <value>label29</value>
  </data>
  <data name="checkBox6.Location" type="System.Drawing.Point, System.Drawing">
    <value>235, 125</value>
  </data>
  <data name="checkBox7.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 392</value>
  </data>
  <data name="&gt;&gt;checkBox4.Name" xml:space="preserve">
    <value>checkBox4</value>
  </data>
  <data name="&gt;&gt;label7.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="checkBox_MakeDWG.Size" type="System.Drawing.Size, System.Drawing">
    <value>15, 14</value>
  </data>
  <data name="&gt;&gt;label29.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox3.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;label7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label9.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_MakePDF.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;label7.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="textBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 77</value>
  </data>
  <data name="checkBox_MakeSpreadMap.TabIndex" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="label8.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 12</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;groupBox_Language.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFReadThickness.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog3.Type" xml:space="preserve">
    <value>System.Windows.Forms.FolderBrowserDialog, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="textBox_EGIUserNo.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 21</value>
  </data>
  <data name="button1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;label17.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox4.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label27.Name" xml:space="preserve">
    <value>label27</value>
  </data>
  <data name="&gt;&gt;textBox6.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;textBox_WorkPath.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox7.Name" xml:space="preserve">
    <value>textBox7</value>
  </data>
  <data name="radioButton6.Text" xml:space="preserve">
    <value>ja-JP</value>
  </data>
  <data name="&gt;&gt;checkBox_UseDocumentDbResult.Name" xml:space="preserve">
    <value>checkBox_UseDocumentDbResult</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeDWG.Name" xml:space="preserve">
    <value>checkBox_MakeDWG</value>
  </data>
  <data name="&gt;&gt;textBox_EGIUserNo.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="&gt;&gt;radioButton3.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="label5.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 12</value>
  </data>
  <data name="&gt;&gt;textBox2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;label30.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="checkBox_MakeSpreadMap.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;label17.Name" xml:space="preserve">
    <value>label17</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeDWG.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;button5.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label14.Name" xml:space="preserve">
    <value>label14</value>
  </data>
  <data name="label26.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="textBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 52</value>
  </data>
  <data name="&gt;&gt;label25.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;textBox2.Name" xml:space="preserve">
    <value>textBox2</value>
  </data>
  <data name="textBox_OutputFileType.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 236</value>
  </data>
  <data name="&gt;&gt;checkBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label14.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 12</value>
  </data>
  <data name="&gt;&gt;checkBox_UseDocumentDbResult.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog2.Name" xml:space="preserve">
    <value>folderBrowserDialog2</value>
  </data>
  <data name="&gt;&gt;label12.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="textBox_OutputFileType.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 21</value>
  </data>
  <data name="checkBox_MakeSpreadMap.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textBox_SendbackUrl.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 21</value>
  </data>
  <data name="&gt;&gt;button4.Name" xml:space="preserve">
    <value>button4</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>59, 20</value>
  </data>
  <data name="&gt;&gt;radioButton8.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;checkBox7.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton7.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label19.Location" type="System.Drawing.Point, System.Drawing">
    <value>299, 156</value>
  </data>
  <data name="label4.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 12</value>
  </data>
  <data name="button2.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton3.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label14.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label21.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;button4.ZOrder" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="button4.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="&gt;&gt;label30.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_UseDocumentDbResult.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>65, 12</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog2.Type" xml:space="preserve">
    <value>System.Windows.Forms.FolderBrowserDialog, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="checkBox_MakeProjectMap.Location" type="System.Drawing.Point, System.Drawing">
    <value>235, 125</value>
  </data>
  <data name="EgiUrl.Location" type="System.Drawing.Point, System.Drawing">
    <value>94, 113</value>
  </data>
  <data name="radioButton2.Text" xml:space="preserve">
    <value>测试</value>
  </data>
  <data name="radioButton7.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="label14.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 360</value>
  </data>
  <data name="&gt;&gt;label22.Name" xml:space="preserve">
    <value>label22</value>
  </data>
  <data name="label15.TabIndex" type="System.Int32, mscorlib">
    <value>61</value>
  </data>
  <data name="label29.TabIndex" type="System.Int32, mscorlib">
    <value>58</value>
  </data>
  <data name="&gt;&gt;textBox5.Name" xml:space="preserve">
    <value>textBox5</value>
  </data>
  <data name="&gt;&gt;checkBox4.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button5.ZOrder" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="&gt;&gt;radioButton3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;EgiUrl.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;label26.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label15.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;textBox_SendbackUrl.Name" xml:space="preserve">
    <value>textBox_SendbackUrl</value>
  </data>
  <data name="&gt;&gt;textBox_WorkPath.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="radioButton5.Location" type="System.Drawing.Point, System.Drawing">
    <value>102, 15</value>
  </data>
  <data name="&gt;&gt;label16.Name" xml:space="preserve">
    <value>label16</value>
  </data>
  <data name="radioButton2.Location" type="System.Drawing.Point, System.Drawing">
    <value>98, 13</value>
  </data>
  <data name="radioButton5.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;label12.Name" xml:space="preserve">
    <value>label12</value>
  </data>
  <data name="textBox_EGIPsw.TabIndex" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="label4.Text" xml:space="preserve">
    <value>ブロック	分解要否</value>
  </data>
  <data name="radioButton8.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 15</value>
  </data>
  <data name="label12.Location" type="System.Drawing.Point, System.Drawing">
    <value>58, 292</value>
  </data>
  <data name="&gt;&gt;label14.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="label17.TabIndex" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="&gt;&gt;label23.Name" xml:space="preserve">
    <value>label23</value>
  </data>
  <data name="radioButton1.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 16</value>
  </data>
  <data name="checkBox5.Location" type="System.Drawing.Point, System.Drawing">
    <value>450, 123</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>155, 259</value>
  </data>
  <data name="checkBox_MakeSpreadMap.Location" type="System.Drawing.Point, System.Drawing">
    <value>450, 123</value>
  </data>
  <data name="label6.Text" xml:space="preserve">
    <value>伸び	線出力要否</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.TabIndex" type="System.Int32, mscorlib">
    <value>62</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label10.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeProjectMap.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label24.ZOrder" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="button8.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>83, 12</value>
  </data>
  <data name="&gt;&gt;checkBox5.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label8.Name" xml:space="preserve">
    <value>label8</value>
  </data>
  <data name="label28.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 360</value>
  </data>
  <data name="&gt;&gt;textBox_EGIPsw.Name" xml:space="preserve">
    <value>textBox_EGIPsw</value>
  </data>
  <data name="radioButton6.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label24.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="&gt;&gt;label15.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="textBox5.TabIndex" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="&gt;&gt;checkBox_MakePDF.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;radioButton6.Parent" xml:space="preserve">
    <value>groupBox_Language</value>
  </data>
  <data name="&gt;&gt;radioButton4.Name" xml:space="preserve">
    <value>radioButton4</value>
  </data>
  <data name="&gt;&gt;button7.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label9.TabIndex" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="label10.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;groupBox4.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="textBox_SendbackUrl.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 84</value>
  </data>
  <data name="&gt;&gt;textBox7.ZOrder" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="checkBox_MakePDF.TabIndex" type="System.Int32, mscorlib">
    <value>69</value>
  </data>
  <data name="textBox6.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 289</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox2.Name" xml:space="preserve">
    <value>checkBox2</value>
  </data>
  <data name="&gt;&gt;button4.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;textBox1.ZOrder" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="label7.TabIndex" type="System.Int32, mscorlib">
    <value>66</value>
  </data>
  <data name="&gt;&gt;textBox6.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;radioButton5.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton2.Name" xml:space="preserve">
    <value>radioButton2</value>
  </data>
  <data name="button4.Text" xml:space="preserve">
    <value>...</value>
  </data>
  <data name="&gt;&gt;button5.Name" xml:space="preserve">
    <value>button5</value>
  </data>
  <data name="label25.TabIndex" type="System.Int32, mscorlib">
    <value>72</value>
  </data>
  <data name="textBox_EGIUserNo.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 289</value>
  </data>
  <data name="label27.TabIndex" type="System.Int32, mscorlib">
    <value>61</value>
  </data>
  <data name="&gt;&gt;label10.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="label4.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog4.Type" xml:space="preserve">
    <value>System.Windows.Forms.FolderBrowserDialog, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label12.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="radioButton3.Location" type="System.Drawing.Point, System.Drawing">
    <value>98, 13</value>
  </data>
  <data name="&gt;&gt;button4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label22.Location" type="System.Drawing.Point, System.Drawing">
    <value>119, 126</value>
  </data>
  <data name="label6.Size" type="System.Drawing.Size, System.Drawing">
    <value>89, 12</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog1.Type" xml:space="preserve">
    <value>System.Windows.Forms.FolderBrowserDialog, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox_Language.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 41</value>
  </data>
  <data name="&gt;&gt;label6.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="button7.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 12</value>
  </data>
  <data name="&gt;&gt;label13.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="button4.Location" type="System.Drawing.Point, System.Drawing">
    <value>469, 52</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeDWG.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button6.Name" xml:space="preserve">
    <value>button6</value>
  </data>
  <data name="label7.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label9.Location" type="System.Drawing.Point, System.Drawing">
    <value>317, 188</value>
  </data>
  <data name="&gt;&gt;button1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;textBox_EgiUrl.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label22.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="label5.Text" xml:space="preserve">
    <value>DXF	ファイル	出力要否</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;groupBox_Language.Name" xml:space="preserve">
    <value>groupBox_Language</value>
  </data>
  <data name="label27.Location" type="System.Drawing.Point, System.Drawing">
    <value>77, 394</value>
  </data>
  <data name="&gt;&gt;radioButton3.Name" xml:space="preserve">
    <value>radioButton3</value>
  </data>
  <data name="checkBox_IsDXFBendLines.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label4.Location" type="System.Drawing.Point, System.Drawing">
    <value>119, 126</value>
  </data>
  <data name="label4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;radioButton5.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="label15.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 12</value>
  </data>
  <data name="label26.Location" type="System.Drawing.Point, System.Drawing">
    <value>93, 87</value>
  </data>
  <data name="&gt;&gt;groupBox_TaskType.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="label5.Location" type="System.Drawing.Point, System.Drawing">
    <value>317, 125</value>
  </data>
  <data name="&gt;&gt;label10.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="&gt;&gt;button7.Name" xml:space="preserve">
    <value>button7</value>
  </data>
  <data name="checkBox_MakePDF.Size" type="System.Drawing.Size, System.Drawing">
    <value>15, 14</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="label25.Location" type="System.Drawing.Point, System.Drawing">
    <value>94, 113</value>
  </data>
  <data name="&gt;&gt;label23.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;button2.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="button7.TabIndex" type="System.Int32, mscorlib">
    <value>75</value>
  </data>
  <data name="&gt;&gt;textBox_SendbackUrl.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;label16.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;button6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label30.TabIndex" type="System.Int32, mscorlib">
    <value>56</value>
  </data>
  <data name="&gt;&gt;button7.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.Size" type="System.Drawing.Size, System.Drawing">
    <value>15, 14</value>
  </data>
  <data name="&gt;&gt;label20.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="&gt;&gt;groupBox4.Name" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;radioButton4.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label19.TabIndex" type="System.Int32, mscorlib">
    <value>66</value>
  </data>
  <data name="&gt;&gt;checkBox7.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;EgiUrl.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="groupBox3.TabIndex" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>模型模板位置:</value>
  </data>
  <data name="&gt;&gt;label17.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label28.Name" xml:space="preserve">
    <value>label28</value>
  </data>
  <data name="radioButton1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;button3.ZOrder" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="&gt;&gt;button7.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label30.Name" xml:space="preserve">
    <value>label30</value>
  </data>
  <data name="groupBox4.TabIndex" type="System.Int32, mscorlib">
    <value>54</value>
  </data>
  <data name="&gt;&gt;checkBox7.Name" xml:space="preserve">
    <value>checkBox7</value>
  </data>
  <data name="&gt;&gt;textBox_WorkPath.ZOrder" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 345</value>
  </data>
  <data name="textBox_OutputFileType.TabIndex" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="&gt;&gt;label10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton2.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>模型输出位置:</value>
  </data>
  <data name="SendbackUrl.Text" xml:space="preserve">
    <value>回调url</value>
  </data>
  <data name="label3.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="&gt;&gt;checkBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button1.Name" xml:space="preserve">
    <value>button1</value>
  </data>
  <data name="&gt;&gt;textBox5.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;groupBox_TaskType.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="textBox_PdsPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 17</value>
  </data>
  <data name="label8.Text" xml:space="preserve">
    <value>DWG	ファイル	出力要否</value>
  </data>
  <data name="button6.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>716, 477</value>
  </data>
  <data name="&gt;&gt;groupBox3.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>119, 227</value>
  </data>
  <data name="button8.TabIndex" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="checkBox_IsDXFBendLines.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;label29.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;checkBox6.Name" xml:space="preserve">
    <value>checkBox6</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="radioButton2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;button1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="groupBox_Language.TabIndex" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="label22.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="label14.TabIndex" type="System.Int32, mscorlib">
    <value>60</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;textBox_EgiUrl.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;label17.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="&gt;&gt;radioButton7.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;button2.Name" xml:space="preserve">
    <value>button2</value>
  </data>
  <data name="&gt;&gt;label18.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="&gt;&gt;label16.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="label15.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton6.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textBox_WorkPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 21</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.Location" type="System.Drawing.Point, System.Drawing">
    <value>450, 154</value>
  </data>
  <data name="label7.Location" type="System.Drawing.Point, System.Drawing">
    <value>299, 156</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFBendLines.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="button3.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;textBox_EgiUrl.Name" xml:space="preserve">
    <value>textBox_EgiUrl</value>
  </data>
  <data name="&gt;&gt;checkBox_MakePDF.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label23.Location" type="System.Drawing.Point, System.Drawing">
    <value>117, 55</value>
  </data>
  <data name="label9.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textBox_PdsPath.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox6.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label13.TabIndex" type="System.Int32, mscorlib">
    <value>58</value>
  </data>
  <data name="label8.TabIndex" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="&gt;&gt;label21.Name" xml:space="preserve">
    <value>label21</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>117, 55</value>
  </data>
  <data name="&gt;&gt;SendbackUrl.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="checkBox_MakeDWG.Location" type="System.Drawing.Point, System.Drawing">
    <value>235, 188</value>
  </data>
  <data name="&gt;&gt;textBox_WorkPath.Name" xml:space="preserve">
    <value>textBox_WorkPath</value>
  </data>
  <data name="textBox_EGIPsw.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 21</value>
  </data>
  <data name="&gt;&gt;textBox_EgiUrl.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;radioButton4.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;SendbackUrl.Name" xml:space="preserve">
    <value>SendbackUrl</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.TabIndex" type="System.Int32, mscorlib">
    <value>67</value>
  </data>
  <data name="&gt;&gt;textBox_EGIUserNo.Name" xml:space="preserve">
    <value>textBox_EGIUserNo</value>
  </data>
  <data name="&gt;&gt;SendbackUrl.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;checkBox5.Name" xml:space="preserve">
    <value>checkBox5</value>
  </data>
  <data name="&gt;&gt;label12.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;radioButton1.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label3.Location" type="System.Drawing.Point, System.Drawing">
    <value>155, 259</value>
  </data>
  <data name="label9.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 12</value>
  </data>
  <data name="checkBox_MakeProjectMap.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;button2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button3.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;checkBox6.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="&gt;&gt;label19.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="checkBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>450, 188</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;checkBox4.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="checkBox_MakeProjectMap.TabIndex" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="&gt;&gt;label30.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeProjectMap.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="label16.TabIndex" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="&gt;&gt;checkBox3.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="&gt;&gt;label13.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="checkBox_IsDXFBendLines.Size" type="System.Drawing.Size, System.Drawing">
    <value>15, 14</value>
  </data>
  <data name="button1.Location" type="System.Drawing.Point, System.Drawing">
    <value>548, 444</value>
  </data>
  <data name="label20.Location" type="System.Drawing.Point, System.Drawing">
    <value>129, 156</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>配置</value>
  </data>
  <data name="&gt;&gt;button3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="textBox_WorkPath.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="checkBox_IsDXFBendLines.Location" type="System.Drawing.Point, System.Drawing">
    <value>235, 155</value>
  </data>
  <data name="button2.Text" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="&gt;&gt;radioButton1.Parent" xml:space="preserve">
    <value>groupBox_TaskType</value>
  </data>
  <data name="textBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 21</value>
  </data>
  <data name="label11.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label13.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton6.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 15</value>
  </data>
  <data name="radioButton5.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 16</value>
  </data>
  <data name="&gt;&gt;textBox4.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="checkBox_MakeSpreadMap.Size" type="System.Drawing.Size, System.Drawing">
    <value>15, 14</value>
  </data>
  <data name="radioButton5.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;groupBox4.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="label5.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;label9.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label7.Size" type="System.Drawing.Size, System.Drawing">
    <value>137, 12</value>
  </data>
  <data name="label5.TabIndex" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="&gt;&gt;label18.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="label13.Text" xml:space="preserve">
    <value>サーバ	IP	アドレス	のパスワード</value>
  </data>
  <data name="&gt;&gt;textBox6.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>83, 12</value>
  </data>
  <data name="&gt;&gt;textBox5.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="label6.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 21</value>
  </data>
  <data name="&gt;&gt;label22.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="button3.Location" type="System.Drawing.Point, System.Drawing">
    <value>469, 17</value>
  </data>
  <data name="&gt;&gt;label13.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;textBox6.Name" xml:space="preserve">
    <value>textBox6</value>
  </data>
  <data name="&gt;&gt;checkBox4.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label16.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox_TaskType.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="radioButton5.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;button5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton2.Parent" xml:space="preserve">
    <value>groupBox_TaskType</value>
  </data>
  <data name="label6.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_MakeProjectMap.Size" type="System.Drawing.Size, System.Drawing">
    <value>15, 14</value>
  </data>
  <data name="&gt;&gt;label18.Name" xml:space="preserve">
    <value>label18</value>
  </data>
  <data name="&gt;&gt;label25.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton6.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;label23.ZOrder" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>FormParameterSet</value>
  </data>
  <data name="&gt;&gt;checkBox6.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="label13.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox6.TabIndex" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeSpreadMap.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox_PdsPath.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="label25.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="textBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 84</value>
  </data>
  <data name="&gt;&gt;checkBox_MakePDF.Name" xml:space="preserve">
    <value>checkBox_MakePDF</value>
  </data>
  <data name="&gt;&gt;label7.Name" xml:space="preserve">
    <value>label7</value>
  </data>
  <data name="&gt;&gt;label20.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;textBox3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="groupBox_Language.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 345</value>
  </data>
  <data name="label18.Location" type="System.Drawing.Point, System.Drawing">
    <value>99, 189</value>
  </data>
  <data name="textBox4.TabIndex" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="&gt;&gt;checkBox3.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="textBox7.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 17</value>
  </data>
  <data name="&gt;&gt;radioButton1.Name" xml:space="preserve">
    <value>radioButton1</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>タスク	種類</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFReadThickness.Name" xml:space="preserve">
    <value>checkBox_IsDXFReadThickness</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFBendLines.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="textBox_EgiUrl.TabIndex" type="System.Int32, mscorlib">
    <value>73</value>
  </data>
  <data name="button1.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="label12.TabIndex" type="System.Int32, mscorlib">
    <value>56</value>
  </data>
  <data name="label15.Text" xml:space="preserve">
    <value>結果	をMongodb	に	出力要否</value>
  </data>
  <data name="label7.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog3.Name" xml:space="preserve">
    <value>folderBrowserDialog3</value>
  </data>
  <data name="textBox1.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFBendLines.Name" xml:space="preserve">
    <value>checkBox_IsDXFBendLines</value>
  </data>
  <data name="&gt;&gt;label8.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="label14.Text" xml:space="preserve">
    <value>言語</value>
  </data>
  <data name="&gt;&gt;textBox_EGIPsw.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;textBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label28.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;label13.Name" xml:space="preserve">
    <value>label13</value>
  </data>
  <data name="&gt;&gt;textBox1.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="label17.Location" type="System.Drawing.Point, System.Drawing">
    <value>317, 188</value>
  </data>
  <data name="&gt;&gt;checkBox2.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="&gt;&gt;label28.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;label9.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="textBox_WorkPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 52</value>
  </data>
  <data name="checkBox2.TabIndex" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 16</value>
  </data>
  <data name="checkBox_MakeDWG.TabIndex" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;checkBox5.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="&gt;&gt;label19.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="&gt;&gt;EgiUrl.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="EgiUrl.TabIndex" type="System.Int32, mscorlib">
    <value>72</value>
  </data>
  <data name="textBox_PdsPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>233, 21</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>任务类型</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="button1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeDWG.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="textBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 236</value>
  </data>
  <data name="label5.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_MakeDWG.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="groupBox_TaskType.TabIndex" type="System.Int32, mscorlib">
    <value>54</value>
  </data>
  <data name="&gt;&gt;label4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="button4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton6.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFReadThickness.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="radioButton2.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 16</value>
  </data>
  <data name="&gt;&gt;label29.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeProjectMap.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="label28.TabIndex" type="System.Int32, mscorlib">
    <value>60</value>
  </data>
  <data name="radioButton7.Location" type="System.Drawing.Point, System.Drawing">
    <value>102, 15</value>
  </data>
  <data name="&gt;&gt;radioButton1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label20.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="&gt;&gt;checkBox3.Name" xml:space="preserve">
    <value>checkBox3</value>
  </data>
  <data name="radioButton8.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textBox3.TabIndex" type="System.Int32, mscorlib">
    <value>73</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="radioButton5.Text" xml:space="preserve">
    <value>en-US</value>
  </data>
  <data name="&gt;&gt;textBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label3.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="&gt;&gt;checkBox1.Name" xml:space="preserve">
    <value>checkBox1</value>
  </data>
  <data name="&gt;&gt;textBox_PdsPath.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="checkBox_MakeProjectMap.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;label20.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="textBox_EGIPsw.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 318</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeSpreadMap.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 392</value>
  </data>
  <data name="textBox_EgiUrl.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 21</value>
  </data>
  <data name="groupBox_TaskType.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 36</value>
  </data>
  <data name="checkBox7.TabIndex" type="System.Int32, mscorlib">
    <value>62</value>
  </data>
  <data name="label9.Text" xml:space="preserve">
    <value>PDF	ファイル	出力要否</value>
  </data>
  <data name="label16.Location" type="System.Drawing.Point, System.Drawing">
    <value>119, 227</value>
  </data>
  <data name="label29.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 321</value>
  </data>
  <data name="&gt;&gt;label4.Name" xml:space="preserve">
    <value>label4</value>
  </data>
  <data name="label21.TabIndex" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="&gt;&gt;groupBox_TaskType.Name" xml:space="preserve">
    <value>groupBox_TaskType</value>
  </data>
  <data name="&gt;&gt;button3.Name" xml:space="preserve">
    <value>button3</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog1.Name" xml:space="preserve">
    <value>folderBrowserDialog1</value>
  </data>
  <data name="&gt;&gt;textBox7.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="SendbackUrl.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="label8.Location" type="System.Drawing.Point, System.Drawing">
    <value>99, 189</value>
  </data>
  <data name="groupBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>235, 246</value>
  </data>
  <data name="&gt;&gt;textBox7.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox7.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>系统设置</value>
  </data>
  <data name="&gt;&gt;label21.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox1.Name" xml:space="preserve">
    <value>textBox1</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFReadThickness.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label6.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Name" xml:space="preserve">
    <value>label5</value>
  </data>
  <data name="&gt;&gt;textBox_OutputFileType.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="radioButton4.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 13</value>
  </data>
  <data name="&gt;&gt;textBox_PdsPath.Name" xml:space="preserve">
    <value>textBox_PdsPath</value>
  </data>
  <data name="checkBox1.TabIndex" type="System.Int32, mscorlib">
    <value>69</value>
  </data>
  <data name="&gt;&gt;checkBox2.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;groupBox4.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="&gt;&gt;label8.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label26.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="button3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;label21.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="&gt;&gt;checkBox5.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;radioButton5.Name" xml:space="preserve">
    <value>radioButton5</value>
  </data>
  <data name="checkBox_IsDXFBendLines.TabIndex" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="EgiUrl.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="&gt;&gt;label3.Name" xml:space="preserve">
    <value>label3</value>
  </data>
  <data name="&gt;&gt;label9.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;textBox_EGIPsw.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;label18.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="textBox2.TabIndex" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="&gt;&gt;label26.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label8.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="button5.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="&gt;&gt;groupBox_Language.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="checkBox4.TabIndex" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="&gt;&gt;SendbackUrl.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;label24.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="radioButton3.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="label4.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="&gt;&gt;textBox3.Name" xml:space="preserve">
    <value>textBox3</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.Size" type="System.Drawing.Size, System.Drawing">
    <value>15, 14</value>
  </data>
  <data name="textBox_EgiUrl.Location" type="System.Drawing.Point, System.Drawing">
    <value>232, 77</value>
  </data>
  <data name="radioButton6.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 16</value>
  </data>
  <data name="button1.Text" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>サーバ	IP	アドレス	のユーザ	名</value>
  </data>
  <data name="&gt;&gt;radioButton8.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="label14.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;radioButton8.Name" xml:space="preserve">
    <value>radioButton8</value>
  </data>
  <data name="label21.Location" type="System.Drawing.Point, System.Drawing">
    <value>317, 125</value>
  </data>
  <data name="&gt;&gt;label3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label5.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton6.Name" xml:space="preserve">
    <value>radioButton6</value>
  </data>
  <data name="&gt;&gt;checkBox1.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="&gt;&gt;button8.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;label27.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="button8.Location" type="System.Drawing.Point, System.Drawing">
    <value>626, 448</value>
  </data>
  <data name="radioButton1.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 13</value>
  </data>
  <data name="button2.Location" type="System.Drawing.Point, System.Drawing">
    <value>629, 444</value>
  </data>
  <data name="&gt;&gt;checkBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label15.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="&gt;&gt;label24.Name" xml:space="preserve">
    <value>label24</value>
  </data>
  <data name="groupBox1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;textBox_OutputFileType.Name" xml:space="preserve">
    <value>textBox_OutputFileType</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFBendLines.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;button1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton6.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label12.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;label6.Name" xml:space="preserve">
    <value>label6</value>
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="label6.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="&gt;&gt;label9.Name" xml:space="preserve">
    <value>label9</value>
  </data>
  <data name="label30.Location" type="System.Drawing.Point, System.Drawing">
    <value>58, 292</value>
  </data>
  <data name="label24.Location" type="System.Drawing.Point, System.Drawing">
    <value>59, 20</value>
  </data>
  <data name="&gt;&gt;radioButton4.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="radioButton1.Text" xml:space="preserve">
    <value>正式</value>
  </data>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ja-JP</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>47</value>
  </metadata>
  <metadata name="folderBrowserDialog2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>557, 17</value>
  </metadata>
  <metadata name="folderBrowserDialog4.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>557, 17</value>
  </metadata>
  <metadata name="folderBrowserDialog3.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>377, 17</value>
  </metadata>
  <metadata name="folderBrowserDialog1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>377, 17</value>
  </metadata>
</root>
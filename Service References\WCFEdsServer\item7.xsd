<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:tns="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/System.Data" />
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:complexType name="EntityObject">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:StructuralObject">
        <xs:sequence>
          <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/System.Data" minOccurs="0" name="EntityKey" nillable="true" type="q1:EntityKey" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EntityObject" nillable="true" type="tns:EntityObject" />
  <xs:complexType name="StructuralObject">
    <xs:sequence />
    <xs:attribute ref="ser:Id" />
    <xs:attribute ref="ser:Ref" />
  </xs:complexType>
  <xs:element name="StructuralObject" nillable="true" type="tns:StructuralObject" />
</xs:schema>
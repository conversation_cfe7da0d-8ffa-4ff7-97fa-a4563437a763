# CADTaskServer 详细设计文档

## 1. 文档说明

本文档详细描述了CADTaskServer系统的类设计、方法实现、数据流程和交互关系。包含完整的类图、时序图以及核心方法的伪代码实现。

## 2. 系统类图

### 2.1 核心类关系图

```mermaid
classDiagram
    class CADTaskServer {
        -Timer GetTaskTimer
        -Timer GetTaskTimerOnTaskRunEnd
        -FormCADTaskServer formCADTaskServer
        -ServiceHost host
        -CADWorkStationInfo workStationInfo
        +Load(int language) CADWorkStationInfo
        +GetTaskCadCodeList() List~CADTaskCode~
        +RunCADCode(CADTaskCode cadCode) CADTaskCode
        +UpdateTaskPartPropWithModel() bool
        +SendLogMessage(Guid taskId, string message, int messageType)
        +SetTaskRunEnd(Guid taskId)
        +UnLoad()
    }

    class FormCADTaskServer {
        -WCFServiceConfigInfo ServiceConfigInfo
        -IPlatformService CADDbConnect
        -EpdTaskInfoDto taskInfo
        -List~EpdCADCommandDto~ taskCadList
        -bool IsWCFConnect
        +GetServiceConfigInfo()
        +GetTaskCadCodeList() List~EpdCADCommandDto~
        +SendLogMessage(Guid taskId, string message, int messageType)
        +UploadResult(string attachmentPath, Guid taskId)
        +UpdateTaskPartPropWithModel() bool
        +GetDocoumentById() IList~string~
        +SaveCadLogFile()
    }

    class CADWCFServer {
        -CADTaskServer cad
        +RunCADCode(CADTaskCode cadTask) CADTaskCode
        +TestServer() bool
        +SetCustomPropertyInfo()
        +GetPropertys() Dictionary~string,string~
        +GetRefconfigList() List~string~
        +GetFeatureList() List~string~
    }

    class CADWorkStationInfo {
        +bool IsLoad
        +string PdsPath
        +string WorkPath
        +string CachePath
        +string BakPath
        +bool UseModelCache
        +bool bMakeDRW
        +bool bMakeDXF
        +bool bMakePDF
    }

    class CADTaskCode {
        +Guid TaskId
        +int SortId
        +int PartId
        +int CadCodeId
        +string Para1-Para20
        +Guid? EpdPartGuid
        +Guid? DocumentId
        +string ModelMd5
        +bool? UseCache
        +FinishStatus Finished
    }

    class ZipHelper {
        +ZipDirectory(string folderToZip) string
        +ZipDirectoryToBuffer(string folderToZip) byte[]
        +UnZip(byte[] zipFileContent, string TargetDirectory)
    }

    class ShareMemoryHelper {
        -IntPtr fileMapping
        -IntPtr mapView
        +CreateShareMemoryMap(string strName, long lngSize) int
        +Write(byte[] bytData, int lngAddr, int lngSize) int
        +Read(ref byte[] bytData, int lngAddr, int lngSize) int
        +Close()
    }

    class SingleQuoteStringParser {
        +Unpack(string text, out List~string~ list) bool
        +Pack(List~string~ list, out string text) bool
        +UnpackDictionary(string text) Dictionary~string,string~
        +PackString(Dictionary~string,string~ dic) string
    }

    CADTaskServer --> FormCADTaskServer : uses
    CADTaskServer --> CADWorkStationInfo : creates
    FormCADTaskServer --> CADTaskCode : processes
    CADWCFServer --> CADTaskServer : delegates to
    FormCADTaskServer --> ZipHelper : uses
    FormCADTaskServer --> SingleQuoteStringParser : uses
    CADTaskServer --> ShareMemoryHelper : may use
```

## 3. 核心类详细设计

### 3.1 CADTaskServer 类

#### 3.1.1 类说明
CADTaskServer是系统的核心业务类，负责任务调度、文件缓存、模型处理等主要业务逻辑。

#### 3.1.2 属性定义
```csharp
public class CADTaskServer
{
    // 定时器 - 用于定期获取任务
    private readonly System.Windows.Forms.Timer GetTaskTimer;
    
    // 定时器 - 任务运行结束后的处理
    private readonly Timer GetTaskTimerOnTaskRunEnd;
    
    // 主窗体引用
    public FormCADTaskServer formCADTaskServer;
    
    // WCF服务主机
    private ServiceHost host;
    
    // 是否已加载
    private bool isLoad;
    
    // 工作站信息
    private CADWorkStationInfo workStationInfo;
    
    // 静态实例
    public static FormCADTaskServer InstanceFormCADTaskServer;
    public static CADWorkStationInfo InstanceWorkStationInfo;
}
```

#### 3.1.3 核心方法实现

##### Load方法 - 系统初始化
```csharp
public CADWorkStationInfo Load(int language, int CadType, FormCADTaskServer mainForm=null)
{
    // 伪代码:
    try {
        // 1. 检查是否已有窗体实例
        if (IsHaveWindow()) {
            return InstanceWorkStationInfo;
        }
        
        // 2. 设置语言环境
        if (language == 1) {
            Thread.CurrentThread.CurrentUICulture = new CultureInfo("en-US");
        }
        
        // 3. 创建或使用传入的窗体
        if (mainForm != null) {
            formCADTaskServer = mainForm;
        } else {
            formCADTaskServer = new FormCADTaskServer();
        }
        
        // 4. 初始化工作站信息
        workStationInfo = new CADWorkStationInfo();
        InstanceWorkStationInfo = workStationInfo;
        InstanceFormCADTaskServer = formCADTaskServer;
        
        // 5. 设置事件处理
        formCADTaskServer.GetNewCADTask += formCADTaskServer_GetCADTask;
        formCADTaskServer.AutoGetNewCADTask += formCADTaskServer_AutoGetNewCADTask;
        
        // 6. 从配置文件加载工作站配置
        LoadWorkStationConfig();
        
        // 7. 启动WCF服务
        StartCADWCF(this, formCADTaskServer.ServiceConfigInfo.WCFCADServer);
        
        // 8. 初始化定时器
        InitializeTimers();
        
        return workStationInfo;
    }
    catch (Exception ex) {
        MessageBox.Show(ex.ToString());
        throw;
    }
}
```

##### GetTaskCadCodeList方法 - 获取任务列表
```csharp
public List<CADTaskCode> GetTaskCadCodeList()
{
    // 伪代码:
    if (formCADTaskServer == null) {
        throw new Exception("formCADTaskServer==null");
    }
    
    try {
        // 1. 从窗体获取任务命令列表
        List<EpdCADCommandDto> taskCadCodes = formCADTaskServer.GetTaskCadCodeList();
        
        if (taskCadCodes == null) return null;
        
        // 2. 转换为内部数据结构
        var listCADTaskCode = new List<CADTaskCode>();
        foreach (EpdCADCommandDto taskCadcode in taskCadCodes) {
            var taskCode = ConvertToCADTaskCode(taskCadcode);
            listCADTaskCode.Add(taskCode);
        }
        
        // 3. 设置任务运行状态
        formCADTaskServer.TaskRunning = true;
        
        // 4. 如果是自动模式，停止定时器
        if (formCADTaskServer.isAuto && listCADTaskCode.Count > 0) {
            GetTaskTimer.Stop();
            GetTaskTimerOnTaskRunEnd.Stop();
        }
        
        return listCADTaskCode;
    }
    catch (Exception e) {
        MessageBox.Show(e.ToString());
        return null;
    }
}
```

##### GetCacheFiles方法 - 文件缓存处理
```csharp
public List<CADTaskCode> GetCacheFiles(List<CADTaskCode> ls, Guid taskId, 
    List<CADCommandCache> totalCommands, CADWorkStationInfo stationInfo, string taskFoldName)
{
    // 伪代码:
    for (int i = 0; i < ls.Count; i++) {
        // 1. 检查是否需要使用缓存
        if ((!ls[i].UseCache.HasValue || !ls[i].UseCache.Value) && stationInfo.UseModelCache) {
            
            // 2. 根据DocumentId查找缓存文件
            IList<string> cacheFiles = formCADTaskServer.GetDocoumentById(
                ls[i].Para3, ls[i].DocumentId?.ToString(), 
                stationInfo.WorkPath + "\\" + taskFoldName);
            
            if (cacheFiles != null && cacheFiles.Any()) {
                // 3. 找到缓存，记录日志
                SendLogMessage(taskId, 
                    string.Format("Find cache document!modelName:{0} id:{1}", 
                    ls[i].Para3, ls[i].DocumentId), 1);
                
                // 4. 设置缓存标志和完成状态
                ls[i].UseCache = true;
                ls[i].Finished = FinishStatus.Finished;
                
                // 5. 处理相关的初始化命令
                var initCode = ls.FirstOrDefault(o => o.PartId == ls[i].PartId && o.CadCodeId == 1300);
                if (initCode != null) {
                    initCode.UseCache = true;
                    initCode.Finished = FinishStatus.Finished;
                    initCode.Files = cacheFiles;
                }
                
                // 6. 处理子命令缓存
                ProcessChildCommandCache(ls[i], totalCommands);
            }
        }
    }
    return ls;
}
```

### 3.2 FormCADTaskServer 类

#### 3.2.1 类说明
FormCADTaskServer是系统的主窗体类，负责用户界面、配置管理、任务显示和用户交互。

#### 3.2.2 属性定义
```csharp
public partial class FormCADTaskServer : Form
{
    // 配置文件路径
    private readonly String cfgFileName;
    public String cfgClientFileName;
    
    // 服务配置信息
    public WCFServiceConfigInfo ServiceConfigInfo { get; set; }
    
    // 数据库连接
    private IPlatformService CADDbConnect;
    private IPlatformService EdsTaskConnect;
    
    // 任务相关
    public List<EpdCADCommandDto> taskCadList { get; private set; }
    public EpdTaskInfoDto taskInfo;
    public bool TaskRunning;
    
    // 连接状态
    public bool IsWCFConnect { get; private set; }
    
    // 事件定义
    public event EventHandler GetNewCADTask;
    public event EventHandler AutoGetNewCADTask;
    public event EventHandler CovertHandGet;
}
```

#### 3.2.3 核心方法实现

##### GetServiceConfigInfo方法 - 配置文件读取
```csharp
private void GetServiceConfigInfo()
{
    // 伪代码:
    // 1. 读取配置文件内容
    ServerConfigText = File.ReadAllText(cfgFileName, Encoding.UTF8);
    ClientConfigText = File.ReadAllText(cfgClientFileName, Encoding.UTF8);
    
    // 2. 解析WCF服务配置
    ServiceConfigInfo.WCFServicePath = GetElement("WCFServicePath");
    ServiceConfigInfo.EndpointUrl = GetElement("EndpointUrl");
    ServiceConfigInfo.MaxArrayLength = GetElement("MaxArrayLength");
    
    // 3. 解析路径配置
    ServiceConfigInfo.PdsPath = GetElement("PdsPath");
    ServiceConfigInfo.WorkPath = GetElement("WorkPath");
    ServiceConfigInfo.CachePath = GetElement("CachePath");
    ServiceConfigInfo.BakPath = GetElement("BakPath");
    
    // 4. 解析功能开关
    ServiceConfigInfo.bMakeDRW = GetBool(GetElement("MakeProjectMap"));
    ServiceConfigInfo.bMakeDXF = GetBool(GetElement("MakeSpreadMap"));
    ServiceConfigInfo.UseModelCache = GetBool(GetElement("UseModelCache"));
    
    // 5. 解析外部服务配置
    ServiceConfigInfo.EGIService = GetElement("EGIService");
    ServiceConfigInfo.EGIUserNo = GetElement("EGIUserNo");
    ServiceConfigInfo.EGIPsw = GetElement("EGIPsw");
    
    // 6. 初始化REST客户端
    RestClientPoxy.BaseUri = ServiceConfigInfo.EGIService;
    RestClient.EquipmentType = "EPD";
    
    // 7. 设置默认会话信息
    RestClientPoxy.SetDefaultSessionId(Guid.NewGuid().ToString());
    RestClientPoxy.SetDefaultJSessionId(Guid.NewGuid().ToString());
}
```

##### GetTaskCadCodeList方法 - 获取任务命令列表
```csharp
public List<EpdCADCommandDto> GetTaskCadCodeList()
{
    // 伪代码:
    if (!IsWCFConnect) {
        SentLogErrorMessage("IsWCFConnect=false");
        return null;
    }
    
    try {
        // 1. 获取新的CAD任务信息
        List<EpdCADCommandDto> task = GetNewCADTaskInfo();
        
        if (task != null && taskInfo != null) {
            // 2. 查询任务详细信息
            var EpdtaskinfoDto = CADDbConnect.QueryEpdTaskInfoWithParameterByTaskId(
                taskInfo.EpdTaskId.ToString(),
                TemplateRunTaskType.IsTest(taskType) ? "Test" : "Formal");
            
            // 3. 检查任务失败次数
            SendLogMessage(taskInfo.EpdTaskId, 
                LanguageHelper.GetString("TaskFailCount") + EpdtaskinfoDto.EpdTaskInfoDto.cadRunCount, -1);
            
            if (EpdtaskinfoDto.EpdTaskInfoDto.cadRunCount > ServiceConfigInfo.ErrRerunCount) {
                SendLogMessage(taskInfo.EpdTaskId, 
                    LanguageHelper.GetString("TaskFailCountOver4"), -1);
                SetModelError();
                return null;
            }
            
            // 4. 处理租户路径
            ProcessTenantPath();
            
            return task;
        } else {
            WriteCmd("[Done!]");
            return null;
        }
    }
    catch (CommunicationException ex) {
        HandleCommunicationError(ex);
        return null;
    }
    catch (Exception ex) {
        SentLogErrorMessage(ex.ToString());
        WriteCmd("[Done!]");
        return null;
    }
}
```

##### SendLogMessage方法 - 日志记录
```csharp
public void SendLogMessage(Guid taskId, string message, int messageType)
{
    // 伪代码:
    // 1. 格式化日志信息
    string info;
    switch (messageType) {
        case 1:
            info = string.Format("{0} !    {1}", DateTime.Now, message);
            break;
        case 2:
            info = string.Format("{0} !!   {1}", DateTime.Now, message);
            break;
        default:
            info = string.Format("{0} {1}", DateTime.Now, message);
            break;
    }
    
    // 2. 显示在界面上
    if (InvokeRequired) {
        Invoke(new Action(() => {
            listBox1.Items.Add(info);
            listBox1.SelectedIndex = listBox1.Items.Count - 1;
        }));
    } else {
        listBox1.Items.Add(info);
        listBox1.SelectedIndex = listBox1.Items.Count - 1;
    }
    
    // 3. 写入日志文件
    string logPath = string.Format("{0}\\CadTaskLog\\{2}\\CadTaskLog_{1}.txt", 
        basePath, taskId, RestClientPoxy.Session?.OrgnizationNum);
    File.AppendAllText(logPath, info + Environment.NewLine);
    
    // 4. 触发日志事件
    OnLogHandler(info + Environment.NewLine);
    Application.DoEvents();
}
```

##### UploadResult方法 - 成果物上传
```csharp
public void UploadResult(string attachmentPath, Guid taskId)
{
    // 伪代码:
    try {
        // 1. 检查附件路径
        if (string.IsNullOrEmpty(attachmentPath)) {
            SendLogMessage(taskId, LanguageHelper.GetString("AttachmentPathEmpty"), 2);
            return;
        }
        
        // 2. 创建新的路径
        string newPath = Path.Combine(ServiceConfigInfo.WorkPath, 
            Path.GetFileName(attachmentPath));
        
        if (Directory.Exists(attachmentPath)) {
            // 3. 复制目录到新路径
            CopyDirectory(attachmentPath, newPath);
        }
        
        // 4. 打包压缩
        SendLogMessage(taskId, LanguageHelper.GetString("PackageUploadResults"), 0);
        byte[] buf = ServiceConfigInfo.ZipMode.HasValue && ServiceConfigInfo.ZipMode.Value == 1 
            ? ZipHelper1.ZipDirectoryToBuffer(newPath) 
            : ZipHelper.ZipDirectoryToBuffer(newPath);
        
        if (buf != null) {
            // 5. 上传到服务器
            var id = DBServiceClient.Instance.EGIPlatformService.UploadFile(
                buf, Path.GetFileName(attachmentPath), "EpdCadResult");
            taskInfo.CADAttachmentPath = id.ToString();
            
            SendLogMessage(taskId, LanguageHelper.GetString("UploadSuccess"), 1);
        }
        
        // 6. 清理临时文件
        if (Directory.Exists(newPath)) {
            Directory.Delete(newPath, true);
        }
    }
    catch (Exception ex) {
        SendLogMessage(taskId, LanguageHelper.GetString("UploadFailed") + ex.ToString(), 2);
    }
}
```

## 4. 时序图

### 4.1 任务获取和执行时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Form as FormCADTaskServer
    participant Core as CADTaskServer
    participant WCF as CADWCFServer
    participant DB as DBServiceClient
    participant Platform as EGI平台服务

    User->>Form: 启动系统
    Form->>Form: GetServiceConfigInfo()
    Form->>DB: 初始化连接
    Form->>Core: Load()
    Core->>Form: 创建窗体实例
    Core->>Core: 启动定时器
    
    Note over Core: 定时器触发
    Core->>Core: GetTaskTimer_Tick()
    Core->>Form: GetTaskCadCodeList()
    Form->>Platform: GetNewCADTaskInfo()
    Platform-->>Form: 返回任务列表
    Form->>Platform: QueryEpdTaskInfoWithParameterByTaskId()
    Platform-->>Form: 返回任务详情
    Form-->>Core: 返回CAD命令列表
    
    Core->>Core: GetCacheFiles()
    Core->>Form: GetDocoumentById()
    Form->>DB: findDocumentFileList()
    DB-->>Form: 返回缓存文件
    Form-->>Core: 返回缓存结果
    
    Core->>WCF: RunCADCode()
    WCF->>Core: 执行CAD命令
    Core-->>WCF: 返回执行结果
    
    Core->>Form: UploadResult()
    Form->>Form: 打包压缩结果
    Form->>Platform: UploadFile()
    Platform-->>Form: 返回文件ID
    
    Core->>Form: SaveCadLogFile()
    Form->>Platform: SaveEpdTaskInfoEntities()
    Platform-->>Form: 保存成功
    
    Core->>Core: SetTaskRunEnd()
```

### 4.2 配置文件读取时序图

```mermaid
sequenceDiagram
    participant Form as FormCADTaskServer
    participant Config as 配置文件
    participant Rest as RestClientPoxy
    participant Lang as LanguageHelper

    Form->>Form: 构造函数调用
    Form->>Form: GetServiceConfigInfo()
    Form->>Config: 读取CADTaskServerConfig.xml
    Config-->>Form: 返回服务器配置内容
    Form->>Config: 读取CADTaskClientConfig.xml
    Config-->>Form: 返回客户端配置内容
    
    loop 解析每个配置项
        Form->>Form: GetElement(elementName)
        Form->>Form: 正则表达式匹配
        Form->>Form: 设置ServiceConfigInfo属性
    end
    
    Form->>Rest: 设置BaseUri
    Form->>Rest: 设置EquipmentType
    Form->>Rest: 设置默认会话信息
    
    Form->>Lang: LoadLanguage()
    Lang->>Lang: 加载语言包
```

## 5. 工具类详细设计

### 5.1 ZipHelper 类

#### 5.1.1 类说明
ZipHelper提供文件和目录的压缩解压功能，支持多种压缩格式和操作模式。

#### 5.1.2 核心方法

##### ZipDirectory方法 - 目录压缩
```csharp
public static string ZipDirectory(string folderToZip)
{
    // 伪代码:
    var fileName = folderToZip + ".zip";
    
    using (ZipFile zip = ZipFile.Create(fileName)) {
        zip.BeginUpdate();
        
        // 1. 获取所有文件
        var files = Directory.GetFiles(folderToZip, "*.*", SearchOption.AllDirectories);
        
        // 2. 添加每个文件到压缩包
        foreach (var file in files) {
            var entryName = file.Substring(folderToZip.Length + 1);
            var zipEntry = new ZipEntry(entryName);
            zip.Add(file, entryName);
        }
        
        zip.CommitUpdate();
    }
    
    return fileName;
}
```

##### UnZip方法 - 解压缩
```csharp
public static void UnZip(byte[] zipFileContent, string TargetDirectory, 
    Action<string> logHandler, string Password = null)
{
    // 伪代码:
    if (!Directory.Exists(TargetDirectory)) {
        logHandler("指定的目录不存在: " + TargetDirectory);
        return;
    }
    
    using (ZipInputStream stream = new ZipInputStream(new MemoryStream(zipFileContent))) {
        stream.Password = Password;
        
        ZipEntry entry;
        while ((entry = stream.GetNextEntry()) != null) {
            // 1. 获取文件路径和名称
            string path = entry.Name;
            string fileName = Path.GetFileName(path);
            string dirName = Path.GetDirectoryName(path);
            
            // 2. 创建目录
            if (!string.IsNullOrEmpty(dirName)) {
                string fullDirPath = Path.Combine(TargetDirectory, dirName);
                if (!Directory.Exists(fullDirPath)) {
                    Directory.CreateDirectory(fullDirPath);
                }
            }
            
            // 3. 解压文件
            if (!string.IsNullOrEmpty(fileName)) {
                string fullFilePath = Path.Combine(TargetDirectory, path);
                using (FileStream output = File.Create(fullFilePath)) {
                    StreamUtils.Copy(stream, output, new byte[4096]);
                }
                logHandler($"解压文件: {fileName}");
            }
        }
    }
}
```

### 5.2 SingleQuoteStringParser 类

#### 5.2.1 类说明
SingleQuoteStringParser提供字符串解析功能，支持单引号分隔的字符串列表和键值对的解析。

#### 5.2.2 核心方法

##### Unpack方法 - 字符串解包
```csharp
public static bool Unpack(string text, out List<string> list)
{
    // 伪代码:
    list = new List<string>();
    
    if (string.IsNullOrEmpty(text)) {
        return true;
    }
    
    // 状态机解析
    int state = 0;  // 0:初始, 1:引号内, 2:引号结束, 3:普通字符
    StringBuilder elemCache = new StringBuilder();
    
    foreach (char ch in text) {
        switch (ch) {
            case '\'':
                state = TransitionOnQuote(state);
                break;
            case ',':
                state = TransitionOnComma(state);
                if (state == 0) {
                    list.Add(elemCache.ToString());
                    elemCache.Clear();
                }
                break;
            default:
                state = TransitionOnOther(state);
                break;
        }
        
        if (state == 3) {
            elemCache.Append(ch);
        } else if (state < 0) {
            return false;  // 解析错误
        }
    }
    
    // 处理最后一个元素
    if (state == 2 || state == 0) {
        list.Add(elemCache.ToString());
        return true;
    }
    
    return false;
}
```

### 5.3 ShareMemoryHelper 类

#### 5.3.1 类说明
ShareMemoryHelper提供共享内存操作功能，用于进程间通信。

#### 5.3.2 核心方法

##### CreateShareMemoryMap方法 - 创建共享内存
```csharp
public int CreateShareMemoryMap(string strName, long lngSize)
{
    // 伪代码:
    if (lngSize <= 0 || lngSize > 0x00800000) {
        lngSize = 0x00800000;  // 限制最大8MB
    }
    
    if (string.IsNullOrEmpty(strName)) {
        return 1;  // 名称不能为空
    }
    
    // 1. 创建文件映射
    fileMapping = CreateFileMapping(INVALID_HANDLE_VALUE, IntPtr.Zero, 
        PAGE_READWRITE, 0, (uint)lngSize, strName);
    
    if (fileMapping == IntPtr.Zero) {
        return 2;  // 创建失败
    }
    
    // 2. 检查是否已存在
    if (GetLastError() == ERROR_ALREADY_EXISTS) {
        m_bAlreadyExist = true;
    }
    
    // 3. 映射视图
    mapView = MapViewOfFile(fileMapping, FILE_MAP_WRITE, 0, 0, (uint)lngSize);
    
    if (mapView == IntPtr.Zero) {
        CloseHandle(fileMapping);
        return 3;  // 映射失败
    }
    
    m_bInit = true;
    m_MemSize = lngSize;
    return 0;  // 成功
}
```

##### Write方法 - 写入数据
```csharp
public int Write(byte[] bytData, int lngAddr, int lngSize)
{
    // 伪代码:
    if (lngAddr + lngSize > m_MemSize) {
        return 2;  // 超出内存范围
    }
    
    if (!m_bInit) {
        return 1;  // 未初始化
    }
    
    // 复制数据到共享内存
    Marshal.Copy(bytData, lngAddr, mapView, lngSize);
    return 0;  // 成功
}
```

## 6. 数据流程图

### 6.1 任务处理数据流

```mermaid
flowchart TD
    A[启动系统] --> B[读取配置文件]
    B --> C[初始化WCF连接]
    C --> D[启动定时器]
    D --> E[定时获取任务]
    E --> F{有新任务?}
    F -->|是| G[检查缓存]
    F -->|否| E
    G --> H{缓存命中?}
    H -->|是| I[使用缓存文件]
    H -->|否| J[下载模型文件]
    I --> K[执行CAD命令]
    J --> K
    K --> L[处理执行结果]
    L --> M[打包上传结果]
    M --> N[更新任务状态]
    N --> O[记录日志]
    O --> P[任务完成]
    P --> E
```

### 6.2 配置管理数据流

```mermaid
flowchart TD
    A[系统启动] --> B[定位配置文件]
    B --> C[读取XML内容]
    C --> D[正则表达式解析]
    D --> E[类型转换]
    E --> F[设置配置对象]
    F --> G[初始化外部服务]
    G --> H[验证配置有效性]
    H --> I{配置有效?}
    I -->|是| J[配置加载完成]
    I -->|否| K[显示错误信息]
    K --> L[退出系统]
    J --> M[系统正常运行]
```

## 7. 异常处理设计

### 7.1 异常分类
- **配置异常**: 配置文件读取失败、格式错误
- **网络异常**: WCF连接失败、平台服务不可用
- **文件异常**: 文件读写失败、路径不存在
- **业务异常**: 任务执行失败、数据验证失败

### 7.2 异常处理策略
```csharp
// 全局异常处理模式
try {
    // 业务逻辑
    ExecuteBusinessLogic();
}
catch (CommunicationException ex) {
    // 通信异常 - 记录日志，尝试重连
    HandleCommunicationError(ex);
}
catch (FileNotFoundException ex) {
    // 文件异常 - 记录日志，提示用户
    HandleFileError(ex);
}
catch (Exception ex) {
    // 通用异常 - 记录详细日志
    HandleGeneralError(ex);
}
finally {
    // 清理资源
    CleanupResources();
}
```

## 8. 性能优化设计

### 8.1 缓存策略
- **文件缓存**: 基于MD5的模型文件缓存
- **配置缓存**: 内存中缓存配置信息
- **连接池**: 复用数据库连接

### 8.2 异步处理
- **定时器**: 非阻塞的任务调度
- **文件操作**: 异步文件读写
- **网络调用**: 异步WCF调用

## 9. 扩展点设计

### 9.1 事件驱动扩展
```csharp
// 定义扩展事件
public event CADEventHander GetCADTask;
public event RunCADCodeEventHander RunCADCodeEvent;
public event Func<string, string, List<string>> GetRefconfigListEvent;

// 扩展点调用
if (GetCADTask != null) {
    var args = new CADArgs { IsOpenRootModel = false };
    GetCADTask(args);
}
```

### 9.2 插件接口设计
```csharp
// 插件接口定义
public interface ICADPlugin
{
    string Name { get; }
    string Version { get; }
    bool Initialize(ICADContext context);
    CADTaskCode ProcessCommand(CADTaskCode command);
    void Cleanup();
}
```

## 10. 窗体类详细设计

### 10.1 FormParameterSet 类 - 参数设置窗体

#### 10.1.1 类说明
FormParameterSet负责系统参数的配置和设置，提供用户友好的配置界面。

#### 10.1.2 属性定义
```csharp
public partial class FormParameterSet : Form
{
    private string _StationType;  // 工作站类型
    private XmlDocument configDoc;  // 配置文档

    // UI控件
    private TextBox textBox_PdsPath;     // PDS路径
    private TextBox textBox_WorkPath;    // 工作路径
    private TextBox textBox_CachePath;   // 缓存路径
    private TextBox textBox_BakPath;     // 备份路径
}
```

#### 10.1.3 核心方法
```csharp
private void FormParameterSet_Load(object sender, EventArgs e)
{
    // 伪代码:
    // 1. 加载配置文件
    string xmlpath = Path.Combine(Application.StartupPath, "CADTaskClientConfig.xml");
    configDoc = new XmlDocument();
    configDoc.LoadXml(File.ReadAllText(xmlpath, Encoding.UTF8));

    // 2. 读取工作站类型
    _StationType = LoadXmlNode("StationType", configDoc);

    // 3. 根据类型设置控件状态
    CheckSetUpContro();

    // 4. 加载路径配置
    textBox_PdsPath.Text = LoadXmlNode("PdsPath", configDoc);
    textBox_WorkPath.Text = LoadXmlNode("WorkPath", configDoc);
    textBox_CachePath.Text = LoadXmlNode("CachePath", configDoc);
    textBox_BakPath.Text = LoadXmlNode("BakPath", configDoc);
}
```

### 10.2 FormTaskInfoSelect 类 - 任务选择窗体

#### 10.2.1 类说明
FormTaskInfoSelect提供任务选择和查看功能，允许用户手动选择要执行的任务。

#### 10.2.2 属性定义
```csharp
public partial class FormTaskInfoSelect : Form
{
    public EpdTaskInfoDto SelectedTaskInfo { get; private set; }
    private List<EpdTaskInfoDto> taskList;
    private DataGridView dataGridViewTasks;
}
```

### 10.3 FormUserLogin 类 - 用户登录窗体

#### 10.3.1 类说明
FormUserLogin处理用户身份验证，支持多种登录方式。

#### 10.3.2 核心方法
```csharp
private void button1_Click(object sender, EventArgs e)
{
    // 伪代码:
    try {
        // 1. 清除之前的会话信息
        RestClientPoxy.SetDefaultSessionId(null);
        RestClientPoxy.SetDefaultJSessionId(null);
        RestClientPoxy.SetDefaultJwtAuth(null);

        // 2. 根据配置选择登录方式
        if (string.IsNullOrEmpty(LoginUrl)) {
            DBServiceClient.Instance.EGIRbacServiceClient.Login(textBox1.Text, textBox2.Text);
        } else {
            DBServiceClient.Instance.EGIRbacServiceClient.Login1(textBox1.Text, textBox2.Text, LoginUrl);
        }

        // 3. 登录成功
        DialogResult = DialogResult.OK;
        Close();
    }
    catch (Exception exception) {
        MessageBox.Show(LanguageHelper.GetString("LoginFailed") + exception.ToString());
    }
}
```

## 11. 数据传输对象 (DTO) 设计

### 11.1 EpdCADCommandDto 类
```csharp
public class EpdCADCommandDto
{
    public Guid TaskId { get; set; }
    public int SortId { get; set; }
    public int PartOrderId { get; set; }
    public int CommandType { get; set; }
    public string Para1 { get; set; }
    public string Para2 { get; set; }
    public string Para3 { get; set; }
    // ... Para4 到 Para20
    public Guid? PartUniqId { get; set; }
    public Guid? DocumentId { get; set; }
    public string ModelMd5 { get; set; }
    public bool? UseCache { get; set; }
}
```

### 11.2 EpdTaskInfoDto 类
```csharp
public class EpdTaskInfoDto
{
    public Guid EpdTaskId { get; set; }
    public string Name { get; set; }
    public string CADAttachmentPath { get; set; }
    public DateTime RunTimePoint { get; set; }
    public int TaskType { get; set; }
    public string CreateUser { get; set; }
    public int cadRunCount { get; set; }
    public bool? Failed { get; set; }
    public string CadWsIp { get; set; }
    public Guid? CADRunLogId { get; set; }
}
```

### 11.3 DocumentInfoDto 类
```csharp
public class DocumentInfoDto
{
    public string DocType { get; set; }
    public string DocExt1 { get; set; }
    public string RefExt1 { get; set; }
    public string RefExt2 { get; set; }
    public string RefExt3 { get; set; }
    public DocumentFileOutputDto DocumentFileOutputDto { get; set; }
}
```

## 12. 配置管理详细设计

### 12.1 WCFServiceConfigInfo 类
```csharp
public class WCFServiceConfigInfo
{
    // WCF服务配置
    public string WCFServicePath { get; set; }
    public string EndpointUrl { get; set; }
    public string MaxArrayLength { get; set; }
    public string MaxDepth { get; set; }
    public string MaxBytesPerRead { get; set; }
    public string MaxNameTableCharCount { get; set; }
    public string MaxStringContentLength { get; set; }
    public long MaxReceivedMessageSize { get; set; }

    // 路径配置
    public string ExpendViewName { get; set; }
    public string PdsPath { get; set; }
    public string WorkPath { get; set; }
    public string CachePath { get; set; }
    public string BakPath { get; set; }
    public string DxfPath { get; set; }
    public string DxfCopyPath { get; set; }
    public string EDrawingPath { get; set; }
    public string PocResultPath { get; set; }

    // 功能开关
    public bool bMakeDRW { get; set; }
    public bool bMakeDXF { get; set; }
    public bool bMakeDWG { get; set; }
    public bool bMakePDF { get; set; }
    public bool bMakeEDrawing { get; set; }
    public bool UseModelCache { get; set; }
    public bool UseDocumentDbTemplate { get; set; }
    public bool UseDocumentDbResult { get; set; }
    public bool KeepOpenLastDoc { get; set; }
    public bool AutoAlignDimension { get; set; }
    public bool InterFerenceDetection { get; set; }
    public bool DeleteNoStandardParam { get; set; }

    // 外部服务配置
    public string EGIService { get; set; }
    public string LoginUrl { get; set; }
    public string EGIUserNo { get; set; }
    public string EGIPsw { get; set; }
    public string WCFCADServer { get; set; }
    public string WCFEdsServer { get; set; }
    public string SendMailServer { get; set; }

    // 其他配置
    public string Language { get; set; }
    public string MaxLogFileSize { get; set; }
    public int? ZipMode { get; set; }
    public string CadWsRemark { get; set; }
    public int ErrRerunCount { get; set; }
    public string CurrentIp { get; set; }
    public string DefaultOrgNum { get; set; }
    public int Status400RerunAfterMinutes { get; set; }
}
```

### 12.2 配置文件结构示例
```xml
<!-- CADTaskServerConfig.xml -->
<Configuration>
    <WCFServicePath>net.tcp://localhost:8001/CADTaskServer</WCFServicePath>
    <EndpointUrl>http://localhost:8080/EGIService</EndpointUrl>
    <PdsPath>D:\PDS\Templates</PdsPath>
    <WorkPath>D:\CAD\Work</WorkPath>
    <CachePath>D:\CAD\Cache</CachePath>
    <BakPath>D:\CAD\Backup</BakPath>
    <MakeProjectMap>true</MakeProjectMap>
    <MakeSpreadMap>true</MakeSpreadMap>
    <UseModelCache>true</UseModelCache>
    <EGIService>http://platform.example.com/api</EGIService>
    <EGIUserNo>admin</EGIUserNo>
    <EGIPsw>password</EGIPsw>
    <Language>zh-CN</Language>
    <MaxLogFileSize>5</MaxLogFileSize>
    <ErrRerunCount>4</ErrRerunCount>
</Configuration>
```

## 13. 错误码和状态定义

### 13.1 FinishStatus 枚举
```csharp
public enum FinishStatus
{
    NotStarted = 0,    // 未开始
    Running = 1,       // 运行中
    Finished = 2,      // 已完成
    Failed = 3,        // 失败
    Cancelled = 4      // 已取消
}
```

### 13.2 TemplateRunTaskType 枚举
```csharp
public enum TemplateRunTaskType
{
    Normal = 1,        // 普通任务
    Test = 2,          // 测试任务
    DWGToPDF = 3,      // DWG转PDF
    Batch = 4          // 批处理任务
}
```

### 13.3 CadItemType 枚举
```csharp
public enum CadItemType
{
    featureItem = 1,      // 特征项
    configItem = 2,       // 配置项
    asmCompModelItem = 3, // 装配组件模型项
    dimensionItem = 4,    // 尺寸项
    propertyItem = 5      // 属性项
}
```

## 14. 国际化支持设计

### 14.1 LanguageHelper 类
```csharp
public static class LanguageHelper
{
    private static Dictionary<string, string> languageResources;

    public static void LoadLanguage(string languageCode)
    {
        // 伪代码:
        string resourceFile = $"Resources.{languageCode}.resx";
        languageResources = LoadResourceFile(resourceFile);
    }

    public static string GetString(string key)
    {
        // 伪代码:
        if (languageResources != null && languageResources.ContainsKey(key)) {
            return languageResources[key];
        }
        return key; // 返回键名作为默认值
    }
}
```

### 14.2 支持的语言
- 中文 (zh-CN)
- 英文 (en-US)
- 日文 (ja-JP)

## 15. 线程安全设计

### 15.1 线程安全策略
```csharp
// 使用锁保护共享资源
private readonly object lockObject = new object();

public void ThreadSafeMethod()
{
    lock (lockObject) {
        // 访问共享资源
        AccessSharedResource();
    }
}

// 使用Invoke确保UI线程安全
private void UpdateUI(string message)
{
    if (InvokeRequired) {
        Invoke(new Action<string>(UpdateUI), message);
        return;
    }

    // 在UI线程中执行
    listBox1.Items.Add(message);
}
```

### 15.2 异步操作模式
```csharp
// 异步文件操作
public async Task<byte[]> ReadFileAsync(string filePath)
{
    using (FileStream stream = new FileStream(filePath, FileMode.Open, FileAccess.Read)) {
        byte[] buffer = new byte[stream.Length];
        await stream.ReadAsync(buffer, 0, buffer.Length);
        return buffer;
    }
}

// 异步网络调用
public async Task<List<EpdCADCommandDto>> GetTaskListAsync()
{
    return await Task.Run(() => {
        return CADDbConnect.GetTaskCadCodeList(taskInfo.EpdTaskId);
    });
}
```

这个详细设计文档提供了系统的完整技术实现细节，包括类图、时序图、核心方法的伪代码实现、配置管理、错误处理、性能优化、国际化支持和线程安全等方面的设计，为开发人员提供了全面的技术指导。

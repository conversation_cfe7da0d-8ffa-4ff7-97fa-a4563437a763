﻿namespace Zxtech.CADTaskServer
{
 public   partial class FormCADTaskServer
    {

        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormCADTaskServer));
            this.rtbRunInfo = new System.Windows.Forms.RichTextBox();
            this.statusStrip1 = new System.Windows.Forms.StatusStrip();
            this.toolStripStatusLabelUserName = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripUserName = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLableWorkStation = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripWorkStation = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabelTaskType = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripTaskType = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripStatusLabel1 = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStripServerConnect = new System.Windows.Forms.ToolStripStatusLabel();
            this.toolStrip1 = new System.Windows.Forms.ToolStrip();
            this.btnStart = new System.Windows.Forms.ToolStripButton();
            this.btnStop = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.btnExit = new System.Windows.Forms.ToolStripButton();
            this.toolStripButtonGetTask = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripButtonGetTaskSelect = new System.Windows.Forms.ToolStripButton();
            this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
            this.toolStripButtonPriority = new System.Windows.Forms.ToolStripButton();
            this.toolStripButton1 = new System.Windows.Forms.ToolStripButton();
            this.labelInfo = new System.Windows.Forms.Label();
            this.statusStrip1.SuspendLayout();
            this.toolStrip1.SuspendLayout();
            this.SuspendLayout();
            // 
            // rtbRunInfo
            // 
            resources.ApplyResources(this.rtbRunInfo, "rtbRunInfo");
            this.rtbRunInfo.BackColor = System.Drawing.SystemColors.Window;
            this.rtbRunInfo.Name = "rtbRunInfo";
            this.rtbRunInfo.ReadOnly = true;
            this.rtbRunInfo.TextChanged += new System.EventHandler(this.rtbRunInfo_TextChanged);
            // 
            // statusStrip1
            // 
            resources.ApplyResources(this.statusStrip1, "statusStrip1");
            this.statusStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripStatusLabelUserName,
            this.toolStripUserName,
            this.toolStripStatusLableWorkStation,
            this.toolStripWorkStation,
            this.toolStripStatusLabelTaskType,
            this.toolStripTaskType,
            this.toolStripStatusLabel1,
            this.toolStripServerConnect});
            this.statusStrip1.Name = "statusStrip1";
            // 
            // toolStripStatusLabelUserName
            // 
            resources.ApplyResources(this.toolStripStatusLabelUserName, "toolStripStatusLabelUserName");
            this.toolStripStatusLabelUserName.Name = "toolStripStatusLabelUserName";
            // 
            // toolStripUserName
            // 
            resources.ApplyResources(this.toolStripUserName, "toolStripUserName");
            this.toolStripUserName.Name = "toolStripUserName";
            // 
            // toolStripStatusLableWorkStation
            // 
            resources.ApplyResources(this.toolStripStatusLableWorkStation, "toolStripStatusLableWorkStation");
            this.toolStripStatusLableWorkStation.Name = "toolStripStatusLableWorkStation";
            // 
            // toolStripWorkStation
            // 
            resources.ApplyResources(this.toolStripWorkStation, "toolStripWorkStation");
            this.toolStripWorkStation.Name = "toolStripWorkStation";
            this.toolStripWorkStation.Spring = true;
            // 
            // toolStripStatusLabelTaskType
            // 
            resources.ApplyResources(this.toolStripStatusLabelTaskType, "toolStripStatusLabelTaskType");
            this.toolStripStatusLabelTaskType.Name = "toolStripStatusLabelTaskType";
            // 
            // toolStripTaskType
            // 
            resources.ApplyResources(this.toolStripTaskType, "toolStripTaskType");
            this.toolStripTaskType.Name = "toolStripTaskType";
            this.toolStripTaskType.Spring = true;
            this.toolStripTaskType.Click += new System.EventHandler(this.toolStripTaskType_Click);
            // 
            // toolStripStatusLabel1
            // 
            resources.ApplyResources(this.toolStripStatusLabel1, "toolStripStatusLabel1");
            this.toolStripStatusLabel1.Name = "toolStripStatusLabel1";
            // 
            // toolStripServerConnect
            // 
            resources.ApplyResources(this.toolStripServerConnect, "toolStripServerConnect");
            this.toolStripServerConnect.Name = "toolStripServerConnect";
            // 
            // toolStrip1
            // 
            resources.ApplyResources(this.toolStrip1, "toolStrip1");
            this.toolStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.btnStart,
            this.btnStop,
            this.toolStripSeparator1,
            this.btnExit,
            this.toolStripButtonGetTask,
            this.toolStripSeparator2,
            this.toolStripSeparator3,
            this.toolStripButtonGetTaskSelect,
            this.toolStripSeparator4,
            this.toolStripButtonPriority,
            this.toolStripButton1});
            this.toolStrip1.Name = "toolStrip1";
            this.toolStrip1.RenderMode = System.Windows.Forms.ToolStripRenderMode.System;
            this.toolStrip1.ItemClicked += new System.Windows.Forms.ToolStripItemClickedEventHandler(this.toolStrip1_ItemClicked);
            // 
            // btnStart
            // 
            resources.ApplyResources(this.btnStart, "btnStart");
            this.btnStart.Image = global::Zxtech.CADTaskServer.Properties.Resources.AutoRunBtn2;
            this.btnStart.Name = "btnStart";
            this.btnStart.Click += new System.EventHandler(this.btnStart_Click);
            // 
            // btnStop
            // 
            resources.ApplyResources(this.btnStop, "btnStop");
            this.btnStop.Image = global::Zxtech.CADTaskServer.Properties.Resources.AutoQuitBtn1;
            this.btnStop.Name = "btnStop";
            this.btnStop.Click += new System.EventHandler(this.btnStop_Click);
            // 
            // toolStripSeparator1
            // 
            resources.ApplyResources(this.toolStripSeparator1, "toolStripSeparator1");
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            // 
            // btnExit
            // 
            resources.ApplyResources(this.btnExit, "btnExit");
            this.btnExit.Alignment = System.Windows.Forms.ToolStripItemAlignment.Right;
            this.btnExit.Name = "btnExit";
            this.btnExit.Click += new System.EventHandler(this.btnExit_Click);
            // 
            // toolStripButtonGetTask
            // 
            resources.ApplyResources(this.toolStripButtonGetTask, "toolStripButtonGetTask");
            this.toolStripButtonGetTask.Name = "toolStripButtonGetTask";
            this.toolStripButtonGetTask.Click += new System.EventHandler(this.toolStripButtonGetTask_Click);
            // 
            // toolStripSeparator2
            // 
            resources.ApplyResources(this.toolStripSeparator2, "toolStripSeparator2");
            this.toolStripSeparator2.Alignment = System.Windows.Forms.ToolStripItemAlignment.Right;
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            // 
            // toolStripSeparator3
            // 
            resources.ApplyResources(this.toolStripSeparator3, "toolStripSeparator3");
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            // 
            // toolStripButtonGetTaskSelect
            // 
            resources.ApplyResources(this.toolStripButtonGetTaskSelect, "toolStripButtonGetTaskSelect");
            this.toolStripButtonGetTaskSelect.Name = "toolStripButtonGetTaskSelect";
            this.toolStripButtonGetTaskSelect.Click += new System.EventHandler(this.toolStripButtonGetTaskSelect_Click);
            // 
            // toolStripSeparator4
            // 
            resources.ApplyResources(this.toolStripSeparator4, "toolStripSeparator4");
            this.toolStripSeparator4.Name = "toolStripSeparator4";
            // 
            // toolStripButtonPriority
            // 
            resources.ApplyResources(this.toolStripButtonPriority, "toolStripButtonPriority");
            this.toolStripButtonPriority.DisplayStyle = System.Windows.Forms.ToolStripItemDisplayStyle.Text;
            this.toolStripButtonPriority.Name = "toolStripButtonPriority";
            // 
            // toolStripButton1
            // 
            resources.ApplyResources(this.toolStripButton1, "toolStripButton1");
            this.toolStripButton1.Image = global::Zxtech.CADTaskServer.Properties.Resources.SetupBtn2;
            this.toolStripButton1.Name = "toolStripButton1";
            this.toolStripButton1.Click += new System.EventHandler(this.toolStripButton1_Click);
            // 
            // labelInfo
            // 
            resources.ApplyResources(this.labelInfo, "labelInfo");
            this.labelInfo.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.labelInfo.Name = "labelInfo";
            // 
            // FormCADTaskServer
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.labelInfo);
            this.Controls.Add(this.toolStrip1);
            this.Controls.Add(this.statusStrip1);
            this.Controls.Add(this.rtbRunInfo);
            this.Name = "FormCADTaskServer";
            this.ShowIcon = false;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormCADTaskServer_FormClosing);
            this.Load += new System.EventHandler(this.FormCADTaskServer_Load);
            this.Shown += new System.EventHandler(this.FormCADTaskServer_Shown);
            this.statusStrip1.ResumeLayout(false);
            this.statusStrip1.PerformLayout();
            this.toolStrip1.ResumeLayout(false);
            this.toolStrip1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.RichTextBox rtbRunInfo;
        private System.Windows.Forms.StatusStrip statusStrip1;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabelUserName;
        private System.Windows.Forms.ToolStripStatusLabel toolStripUserName;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLableWorkStation;
        private System.Windows.Forms.ToolStripStatusLabel toolStripWorkStation;
        private System.Windows.Forms.ToolStrip toolStrip1;
        private System.Windows.Forms.ToolStripButton btnStart;
        private System.Windows.Forms.ToolStripButton btnStop;
        private System.Windows.Forms.ToolStripButton btnExit;
        private System.Windows.Forms.Label labelInfo;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabelTaskType;
        private System.Windows.Forms.ToolStripStatusLabel toolStripTaskType;
        private System.Windows.Forms.ToolStripButton toolStripButtonGetTask;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripStatusLabel toolStripStatusLabel1;
        private System.Windows.Forms.ToolStripStatusLabel toolStripServerConnect;
        private System.Windows.Forms.ToolStripButton toolStripButtonGetTaskSelect;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator4;
        private System.Windows.Forms.ToolStripButton toolStripButtonPriority;
        private System.Windows.Forms.ToolStripButton toolStripButton1;
    }
}
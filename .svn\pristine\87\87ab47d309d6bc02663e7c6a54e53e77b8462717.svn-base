<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:tns="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" />
  <xs:complexType name="ArrayOfEdsPartSupplierOrgSO">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsPartSupplierOrgSO" nillable="true" type="tns:EdsPartSupplierOrgSO" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsPartSupplierOrgSO" nillable="true" type="tns:ArrayOfEdsPartSupplierOrgSO" />
  <xs:complexType name="EdsPartSupplierOrgSO">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q1="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q1:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="AutoId" type="xs:int" />
          <xs:element minOccurs="0" name="Branch" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PartGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="PartNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PartPCNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Price" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SupplierCategory" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="SupplierName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SupplierOrderId" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="SupplierType" nillable="true" type="xs:int" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsPartSupplierOrgSO" nillable="true" type="tns:EdsPartSupplierOrgSO" />
  <xs:complexType name="ArrayOfEdsFMOrgSO">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsFMOrgSO" nillable="true" type="tns:EdsFMOrgSO" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsFMOrgSO" nillable="true" type="tns:ArrayOfEdsFMOrgSO" />
  <xs:complexType name="EdsFMOrgSO">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q2="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q2:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="ChildDrawingNO" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Description" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DrawingNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MainPart" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PackBox" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Remark" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TaskGuid" type="ser:guid" />
          <xs:element minOccurs="0" name="ToCount" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ToPackNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TotalRange" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Volumescale" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsFMOrgSO" nillable="true" type="tns:EdsFMOrgSO" />
  <xs:complexType name="ArrayOfEdsDbProperty">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsDbProperty" nillable="true" type="tns:EdsDbProperty" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsDbProperty" nillable="true" type="tns:ArrayOfEdsDbProperty" />
  <xs:complexType name="EdsDbProperty">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q3="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q3:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="DbColName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Desc" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FieldLength" type="xs:int" />
          <xs:element minOccurs="0" name="IsCorrespondVariable" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="IsFixed" type="xs:int" />
          <xs:element minOccurs="0" name="MemberName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Note" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PropConfigOrder" type="xs:int" />
          <xs:element minOccurs="0" name="PropId" type="xs:int" />
          <xs:element minOccurs="0" name="PropName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PropType" type="xs:int" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsDbProperty" nillable="true" type="tns:EdsDbProperty" />
  <xs:complexType name="ArrayOfEdsDbPropertyWS">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsDbPropertyWS" nillable="true" type="tns:EdsDbPropertyWS" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsDbPropertyWS" nillable="true" type="tns:ArrayOfEdsDbPropertyWS" />
  <xs:complexType name="EdsDbPropertyWS">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q4="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q4:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="DbColName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Desc" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FieldLength" type="xs:int" />
          <xs:element minOccurs="0" name="IsCorrespondVariable" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="IsFixed" type="xs:int" />
          <xs:element minOccurs="0" name="MemberName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Note" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PropId" type="xs:int" />
          <xs:element minOccurs="0" name="PropName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PropType" type="xs:int" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsDbPropertyWS" nillable="true" type="tns:EdsDbPropertyWS" />
  <xs:complexType name="EdsCompleteTaskWS">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q5="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q5:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="BomRoot" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="GlobalParas" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="OtherParams" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RunLog" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="StandardFlag" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="Status" type="xs:int" />
          <xs:element minOccurs="0" name="TaskID" type="ser:guid" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsCompleteTaskWS" nillable="true" type="tns:EdsCompleteTaskWS" />
  <xs:complexType name="EdsPartOrg">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q6="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q6:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="ArchiveTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="ChangeDescription" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CombineReviewer" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ContractGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="CreateTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="Creator" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CreatorEmployeeCode" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DepTopo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DrwGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="EffectEnd" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="EffectStart" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="F2000" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2001" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2002" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="F2003" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2004" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2005" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2006" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2007" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2008" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2009" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2010" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2011" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2012" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="F2014" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2015" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2016" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2017" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2018" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2019" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2020" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2050" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2051" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2052" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2053" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2054" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2055" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2056" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2060" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2100" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2101" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2102" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2103" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2104" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2105" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2106" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2107" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2108" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2109" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2110" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2111" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2120" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2121" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2122" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2123" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2124" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2125" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2126" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2127" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2128" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2129" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2130" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2150" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2151" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2152" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2153" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2154" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2155" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2156" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2157" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2158" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2159" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2160" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2161" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2162" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2163" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2164" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2165" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2166" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2167" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2168" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2169" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2170" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2171" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2172" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2173" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2174" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2175" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IsChildPartNo" type="xs:boolean" />
          <xs:element minOccurs="0" name="OrderIdInDrwNo" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="PQCS" nillable="true" type="xs:boolean" />
          <xs:element minOccurs="0" name="ParentPathAfterCheckIn" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ParentPathBeforeCheckIn" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PartGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="State" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="TaskGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="UnStandardReason" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsPartOrg" nillable="true" type="tns:EdsPartOrg" />
  <xs:complexType name="EdsPartOrgSO">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q7="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q7:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="ArchiveTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="ChangeDescription" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CombineReviewer" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ContractGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="CreateTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="Creator" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CreatorEmployeeCode" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DepTopo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DetailListDes" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DetailListVersion" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DrawingRemark" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DrwGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="EffectEnd" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="EffectStart" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="F2000" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2001" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2002" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="F2003" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2004" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2005" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2006" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2007" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2008" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2009" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2010" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2011" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2012" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="F2014" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2015" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2016" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2017" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2018" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2019" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2020" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2050" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2051" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2052" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2053" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2054" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2055" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2056" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2060" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2100" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2101" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2102" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2103" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2104" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2105" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2106" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2107" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2108" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2109" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2110" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2111" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2120" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2121" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2122" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2123" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2124" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2125" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2126" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2127" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2128" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2129" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2130" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2150" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2151" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2152" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2153" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2154" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2155" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2156" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2157" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2158" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2159" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2160" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2161" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2162" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2163" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2164" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2165" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2166" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2167" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2168" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2169" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2170" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2171" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2172" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2173" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2174" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F2175" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IsChildPartNo" type="xs:boolean" />
          <xs:element minOccurs="0" name="OperationDes" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="OrderIdInDrwNo" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="Owner" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PQCS" nillable="true" type="xs:boolean" />
          <xs:element minOccurs="0" name="ParentPathAfterCheckIn" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ParentPathBeforeCheckIn" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PartGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="State" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="TaskGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="UnStandardReason" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="UpdateTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="Updater" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsPartOrgSO" nillable="true" type="tns:EdsPartOrgSO" />
  <xs:complexType name="ArrayOfVEdsDllFunctionData">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="VEdsDllFunctionData" nillable="true" type="tns:VEdsDllFunctionData" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfVEdsDllFunctionData" nillable="true" type="tns:ArrayOfVEdsDllFunctionData" />
  <xs:complexType name="VEdsDllFunctionData">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q8="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q8:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="ContractGuid" type="ser:guid" />
          <xs:element minOccurs="0" name="ContractNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IsCurVersion" type="xs:boolean" />
          <xs:element minOccurs="0" name="ParaFile" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ProductFamily" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ProjectNo" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="VEdsDllFunctionData" nillable="true" type="tns:VEdsDllFunctionData" />
  <xs:complexType name="ArrayOfEdsDrawingRefParam">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsDrawingRefParam" nillable="true" type="tns:EdsDrawingRefParam" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsDrawingRefParam" nillable="true" type="tns:ArrayOfEdsDrawingRefParam" />
  <xs:complexType name="EdsDrawingRefParam">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q9="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q9:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="ChildCop" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DrawingNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EffectEnd" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="EffectStart" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="FileType" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="IsPrefab" nillable="true" type="xs:boolean" />
          <xs:element minOccurs="0" name="PCNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ParamName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ParamUsageType" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="ParamValueRange" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsDrawingRefParam" nillable="true" type="tns:EdsDrawingRefParam" />
  <xs:complexType name="EdsEpdTaskInfo">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q10="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q10:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="Branch" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ContractNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ContractStatisticalTask" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CreateTime" type="xs:dateTime" />
          <xs:element minOccurs="0" name="Creator" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CurrentDoorFlag" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CurrentFloor" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CurrentFloorid" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="DrawingNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DrawingParams" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FileFlag" type="xs:int" />
          <xs:element minOccurs="0" name="FirstmlF" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FistUnitF" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FistUnitR" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FloorNumber" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="ID" type="ser:guid" />
          <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PCNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Parameters" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ParametricStatisticalTask" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PartNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PcList" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Priority" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="ProjectNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RunLevel" type="xs:int" />
          <xs:element minOccurs="0" name="RunTimePoint" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="TestFileTypes" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Testlevel" nillable="true" type="xs:int" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsEpdTaskInfo" nillable="true" type="tns:EdsEpdTaskInfo" />
  <xs:complexType name="ArrayOfEdsBackLogRegulaInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsBackLogRegulaInfo" nillable="true" type="tns:EdsBackLogRegulaInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsBackLogRegulaInfo" nillable="true" type="tns:ArrayOfEdsBackLogRegulaInfo" />
  <xs:complexType name="EdsBackLogRegulaInfo">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q11="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q11:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="ApproveRemark" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="CreatedTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="Creator" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Description" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EffectEnd" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="EffectStart" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="ExcelGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="IsUsedInAllContract" nillable="true" type="xs:boolean" />
          <xs:element minOccurs="0" name="LatestUpdateTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="RegulaName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RegulaNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RegulaType" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Remark" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="State" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsBackLogRegulaInfo" nillable="true" type="tns:EdsBackLogRegulaInfo" />
  <xs:complexType name="ArrayOfEdsNonstandardTaskItem">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsNonstandardTaskItem" nillable="true" type="tns:EdsNonstandardTaskItem" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsNonstandardTaskItem" nillable="true" type="tns:ArrayOfEdsNonstandardTaskItem" />
  <xs:complexType name="EdsNonstandardTaskItem">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q12="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q12:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="Code" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ContractGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="Desc" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IsEditPara" nillable="true" type="xs:boolean" />
          <xs:element minOccurs="0" name="IsFnd" nillable="true" type="xs:boolean" />
          <xs:element minOccurs="0" name="IsSpecialNonstandard" nillable="true" type="xs:boolean" />
          <xs:element minOccurs="0" name="ItemType" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="NonstandardCode" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsNonstandardTaskItem" nillable="true" type="tns:EdsNonstandardTaskItem" />
  <xs:complexType name="ArrayOfEdsTaskParaRef">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsTaskParaRef" nillable="true" type="tns:EdsTaskParaRef" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsTaskParaRef" nillable="true" type="tns:ArrayOfEdsTaskParaRef" />
  <xs:complexType name="EdsTaskParaRef">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q13="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q13:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="DrawingNOAndPCNO" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IsExist" type="xs:boolean" />
          <xs:element minOccurs="0" name="ParaName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ParaValue" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TaskID" type="ser:guid" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsTaskParaRef" nillable="true" type="tns:EdsTaskParaRef" />
  <xs:complexType name="ArrayOfEdsParameterSO">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsParameterSO" nillable="true" type="tns:EdsParameterSO" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsParameterSO" nillable="true" type="tns:ArrayOfEdsParameterSO" />
  <xs:complexType name="EdsParameterSO">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q14="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q14:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="ApplicationCase" type="xs:int" />
          <xs:element minOccurs="0" name="ChsDesc" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Comments" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CustomerType" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DataType" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DecimalDigits" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="Door" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ERP" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ERPCode" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ERPEffectFromDate" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="ERPPDM" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EffectiveDateInEDSSystem" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="EngDesc" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EquipmentType" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Global" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IsPrefab" type="xs:boolean" />
          <xs:element minOccurs="0" name="Level" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Loop" nillable="true" type="xs:boolean" />
          <xs:element minOccurs="0" name="MIT" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="NameAssistant" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="OperateType" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ParameterID" type="ser:guid" />
          <xs:element minOccurs="0" name="ProductAdmin" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TaskGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="UOM" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="UiOrderId" nillable="true" type="xs:int" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsParameterSO" nillable="true" type="tns:EdsParameterSO" />
  <xs:complexType name="EdsDrawingVersionSO">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q15="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q15:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="CODSType" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="CreateTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="Creator" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DepTopo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DrawingTypeCode" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="Eds_ObjectGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="EquipmentType" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F1000" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F1001" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F1002" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F1003" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F1004" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F1005" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F1008" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F1010" nillable="true" type="xs:base64Binary" />
          <xs:element minOccurs="0" name="FmNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IsHasChildPartNo" type="xs:boolean" />
          <xs:element minOccurs="0" name="IsPackageBody" type="xs:boolean" />
          <xs:element minOccurs="0" name="IsPrefab" nillable="true" type="xs:boolean" />
          <xs:element minOccurs="0" name="MaxUsedPostFix" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Operate" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="State" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="StdRemark" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TaskGuid" type="ser:guid" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsDrawingVersionSO" nillable="true" type="tns:EdsDrawingVersionSO" />
  <xs:complexType name="ArrayOfEDSMOFContractInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EDSMOFContractInfo" nillable="true" type="tns:EDSMOFContractInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEDSMOFContractInfo" nillable="true" type="tns:ArrayOfEDSMOFContractInfo" />
  <xs:complexType name="EDSMOFContractInfo">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q16="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q16:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="CARID" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CLCChanged" nillable="true" type="xs:boolean" />
          <xs:element minOccurs="0" name="CLCDiscountGranted" type="xs:int" />
          <xs:element minOccurs="0" name="CLCDiscountPercentage" type="xs:decimal" />
          <xs:element minOccurs="0" name="CLCExtraUnitNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CLCName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CLCPrice" type="xs:decimal" />
          <xs:element minOccurs="0" name="CalculatedCLCDiscountCost" type="xs:decimal" />
          <xs:element minOccurs="0" name="CarGroup" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ChangeOrderFlag" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ChangeOrderUnitNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ContractNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Currency" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DealStatus" type="xs:int" />
          <xs:element minOccurs="0" name="DevationInfo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DeviationCLCDiscount" type="xs:decimal" />
          <xs:element minOccurs="0" name="DeviationNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ExtraOrderLeadTime" type="xs:int" />
          <xs:element minOccurs="0" name="ExtraOrderUnitNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FieldSupervisorName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="FirstUnitNum" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ID" type="xs:int" />
          <xs:element minOccurs="0" name="InstallationArea" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="InstallationAreaShort" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="IsActive" type="xs:boolean" />
          <xs:element minOccurs="0" name="JobSiteAddress1" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="JobSiteAddress2" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="JobSiteCity" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="JobSiteCountry" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="JobSitePostalCode" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="JobSiteState" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="LiftType" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MOFCompleteDate" type="xs:dateTime" />
          <xs:element minOccurs="0" name="MOFCompleted" type="xs:boolean" />
          <xs:element minOccurs="0" name="ManualMOF" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Metric" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ModelCode" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="NumberofFloors" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="Parameter" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ParentUnitNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PenaltyClause" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PenaltyRemarks" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PrebidCheckStatus" type="xs:int" />
          <xs:element minOccurs="0" name="Product" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ProductFamily" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ProductGroup" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ProjectName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ProjectNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PromisdeShipmentDate" type="xs:dateTime" />
          <xs:element minOccurs="0" name="PromisedDeliveryDate" type="xs:dateTime" />
          <xs:element minOccurs="0" name="QuotationInfo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Region" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RegionCode" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ReleaseDate" type="xs:dateTime" />
          <xs:element minOccurs="0" name="ReplaceGroup" nillable="true" type="xs:boolean" />
          <xs:element minOccurs="0" name="RequestedDeliveryDate" type="xs:dateTime" />
          <xs:element minOccurs="0" name="SalesRepresentative" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SellingOfficeName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Status" type="xs:int" />
          <xs:element minOccurs="0" name="TotalDeliveryLeadTime" type="xs:int" />
          <xs:element minOccurs="0" name="TotalMaterialLeadTime" type="xs:int" />
          <xs:element minOccurs="0" name="UnitBookingDate" type="xs:dateTime" />
          <xs:element minOccurs="0" name="UnitName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="UnitRequestedDate" type="xs:dateTime" />
          <xs:element minOccurs="0" name="UnitValidationDate" type="xs:dateTime" />
          <xs:element minOccurs="0" name="VendorEquipment" type="xs:boolean" />
          <xs:element minOccurs="0" name="Version" nillable="true" type="xs:int" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EDSMOFContractInfo" nillable="true" type="tns:EDSMOFContractInfo" />
  <xs:complexType name="EdsTaskInfoWS">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q17="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q17:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="Branch" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ContractGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="ContractNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CreateTime" type="xs:dateTime" />
          <xs:element minOccurs="0" name="CreateUserNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Creator" type="xs:int" />
          <xs:element minOccurs="0" name="FileFlag" type="xs:int" />
          <xs:element minOccurs="0" name="ID" type="ser:guid" />
          <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Parameters" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Priority" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="ProductNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ProjectNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RunLevel" type="xs:int" />
          <xs:element minOccurs="0" name="RunTimePoint" nillable="true" type="xs:dateTime" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsTaskInfoWS" nillable="true" type="tns:EdsTaskInfoWS" />
  <xs:complexType name="ArrayOfEdsTaskInfoWS">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsTaskInfoWS" nillable="true" type="tns:EdsTaskInfoWS" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsTaskInfoWS" nillable="true" type="tns:ArrayOfEdsTaskInfoWS" />
  <xs:complexType name="EdsDllFilePrefab">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q18="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q18:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="ArchiveTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="ChildCop" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DllFile" nillable="true" type="xs:base64Binary" />
          <xs:element minOccurs="0" name="DllType" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="DrawingNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EffectEnd" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="EffectStart" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="Expand" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="FileFlag" type="xs:int" />
          <xs:element minOccurs="0" name="OpState" type="xs:int" />
          <xs:element minOccurs="0" name="PCNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ProgramCode" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsDllFilePrefab" nillable="true" type="tns:EdsDllFilePrefab" />
  <xs:complexType name="EdsEpdTaskComplete">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q19="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q19:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="BomRoot" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="GlobalParas" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RunLog" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="StandardFlag" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="Status" type="xs:int" />
          <xs:element minOccurs="0" name="TaskID" type="ser:guid" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsEpdTaskComplete" nillable="true" type="tns:EdsEpdTaskComplete" />
  <xs:complexType name="ArrayOfEdsEpdTaskInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsEpdTaskInfo" nillable="true" type="tns:EdsEpdTaskInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsEpdTaskInfo" nillable="true" type="tns:ArrayOfEdsEpdTaskInfo" />
  <xs:complexType name="ArrayOfEdsEpdTaskComplete">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsEpdTaskComplete" nillable="true" type="tns:EdsEpdTaskComplete" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsEpdTaskComplete" nillable="true" type="tns:ArrayOfEdsEpdTaskComplete" />
  <xs:complexType name="ArrayOfEdsCompleteTaskWS">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsCompleteTaskWS" nillable="true" type="tns:EdsCompleteTaskWS" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsCompleteTaskWS" nillable="true" type="tns:ArrayOfEdsCompleteTaskWS" />
  <xs:complexType name="ArrayOfEdsFMOrg">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsFMOrg" nillable="true" type="tns:EdsFMOrg" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsFMOrg" nillable="true" type="tns:ArrayOfEdsFMOrg" />
  <xs:complexType name="EdsFMOrg">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q20="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q20:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="ChildDrawingNO" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Description" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DrawingNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MainPart" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PackBox" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Remark" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TaskGuid" type="ser:guid" />
          <xs:element minOccurs="0" name="ToCount" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ToPackNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TotalRange" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Volumescale" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsFMOrg" nillable="true" type="tns:EdsFMOrg" />
  <xs:complexType name="ArrayOfEdsBomRootOrg">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsBomRootOrg" nillable="true" type="tns:EdsBomRootOrg" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsBomRootOrg" nillable="true" type="tns:ArrayOfEdsBomRootOrg" />
  <xs:complexType name="EdsBomRootOrg">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q21="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q21:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="ArchiveTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="BomRootOrgGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="ContractGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="CreateTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="Creator" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CreatorEmployeeCode" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DepTopo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EffectEnd" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="EffectStart" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="F3001" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3015" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3016" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3060" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ParentNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TaskGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="Type" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsBomRootOrg" nillable="true" type="tns:EdsBomRootOrg" />
  <xs:complexType name="ArrayOfEdsPartSupplierOrg">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsPartSupplierOrg" nillable="true" type="tns:EdsPartSupplierOrg" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsPartSupplierOrg" nillable="true" type="tns:ArrayOfEdsPartSupplierOrg" />
  <xs:complexType name="EdsPartSupplierOrg">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q22="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q22:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="AutoId" type="xs:int" />
          <xs:element minOccurs="0" name="Branch" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PartGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="PartNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PartPCNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Price" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SupplierCategory" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="SupplierName" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SupplierOrderId" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="SupplierType" nillable="true" type="xs:int" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsPartSupplierOrg" nillable="true" type="tns:EdsPartSupplierOrg" />
  <xs:complexType name="ArrayOfEdsPartOrg">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsPartOrg" nillable="true" type="tns:EdsPartOrg" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsPartOrg" nillable="true" type="tns:ArrayOfEdsPartOrg" />
  <xs:complexType name="ArrayOfEdsPartToRouting">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsPartToRouting" nillable="true" type="tns:EdsPartToRouting" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsPartToRouting" nillable="true" type="tns:ArrayOfEdsPartToRouting" />
  <xs:complexType name="EdsPartToRouting">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q23="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q23:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="ContractGuid" type="ser:guid" />
          <xs:element minOccurs="0" name="InputParams" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="InternalParams" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PartNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RoutingNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SelRoutingParam" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SystemParams" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TaskGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="UnStandardReason" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsPartToRouting" nillable="true" type="tns:EdsPartToRouting" />
  <xs:complexType name="ArrayOfEdsBomOrg">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsBomOrg" nillable="true" type="tns:EdsBomOrg" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsBomOrg" nillable="true" type="tns:ArrayOfEdsBomOrg" />
  <xs:complexType name="EdsBomOrg">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q24="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q24:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="BomRootOrgGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="ChildFileType" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="ChildPartGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="ContractGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="EnterDescrption" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3000" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3001" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3002" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3003" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3004" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3006" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3007" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3008" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3010" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="F3012" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="F3013" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3014" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3015" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3016" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3060" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3100" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3101" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3102" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3103" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3104" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3105" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3106" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3107" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3108" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3200" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MFGDescrption" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="OrderId" type="xs:int" />
          <xs:element minOccurs="0" name="ParentMatSuffix" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ParentNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Qty" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ReferencePartNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TaskGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="UnStandardReason" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsBomOrg" nillable="true" type="tns:EdsBomOrg" />
  <xs:complexType name="ArrayOfEdsBomRootOrgSO">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsBomRootOrgSO" nillable="true" type="tns:EdsBomRootOrgSO" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsBomRootOrgSO" nillable="true" type="tns:ArrayOfEdsBomRootOrgSO" />
  <xs:complexType name="EdsBomRootOrgSO">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q25="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q25:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="ArchiveTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="BomRootOrgGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="BranchFlowGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="ContractGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="CreateTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="Creator" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="CreatorEmployeeCode" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DepTopo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EffectEnd" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="EffectStart" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="F3001" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3015" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3016" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3060" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ParentNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TaskGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="Type" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsBomRootOrgSO" nillable="true" type="tns:EdsBomRootOrgSO" />
  <xs:complexType name="ArrayOfEdsPartOrgSO">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsPartOrgSO" nillable="true" type="tns:EdsPartOrgSO" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsPartOrgSO" nillable="true" type="tns:ArrayOfEdsPartOrgSO" />
  <xs:complexType name="ArrayOfEdsPartToRoutingOrgSO">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsPartToRoutingOrgSO" nillable="true" type="tns:EdsPartToRoutingOrgSO" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsPartToRoutingOrgSO" nillable="true" type="tns:ArrayOfEdsPartToRoutingOrgSO" />
  <xs:complexType name="EdsPartToRoutingOrgSO">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q26="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q26:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="ContractGuid" type="ser:guid" />
          <xs:element minOccurs="0" name="InputParams" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="InternalParams" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PartNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="RoutingNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SelRoutingParam" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="SystemParams" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TaskGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="UnStandardReason" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsPartToRoutingOrgSO" nillable="true" type="tns:EdsPartToRoutingOrgSO" />
  <xs:complexType name="ArrayOfEdsBomOrgSO">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EdsBomOrgSO" nillable="true" type="tns:EdsBomOrgSO" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEdsBomOrgSO" nillable="true" type="tns:ArrayOfEdsBomOrgSO" />
  <xs:complexType name="EdsBomOrgSO">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q27="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q27:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="BomRootOrgGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="BomType" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="ChildFileType" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="ChildPartGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="ContractGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="EnterDescrption" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3000" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3001" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3002" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3003" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3004" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3006" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3007" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3008" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3010" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="F3012" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="F3013" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3014" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3015" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3016" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3060" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3100" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3101" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3102" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3103" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3104" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3105" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3106" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3107" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3108" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="F3200" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="MFGDescrption" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="OrderId" type="xs:int" />
          <xs:element minOccurs="0" name="ParentMatSuffix" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ParentNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="PartGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="Qty" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ReferencePartNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="TaskGuid" nillable="true" type="ser:guid" />
          <xs:element minOccurs="0" name="UnStandardReason" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsBomOrgSO" nillable="true" type="tns:EdsBomOrgSO" />
  <xs:complexType name="EdsDllFileHis">
    <xs:complexContent mixed="false">
      <xs:extension xmlns:q28="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" base="q28:EntityObject">
        <xs:sequence>
          <xs:element minOccurs="0" name="ArchiveTime" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="AutoID" type="xs:int" />
          <xs:element minOccurs="0" name="ChildCop" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="DllFile" nillable="true" type="xs:base64Binary" />
          <xs:element minOccurs="0" name="DllType" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="DrawingNumber" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="EffectEnd" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="EffectStart" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="Expand" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="FileFlag" type="xs:int" />
          <xs:element minOccurs="0" name="OpState" type="xs:int" />
          <xs:element minOccurs="0" name="PCNo" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="ProgramCode" nillable="true" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="EdsDllFileHis" nillable="true" type="tns:EdsDllFileHis" />
</xs:schema>
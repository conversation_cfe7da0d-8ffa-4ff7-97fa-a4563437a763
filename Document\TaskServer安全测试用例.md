# CADTaskServer 安全测试用例

## 1. 安全测试概述

### 1.1 测试目标
验证CADTaskServer系统的安全防护能力，包括身份认证、权限控制、数据保护、输入验证、通信安全等方面，确保系统能够抵御常见的安全威胁。

### 1.2 安全威胁模型
基于OWASP Top 10和系统特点，识别的主要安全威胁：
- 身份认证绕过
- 权限提升攻击
- 注入攻击（SQL注入、命令注入）
- 跨站脚本攻击（XSS）
- 路径遍历攻击
- 拒绝服务攻击（DoS）
- 敏感信息泄露
- 不安全的通信
- 配置安全问题

### 1.3 安全测试分类
- **身份认证测试**: 验证用户身份验证机制
- **授权测试**: 验证访问控制和权限管理
- **输入验证测试**: 验证输入数据的安全过滤
- **会话管理测试**: 验证会话安全机制
- **数据保护测试**: 验证敏感数据保护
- **通信安全测试**: 验证网络通信安全
- **配置安全测试**: 验证系统配置安全性

### 1.4 测试工具
- **漏洞扫描**: OWASP ZAP, Nessus
- **渗透测试**: Burp Suite, Metasploit
- **代码审计**: SonarQube, Checkmarx
- **网络分析**: Wireshark, Fiddler

## 2. 身份认证安全测试用例

### TC_SEC_001: 正常登录流程安全测试
**测试目标**: 验证正常登录流程的安全性

**测试步骤**:
1. 使用有效用户名和密码登录
2. 验证认证过程的安全性
3. 检查会话建立机制
4. 验证登录日志记录

**安全检查点**:
- 密码传输是否加密
- 会话ID是否安全生成
- 登录失败是否记录日志
- 认证令牌是否安全

**预期结果**:
- 密码不以明文传输
- 会话ID随机且不可预测
- 登录活动完整记录
- 认证机制安全可靠

### TC_SEC_002: 暴力破解攻击防护测试
**测试目标**: 验证系统对暴力破解攻击的防护能力

**测试步骤**:
1. 使用自动化工具尝试暴力破解
2. 连续输入错误密码
3. 验证账户锁定机制
4. 测试锁定后的恢复机制

**攻击模拟**:
```
用户名: admin
密码尝试: password, 123456, admin, test, qwerty...
尝试频率: 每秒10次
持续时间: 5分钟
```

**安全检查点**:
- 是否有登录尝试次数限制
- 账户锁定机制是否有效
- 是否有验证码或延迟机制
- 攻击行为是否被记录

**预期结果**:
- 连续失败后账户被锁定
- 锁定时间合理（5-30分钟）
- 攻击行为被记录和告警
- 系统性能不受影响

### TC_SEC_003: 弱密码策略测试
**测试目标**: 验证密码强度要求和策略

**测试步骤**:
1. 尝试设置弱密码
2. 测试密码复杂度要求
3. 验证密码历史记录
4. 测试密码过期机制

**弱密码测试**:
```
简单密码: 123456, password, admin
短密码: 123, ab
重复密码: 111111, aaaaaa
字典密码: qwerty, welcome
```

**安全检查点**:
- 密码长度最小要求
- 密码复杂度要求
- 密码重用限制
- 密码过期提醒

**预期结果**:
- 弱密码被拒绝
- 密码策略明确提示
- 历史密码不能重用
- 定期提醒密码更新

### TC_SEC_004: 会话劫持防护测试
**测试目标**: 验证会话安全机制

**测试步骤**:
1. 获取有效会话令牌
2. 尝试会话固定攻击
3. 测试会话超时机制
4. 验证会话注销安全

**攻击模拟**:
- 会话ID预测攻击
- 会话固定攻击
- 会话重放攻击
- 跨站会话攻击

**安全检查点**:
- 会话ID是否随机生成
- 会话是否有超时机制
- 注销是否完全清除会话
- 会话是否绑定IP地址

**预期结果**:
- 会话ID不可预测
- 会话自动超时
- 注销完全清除会话
- 会话劫持攻击失败

### TC_SEC_005: 多因素认证测试
**测试目标**: 验证多因素认证机制（如果实现）

**测试步骤**:
1. 测试第二因素认证
2. 验证认证因素绑定
3. 测试认证因素恢复
4. 验证认证流程安全

**认证因素**:
- 短信验证码
- 邮箱验证码
- 硬件令牌
- 生物识别

**预期结果**:
- 多因素认证正常工作
- 认证因素安全绑定
- 恢复机制安全可靠
- 绕过攻击无效

## 3. 权限控制安全测试用例

### TC_SEC_006: 垂直权限提升测试
**测试目标**: 验证垂直权限控制机制

**测试步骤**:
1. 使用普通用户账户登录
2. 尝试访问管理员功能
3. 尝试修改权限配置
4. 验证权限检查机制

**攻击场景**:
- 直接访问管理URL
- 修改请求参数提升权限
- 利用功能漏洞获取高权限
- 绕过前端权限检查

**安全检查点**:
- 后端权限验证是否完整
- 权限检查是否在每个操作
- 权限提升是否被阻止
- 违规操作是否被记录

**预期结果**:
- 普通用户无法访问管理功能
- 权限提升尝试被阻止
- 违规操作被记录和告警
- 权限检查机制完整

### TC_SEC_007: 水平权限提升测试
**测试目标**: 验证用户间数据隔离

**测试步骤**:
1. 创建多个测试用户
2. 尝试访问其他用户数据
3. 测试数据修改权限
4. 验证数据隔离机制

**攻击场景**:
- 修改用户ID参数访问他人数据
- 利用批量操作访问他人资源
- 通过共享功能泄露数据
- 绕过用户身份验证

**安全检查点**:
- 用户数据是否完全隔离
- 用户ID是否可被枚举
- 批量操作是否有权限检查
- 共享功能是否安全

**预期结果**:
- 用户只能访问自己的数据
- 他人数据访问被拒绝
- 用户ID不可被枚举
- 数据隔离机制有效

### TC_SEC_008: 功能权限测试
**测试目标**: 验证功能级别的权限控制

**测试步骤**:
1. 测试各功能模块的权限要求
2. 验证权限配置的有效性
3. 测试权限继承和组合
4. 验证权限变更的及时性

**功能权限测试**:
- 配置文件读取权限
- 任务执行权限
- 文件上传权限
- 日志查看权限
- 系统管理权限

**预期结果**:
- 每个功能都有明确权限要求
- 权限配置正确生效
- 权限变更立即生效
- 无权限功能被隐藏或禁用

### TC_SEC_009: API权限测试
**测试目标**: 验证API接口的权限控制

**测试步骤**:
1. 测试WCF接口的权限要求
2. 尝试未授权API调用
3. 验证API令牌机制
4. 测试API权限粒度

**API权限测试**:
```
未授权调用: 无令牌访问API
权限不足: 普通用户调用管理API
令牌过期: 使用过期令牌访问
权限变更: 权限变更后的API访问
```

**预期结果**:
- 未授权API调用被拒绝
- API权限检查完整
- 令牌机制安全有效
- 权限粒度合理

## 4. 输入验证安全测试用例

### TC_SEC_010: SQL注入攻击测试
**测试目标**: 验证SQL注入防护机制

**测试步骤**:
1. 在输入字段注入SQL代码
2. 测试不同类型的SQL注入
3. 验证参数化查询使用
4. 检查错误信息泄露

**注入测试载荷**:
```sql
-- 基本注入
' OR '1'='1
'; DROP TABLE Users; --
' UNION SELECT * FROM Users --

-- 盲注测试
' AND (SELECT COUNT(*) FROM Users) > 0 --
' AND SUBSTRING(@@version,1,1) = '5' --

-- 时间盲注
'; WAITFOR DELAY '00:00:05' --
```

**安全检查点**:
- 是否使用参数化查询
- 输入是否正确转义
- 错误信息是否泄露数据库信息
- 数据库权限是否最小化

**预期结果**:
- SQL注入攻击无效
- 数据库查询安全
- 错误信息不泄露敏感信息
- 数据库连接权限最小

### TC_SEC_011: 命令注入攻击测试
**测试目标**: 验证命令注入防护机制

**测试步骤**:
1. 在文件路径参数注入命令
2. 测试系统命令执行
3. 验证输入过滤机制
4. 检查命令执行权限

**注入测试载荷**:
```bash
# Windows命令注入
file.txt; dir
file.txt & whoami
file.txt | type C:\Windows\System32\drivers\etc\hosts

# 路径遍历
../../../windows/system32/config/sam
..\..\..\..\boot.ini
```

**安全检查点**:
- 文件路径是否验证
- 系统命令是否可执行
- 输入是否正确过滤
- 进程权限是否最小化

**预期结果**:
- 命令注入攻击无效
- 文件路径验证严格
- 系统命令无法执行
- 进程权限最小化

### TC_SEC_012: 跨站脚本(XSS)攻击测试
**测试目标**: 验证XSS攻击防护机制

**测试步骤**:
1. 在输入字段注入脚本代码
2. 测试反射型XSS
3. 测试存储型XSS
4. 验证输出编码机制

**XSS测试载荷**:
```html
<!-- 基本XSS -->
<script>alert('XSS')</script>
<img src=x onerror=alert('XSS')>
<svg onload=alert('XSS')>

<!-- 绕过过滤 -->
<ScRiPt>alert('XSS')</ScRiPt>
javascript:alert('XSS')
<iframe src="javascript:alert('XSS')">
```

**安全检查点**:
- 输入是否正确过滤
- 输出是否正确编码
- 内容安全策略是否配置
- 危险标签是否被移除

**预期结果**:
- XSS攻击无效
- 脚本代码被过滤或编码
- 页面显示安全
- 用户数据不被窃取

### TC_SEC_013: 文件上传安全测试
**测试目标**: 验证文件上传的安全机制

**测试步骤**:
1. 上传恶意文件类型
2. 测试文件大小限制
3. 验证文件内容检查
4. 测试文件存储安全

**恶意文件测试**:
```
可执行文件: .exe, .bat, .cmd, .scr
脚本文件: .asp, .php, .jsp, .js
双扩展名: file.jpg.exe, file.txt.bat
空字节: file.asp%00.jpg
```

**安全检查点**:
- 文件类型是否严格验证
- 文件大小是否有限制
- 文件内容是否检查
- 上传路径是否安全

**预期结果**:
- 恶意文件上传被拒绝
- 文件类型验证严格
- 文件大小限制有效
- 上传文件无法执行

### TC_SEC_014: 路径遍历攻击测试
**测试目标**: 验证路径遍历攻击防护

**测试步骤**:
1. 尝试访问系统敏感文件
2. 测试相对路径遍历
3. 验证文件访问控制
4. 检查路径规范化

**路径遍历测试**:
```
../../../etc/passwd
..\..\..\..\windows\system32\config\sam
....//....//....//etc/passwd
%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd
```

**安全检查点**:
- 路径是否规范化
- 相对路径是否被阻止
- 文件访问是否受限
- 敏感文件是否可访问

**预期结果**:
- 路径遍历攻击无效
- 敏感文件无法访问
- 文件访问严格受控
- 路径验证机制有效

## 5. 数据保护安全测试用例

### TC_SEC_015: 敏感数据加密测试
**测试目标**: 验证敏感数据的加密保护

**测试步骤**:
1. 检查配置文件中的敏感信息
2. 验证数据库中的敏感数据
3. 测试传输过程中的数据加密
4. 检查日志文件中的敏感信息

**敏感数据类型**:
- 用户密码
- 数据库连接字符串
- API密钥
- 个人身份信息

**安全检查点**:
- 密码是否哈希存储
- 连接字符串是否加密
- 传输数据是否加密
- 日志是否包含敏感信息

**预期结果**:
- 敏感数据加密存储
- 传输过程加密保护
- 日志不包含敏感信息
- 加密算法安全可靠

### TC_SEC_016: 数据备份安全测试
**测试目标**: 验证数据备份的安全性

**测试步骤**:
1. 检查备份文件的访问权限
2. 验证备份数据的加密
3. 测试备份文件的完整性
4. 检查备份恢复的安全性

**安全检查点**:
- 备份文件访问权限
- 备份数据加密状态
- 备份完整性验证
- 恢复过程安全性

**预期结果**:
- 备份文件权限严格
- 备份数据加密保护
- 完整性验证机制有效
- 恢复过程安全可控

### TC_SEC_017: 数据销毁安全测试
**测试目标**: 验证敏感数据的安全销毁

**测试步骤**:
1. 测试临时文件清理
2. 验证内存数据清除
3. 检查日志文件轮转
4. 测试数据删除的彻底性

**数据销毁测试**:
- 临时文件自动清理
- 内存敏感数据清零
- 日志文件安全删除
- 缓存数据及时清理

**预期结果**:
- 临时文件及时清理
- 内存数据安全清除
- 删除数据无法恢复
- 数据销毁机制完整

## 6. 通信安全测试用例

### TC_SEC_018: 网络传输加密测试
**测试目标**: 验证网络通信的加密保护

**测试步骤**:
1. 监控网络传输数据
2. 检查加密协议使用
3. 验证证书有效性
4. 测试加密强度

**通信协议测试**:
- WCF服务通信加密
- 数据库连接加密
- 文件传输加密
- API调用加密

**安全检查点**:
- 是否使用HTTPS/TLS
- 加密算法是否安全
- 证书是否有效
- 密钥交换是否安全

**预期结果**:
- 网络传输全程加密
- 使用强加密算法
- 证书配置正确
- 通信安全可靠

### TC_SEC_019: 中间人攻击防护测试
**测试目标**: 验证中间人攻击防护机制

**测试步骤**:
1. 模拟中间人攻击
2. 测试证书验证机制
3. 验证证书固定
4. 检查通信完整性

**攻击模拟**:
- 伪造SSL证书
- DNS劫持攻击
- ARP欺骗攻击
- 代理服务器攻击

**预期结果**:
- 伪造证书被拒绝
- 通信完整性保护
- 攻击行为被检测
- 安全连接建立

### TC_SEC_020: 网络访问控制测试
**测试目标**: 验证网络访问控制机制

**测试步骤**:
1. 测试IP地址白名单
2. 验证端口访问控制
3. 检查防火墙配置
4. 测试网络隔离

**访问控制测试**:
- 允许IP访问测试
- 禁止IP访问测试
- 端口扫描测试
- 网络隔离测试

**预期结果**:
- IP白名单机制有效
- 端口访问严格控制
- 防火墙配置正确
- 网络隔离完整

---

**文档版本**: v1.0
**编写日期**: 2024年1月
**编写人**: 安全测试团队
**审核人**: 安全专家

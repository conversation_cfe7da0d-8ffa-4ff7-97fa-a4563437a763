﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace Zxtech.CADTaskServer.Properties {
    using System;
    
    
    /// <summary>
    ///   一个强类型的资源类，用于查找本地化的字符串等。
    /// </summary>
    // 此类是由 StronglyTypedResourceBuilder
    // 类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    // 若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    // (以 /str 作为命令选项)，或重新生成 VS 项目。
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   返回此类使用的缓存的 ResourceManager 实例。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Zxtech.CADTaskServer.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   重写当前线程的 CurrentUICulture 属性，对
        ///   使用此强类型资源类的所有资源查找执行重写。
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap AutoQuitBtn {
            get {
                object obj = ResourceManager.GetObject("AutoQuitBtn", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap AutoQuitBtn1 {
            get {
                object obj = ResourceManager.GetObject("AutoQuitBtn1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap AutoRunBtn {
            get {
                object obj = ResourceManager.GetObject("AutoRunBtn", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap AutoRunBtn1 {
            get {
                object obj = ResourceManager.GetObject("AutoRunBtn1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap AutoRunBtn2 {
            get {
                object obj = ResourceManager.GetObject("AutoRunBtn2", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 BIM业务运行 的本地化字符串。
        /// </summary>
        internal static string BIMBusinessFormal {
            get {
                return ResourceManager.GetString("BIMBusinessFormal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 BIM业务测试 的本地化字符串。
        /// </summary>
        internal static string BIMBusinessTest {
            get {
                return ResourceManager.GetString("BIMBusinessTest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 BIM开发测试 的本地化字符串。
        /// </summary>
        internal static string BIMDesignTest {
            get {
                return ResourceManager.GetString("BIMDesignTest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 业务正式运行 的本地化字符串。
        /// </summary>
        internal static string BusinessFormal {
            get {
                return ResourceManager.GetString("BusinessFormal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 业务测试 的本地化字符串。
        /// </summary>
        internal static string BusinessTest {
            get {
                return ResourceManager.GetString("BusinessTest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已连接 的本地化字符串。
        /// </summary>
        internal static string Connected {
            get {
                return ResourceManager.GetString("Connected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 DDS业务运行 的本地化字符串。
        /// </summary>
        internal static string DecBusinessFormal {
            get {
                return ResourceManager.GetString("DecBusinessFormal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 DDS业务测试 的本地化字符串。
        /// </summary>
        internal static string DecBusinessTest {
            get {
                return ResourceManager.GetString("DecBusinessTest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 DDS开发测试 的本地化字符串。
        /// </summary>
        internal static string DecDesignTest {
            get {
                return ResourceManager.GetString("DecDesignTest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 开发测试 的本地化字符串。
        /// </summary>
        internal static string DesignTest {
            get {
                return ResourceManager.GetString("DesignTest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 非预期的数据类型. 的本地化字符串。
        /// </summary>
        internal static string ExcepType {
            get {
                return ResourceManager.GetString("ExcepType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 真的要退出吗？ 的本地化字符串。
        /// </summary>
        internal static string ExitInfo {
            get {
                return ResourceManager.GetString("ExitInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 文件夹为&apos;{0}&apos;，已删除！ 的本地化字符串。
        /// </summary>
        internal static string FolderDeleted {
            get {
                return ResourceManager.GetString("FolderDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已得到任务，任务编号为&apos;{0}&apos;，但任务列表中无数据，CAD任务完成！ 的本地化字符串。
        /// </summary>
        internal static string GetTask {
            get {
                return ResourceManager.GetString("GetTask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap GetTaskBtn {
            get {
                object obj = ResourceManager.GetObject("GetTaskBtn", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap GetTaskSelectBtn {
            get {
                object obj = ResourceManager.GetObject("GetTaskSelectBtn", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap InsertRow {
            get {
                object obj = ResourceManager.GetObject("InsertRow", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 LDS业务运行 的本地化字符串。
        /// </summary>
        internal static string LDSBusinessFormal {
            get {
                return ResourceManager.GetString("LDSBusinessFormal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 LDS业务测试 的本地化字符串。
        /// </summary>
        internal static string LDSBusinessTest {
            get {
                return ResourceManager.GetString("LDSBusinessTest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 LDS开发测试 的本地化字符串。
        /// </summary>
        internal static string LDSDesignTest {
            get {
                return ResourceManager.GetString("LDSDesignTest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 用户已经登录! 请换一用户 的本地化字符串。
        /// </summary>
        internal static string LoginRepeat {
            get {
                return ResourceManager.GetString("LoginRepeat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 未连接 的本地化字符串。
        /// </summary>
        internal static string NotConnected {
            get {
                return ResourceManager.GetString("NotConnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 未启动 的本地化字符串。
        /// </summary>
        internal static string OnlineNoStart {
            get {
                return ResourceManager.GetString("OnlineNoStart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 已启动 的本地化字符串。
        /// </summary>
        internal static string OnlineStared {
            get {
                return ResourceManager.GetString("OnlineStared", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 密码错误! 请重新输入 的本地化字符串。
        /// </summary>
        internal static string PassError {
            get {
                return ResourceManager.GetString("PassError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap QuitBtn {
            get {
                object obj = ResourceManager.GetObject("QuitBtn", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap QuitBtn1 {
            get {
                object obj = ResourceManager.GetObject("QuitBtn1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似 CAD任务服务: 运行中...... 的本地化字符串。
        /// </summary>
        internal static string Running {
            get {
                return ResourceManager.GetString("Running", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 文件名为&apos;{0}&apos;的文件，已保存到服务器！ 的本地化字符串。
        /// </summary>
        internal static string SaveError {
            get {
                return ResourceManager.GetString("SaveError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap SetupBtn {
            get {
                object obj = ResourceManager.GetObject("SetupBtn", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap SetupBtn1 {
            get {
                object obj = ResourceManager.GetObject("SetupBtn1", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap SetupBtn2 {
            get {
                object obj = ResourceManager.GetObject("SetupBtn2", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找 System.Drawing.Bitmap 类型的本地化资源。
        /// </summary>
        internal static System.Drawing.Bitmap SetupBtn3 {
            get {
                object obj = ResourceManager.GetObject("SetupBtn3", resourceCulture);
                return ((System.Drawing.Bitmap)(obj));
            }
        }
        
        /// <summary>
        ///   查找类似   CAD任务服务: 停止 的本地化字符串。
        /// </summary>
        internal static string TaskStop {
            get {
                return ResourceManager.GetString("TaskStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   查找类似 用户名错误!请重新输入 的本地化字符串。
        /// </summary>
        internal static string UserNameError {
            get {
                return ResourceManager.GetString("UserNameError", resourceCulture);
            }
        }
    }
}

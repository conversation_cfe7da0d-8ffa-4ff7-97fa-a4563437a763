<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="TaskService" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsp:Policy wsu:Id="NetTcpBinding_ITaskService_policy">
    <wsp:ExactlyOne>
      <wsp:All>
        <msb:BinaryEncoding xmlns:msb="http://schemas.microsoft.com/ws/06/2004/mspolicy/netbinary1" />
        <msf:Streamed xmlns:msf="http://schemas.microsoft.com/ws/2006/05/framing/policy" />
        <wsaw:UsingAddressing />
      </wsp:All>
    </wsp:ExactlyOne>
  </wsp:Policy>
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://************:8112/?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://************:8112/?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://************:8112/?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" />
      <xsd:import schemaLocation="http://************:8112/?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/System.Data.Objects.DataClasses" />
      <xsd:import schemaLocation="http://************:8112/?xsd=xsd4" namespace="http://schemas.datacontract.org/2004/07/System.Data" />
      <xsd:import schemaLocation="http://************:8112/?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" />
      <xsd:import schemaLocation="http://************:8112/?xsd=xsd6" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
      <xsd:import schemaLocation="http://************:8112/?xsd=xsd7" namespace="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.Common" />
      <xsd:import schemaLocation="http://************:8112/?xsd=xsd8" namespace="http://schemas.microsoft.com/Message" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="ITaskService_SaveEdsPartSupplierOrgSOs_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsPartSupplierOrgSOs" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsPartSupplierOrgSOs_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsPartSupplierOrgSOsResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsFMOrgSOs_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsFMOrgSOs" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsFMOrgSOs_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsFMOrgSOsResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsDbPropertyAll_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsDbPropertyAll" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsDbPropertyAll_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsDbPropertyAllResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsDbPropertyWSAll_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsDbPropertyWSAll" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsDbPropertyWSAll_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsDbPropertyWSAllResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsCompleteTaskWS_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsCompleteTaskWS" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsCompleteTaskWS_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsCompleteTaskWSResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_IsHaveThisPartCA_InputMessage">
    <wsdl:part name="parameters" element="tns:IsHaveThisPartCA" />
  </wsdl:message>
  <wsdl:message name="ITaskService_IsHaveThisPartCA_OutputMessage">
    <wsdl:part name="parameters" element="tns:IsHaveThisPartCAResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_IsHaveThisPartSO_InputMessage">
    <wsdl:part name="parameters" element="tns:IsHaveThisPartSO" />
  </wsdl:message>
  <wsdl:message name="ITaskService_IsHaveThisPartSO_OutputMessage">
    <wsdl:part name="parameters" element="tns:IsHaveThisPartSOResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDoorTypeCA_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDoorTypeCA" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDoorTypeCA_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDoorTypeCAResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDoorTypeSO_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDoorTypeSO" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDoorTypeSO_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDoorTypeSOResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_FindPartCA_InputMessage">
    <wsdl:part name="parameters" element="tns:FindPartCA" />
  </wsdl:message>
  <wsdl:message name="ITaskService_FindPartCA_OutputMessage">
    <wsdl:part name="parameters" element="tns:FindPartCAResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_FindPartSO_InputMessage">
    <wsdl:part name="parameters" element="tns:FindPartSO" />
  </wsdl:message>
  <wsdl:message name="ITaskService_FindPartSO_OutputMessage">
    <wsdl:part name="parameters" element="tns:FindPartSOResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetVEdsDllFunctionDataProject_InputMessage">
    <wsdl:part name="parameters" element="tns:GetVEdsDllFunctionDataProject" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetVEdsDllFunctionDataProject_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetVEdsDllFunctionDataProjectResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetParameter_InputMessage">
    <wsdl:part name="parameters" element="tns:GetParameter" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetParameter_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetParameterResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDrawingRefParameters_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDrawingRefParameters" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDrawingRefParameters_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDrawingRefParametersResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDrawingRefParametersForHis_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDrawingRefParametersForHis" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDrawingRefParametersForHis_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDrawingRefParametersForHisResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetAccumulativeByProjectNOAndParamterNameAndParamterValue_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAccumulativeByProjectNOAndParamterNameAndParamterValue" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetAccumulativeByProjectNOAndParamterNameAndParamterValue_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAccumulativeByProjectNOAndParamterNameAndParamterValueResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_AddOrUpdateFunctionAccumulative_InputMessage">
    <wsdl:part name="parameters" element="tns:AddOrUpdateFunctionAccumulative" />
  </wsdl:message>
  <wsdl:message name="ITaskService_AddOrUpdateFunctionAccumulative_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddOrUpdateFunctionAccumulativeResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetProjectNoByContractNo_InputMessage">
    <wsdl:part name="parameters" element="tns:GetProjectNoByContractNo" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetProjectNoByContractNo_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetProjectNoByContractNoResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetContractParamaters_InputMessage">
    <wsdl:part name="parameters" element="tns:GetContractParamaters" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetContractParamaters_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetContractParamatersResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetConnectContractGuid_InputMessage">
    <wsdl:part name="parameters" element="tns:GetConnectContractGuid" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetConnectContractGuid_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetConnectContractGuidResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetCurrentContractShippingStatus_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCurrentContractShippingStatus" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetCurrentContractShippingStatus_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCurrentContractShippingStatusResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetKeyParameterName_InputMessage">
    <wsdl:part name="parameters" element="tns:GetKeyParameterName" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetKeyParameterName_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetKeyParameterNameResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetParameterDesAndValueDes_InputMessage">
    <wsdl:part name="parameters" element="tns:GetParameterDesAndValueDes" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetParameterDesAndValueDes_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetParameterDesAndValueDesResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetPCBaseInfo_InputMessage">
    <wsdl:part name="parameters" element="tns:GetPCBaseInfo" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetPCBaseInfo_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetPCBaseInfoResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_UpdateEdsTaskInfo_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdateEdsTaskInfo" />
  </wsdl:message>
  <wsdl:message name="ITaskService_UpdateEdsTaskInfo_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdateEdsTaskInfoResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveTestDrawingParamLst_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveTestDrawingParamLst" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveTestDrawingParamLst_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveTestDrawingParamLstResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_CPARAGetDLL_InputMessage">
    <wsdl:part name="parameters" element="tns:CPARAGetDLL" />
  </wsdl:message>
  <wsdl:message name="ITaskService_CPARAGetDLL_OutputMessage">
    <wsdl:part name="parameters" element="tns:CPARAGetDLLResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetAllPrivateParas_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAllPrivateParas" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetAllPrivateParas_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAllPrivateParasResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetGlobalParas_InputMessage">
    <wsdl:part name="parameters" element="tns:GetGlobalParas" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetGlobalParas_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetGlobalParasResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetBacklogRegula_InputMessage">
    <wsdl:part name="parameters" element="tns:GetBacklogRegula" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetBacklogRegula_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetBacklogRegulaResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_InsertNonStandardTaskItem_InputMessage">
    <wsdl:part name="parameters" element="tns:InsertNonStandardTaskItem" />
  </wsdl:message>
  <wsdl:message name="ITaskService_InsertNonStandardTaskItem_OutputMessage">
    <wsdl:part name="parameters" element="tns:InsertNonStandardTaskItemResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_AddEdsTaskParaRef_InputMessage">
    <wsdl:part name="parameters" element="tns:AddEdsTaskParaRef" />
  </wsdl:message>
  <wsdl:message name="ITaskService_AddEdsTaskParaRef_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddEdsTaskParaRefResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsTaskParaRef_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsTaskParaRef" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsTaskParaRef_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsTaskParaRefResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetAllPara_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAllPara" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetAllPara_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAllParaResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_CountForSql_InputMessage">
    <wsdl:part name="parameters" element="tns:CountForSql" />
  </wsdl:message>
  <wsdl:message name="ITaskService_CountForSql_OutputMessage">
    <wsdl:part name="parameters" element="tns:CountForSqlResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDrawingVersion_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDrawingVersion" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDrawingVersion_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDrawingVersionResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_QueryProjectMehtod_InputMessage">
    <wsdl:part name="parameters" element="tns:QueryProjectMehtod" />
  </wsdl:message>
  <wsdl:message name="ITaskService_QueryProjectMehtod_OutputMessage">
    <wsdl:part name="parameters" element="tns:QueryProjectMehtodResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetProjectInfo_InputMessage">
    <wsdl:part name="parameters" element="tns:GetProjectInfo" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetProjectInfo_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetProjectInfoResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDensity_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDensity" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDensity_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDensityResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetTaskInfoViewWhere_InputMessage">
    <wsdl:part name="parameters" element="tns:GetTaskInfoViewWhere" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetTaskInfoViewWhere_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetTaskInfoViewWhereResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_UpdatePartProp_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdatePartProp" />
  </wsdl:message>
  <wsdl:message name="ITaskService_UpdatePartProp_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdatePartPropResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SumMassProperty_InputMessage">
    <wsdl:part name="parameters" element="tns:SumMassProperty" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SumMassProperty_OutputMessage">
    <wsdl:part name="parameters" element="tns:SumMassPropertyResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsMOFContractList_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsMOFContractList" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsMOFContractList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsMOFContractListResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_AddUnstatndDrawingVerionSO_InputMessage">
    <wsdl:part name="parameters" element="tns:AddUnstatndDrawingVerionSO" />
  </wsdl:message>
  <wsdl:message name="ITaskService_AddUnstatndDrawingVerionSO_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddUnstatndDrawingVerionSOResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_AddTaskQueue_InputMessage">
    <wsdl:part name="parameters" element="tns:AddTaskQueue" />
  </wsdl:message>
  <wsdl:message name="ITaskService_AddTaskQueue_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddTaskQueueResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_FreezeTask_InputMessage">
    <wsdl:part name="parameters" element="tns:FreezeTask" />
  </wsdl:message>
  <wsdl:message name="ITaskService_FreezeTask_OutputMessage">
    <wsdl:part name="parameters" element="tns:FreezeTaskResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetNotFinishTask_InputMessage">
    <wsdl:part name="parameters" element="tns:GetNotFinishTask" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetNotFinishTask_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetNotFinishTaskResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetNumber_InputMessage">
    <wsdl:part name="parameters" element="tns:GetNumber" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetNumber_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetNumberResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetOnLineWorkStations_InputMessage">
    <wsdl:part name="parameters" element="tns:GetOnLineWorkStations" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetOnLineWorkStations_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetOnLineWorkStationsResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetQueues_InputMessage">
    <wsdl:part name="parameters" element="tns:GetQueues" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetQueues_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetQueuesResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetRuningTasks_InputMessage">
    <wsdl:part name="parameters" element="tns:GetRuningTasks" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetRuningTasks_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetRuningTasksResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetTaskFromQueue_InputMessage">
    <wsdl:part name="parameters" element="tns:GetTaskFromQueue" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetTaskFromQueue_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetTaskFromQueueResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsDllFilePrefab_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsDllFilePrefab" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsDllFilePrefab_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsDllFilePrefabResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_ReportWorkStationLive_InputMessage">
    <wsdl:part name="parameters" element="tns:ReportWorkStationLive" />
  </wsdl:message>
  <wsdl:message name="ITaskService_ReportWorkStationLive_OutputMessage">
    <wsdl:part name="parameters" element="tns:ReportWorkStationLiveResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveTaskBomStream_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveTaskBomStream" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveTaskBomStream_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveTaskBomStreamResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveTaskComplete_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveTaskComplete" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveTaskComplete_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveTaskCompleteResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveTaskInfo_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveTaskInfo" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveTaskInfo_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveTaskInfoResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveTaskInfoWS_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveTaskInfoWS" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveTaskInfoWS_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveTaskInfoWSResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEpdTaskInfoWhere_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEpdTaskInfoWhere" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEpdTaskInfoWhere_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEpdTaskInfoWhereResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsTaskInfoWSWhere_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsTaskInfoWSWhere" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsTaskInfoWSWhere_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsTaskInfoWSWhereResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEpdTaskCompleteWhere_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEpdTaskCompleteWhere" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEpdTaskCompleteWhere_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEpdTaskCompleteWhereResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEpdTaskCompletesForID_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEpdTaskCompletesForID" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEpdTaskCompletesForID_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEpdTaskCompletesForIDResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsCompleteTaskWSWhere_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsCompleteTaskWSWhere" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsCompleteTaskWSWhere_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsCompleteTaskWSWhereResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveTaskBom_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveTaskBom" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveTaskBom_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveTaskBomResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetTaskBom_InputMessage">
    <wsdl:part name="parameters" element="tns:GetTaskBom" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetTaskBom_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetTaskBomResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetTaskBomSO_InputMessage">
    <wsdl:part name="parameters" element="tns:GetTaskBomSO" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetTaskBomSO_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetTaskBomSOResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetProgramCode_InputMessage">
    <wsdl:part name="parameters" element="tns:GetProgramCode" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetProgramCode_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetProgramCodeResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveDllAndCode_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveDllAndCode" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveDllAndCode_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveDllAndCodeResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDLLForTest_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDLLForTest" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDLLForTest_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDLLForTestResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDLLForTestTwo_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDLLForTestTwo" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDLLForTestTwo_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDLLForTestTwoResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDLLForHis_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDLLForHis" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDLLForHis_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDLLForHisResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDLLForFormally_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDLLForFormally" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDLLForFormally_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDLLForFormallyResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDLLForFormallyTwo_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDLLForFormallyTwo" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetDLLForFormallyTwo_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDLLForFormallyTwoResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsParameterSO_InputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsParameterSO" />
  </wsdl:message>
  <wsdl:message name="ITaskService_GetEdsParameterSO_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetEdsParameterSOResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsBomOrgs_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsBomOrgs" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsBomOrgs_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsBomOrgsResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsBomRootOrgs_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsBomRootOrgs" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsBomRootOrgs_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsBomRootOrgsResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsPartOrgs_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsPartOrgs" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsPartOrgs_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsPartOrgsResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsPartToRoutings_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsPartToRoutings" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsPartToRoutings_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsPartToRoutingsResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsFMOrgs_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsFMOrgs" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsFMOrgs_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsFMOrgsResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsBomOrgSOs_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsBomOrgSOs" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsBomOrgSOs_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsBomOrgSOsResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsBomRootOrgSOs_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsBomRootOrgSOs" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsBomRootOrgSOs_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsBomRootOrgSOsResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsPartOrgSOs_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsPartOrgSOs" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsPartOrgSOs_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsPartOrgSOsResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsPartToRoutingSOs_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsPartToRoutingSOs" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsPartToRoutingSOs_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsPartToRoutingSOsResponse" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsPartSupplierOrgs_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsPartSupplierOrgs" />
  </wsdl:message>
  <wsdl:message name="ITaskService_SaveEdsPartSupplierOrgs_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveEdsPartSupplierOrgsResponse" />
  </wsdl:message>
  <wsdl:portType name="ITaskService">
    <wsdl:operation name="SaveEdsPartSupplierOrgSOs">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsPartSupplierOrgSOs" message="tns:ITaskService_SaveEdsPartSupplierOrgSOs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsPartSupplierOrgSOsResponse" message="tns:ITaskService_SaveEdsPartSupplierOrgSOs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveEdsFMOrgSOs">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsFMOrgSOs" message="tns:ITaskService_SaveEdsFMOrgSOs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsFMOrgSOsResponse" message="tns:ITaskService_SaveEdsFMOrgSOs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEdsDbPropertyAll">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetEdsDbPropertyAll" message="tns:ITaskService_GetEdsDbPropertyAll_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetEdsDbPropertyAllResponse" message="tns:ITaskService_GetEdsDbPropertyAll_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEdsDbPropertyWSAll">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetEdsDbPropertyWSAll" message="tns:ITaskService_GetEdsDbPropertyWSAll_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetEdsDbPropertyWSAllResponse" message="tns:ITaskService_GetEdsDbPropertyWSAll_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveEdsCompleteTaskWS">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsCompleteTaskWS" message="tns:ITaskService_SaveEdsCompleteTaskWS_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsCompleteTaskWSResponse" message="tns:ITaskService_SaveEdsCompleteTaskWS_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="IsHaveThisPartCA">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/IsHaveThisPartCA" message="tns:ITaskService_IsHaveThisPartCA_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/IsHaveThisPartCAResponse" message="tns:ITaskService_IsHaveThisPartCA_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="IsHaveThisPartSO">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/IsHaveThisPartSO" message="tns:ITaskService_IsHaveThisPartSO_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/IsHaveThisPartSOResponse" message="tns:ITaskService_IsHaveThisPartSO_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDoorTypeCA">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetDoorTypeCA" message="tns:ITaskService_GetDoorTypeCA_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetDoorTypeCAResponse" message="tns:ITaskService_GetDoorTypeCA_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDoorTypeSO">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetDoorTypeSO" message="tns:ITaskService_GetDoorTypeSO_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetDoorTypeSOResponse" message="tns:ITaskService_GetDoorTypeSO_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FindPartCA">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/FindPartCA" message="tns:ITaskService_FindPartCA_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/FindPartCAResponse" message="tns:ITaskService_FindPartCA_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FindPartSO">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/FindPartSO" message="tns:ITaskService_FindPartSO_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/FindPartSOResponse" message="tns:ITaskService_FindPartSO_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetVEdsDllFunctionDataProject">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetVEdsDllFunctionDataProject" message="tns:ITaskService_GetVEdsDllFunctionDataProject_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetVEdsDllFunctionDataProjectResponse" message="tns:ITaskService_GetVEdsDllFunctionDataProject_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetParameter">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetParameter" message="tns:ITaskService_GetParameter_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetParameterResponse" message="tns:ITaskService_GetParameter_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDrawingRefParameters">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetDrawingRefParameters" message="tns:ITaskService_GetDrawingRefParameters_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetDrawingRefParametersResponse" message="tns:ITaskService_GetDrawingRefParameters_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDrawingRefParametersForHis">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetDrawingRefParametersForHis" message="tns:ITaskService_GetDrawingRefParametersForHis_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetDrawingRefParametersForHisResponse" message="tns:ITaskService_GetDrawingRefParametersForHis_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAccumulativeByProjectNOAndParamterNameAndParamterValue">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetAccumulativeByProjectNOAndParamterNameAndParamterValue" message="tns:ITaskService_GetAccumulativeByProjectNOAndParamterNameAndParamterValue_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetAccumulativeByProjectNOAndParamterNameAndParamterValueResponse" message="tns:ITaskService_GetAccumulativeByProjectNOAndParamterNameAndParamterValue_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddOrUpdateFunctionAccumulative">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/AddOrUpdateFunctionAccumulative" message="tns:ITaskService_AddOrUpdateFunctionAccumulative_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/AddOrUpdateFunctionAccumulativeResponse" message="tns:ITaskService_AddOrUpdateFunctionAccumulative_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetProjectNoByContractNo">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetProjectNoByContractNo" message="tns:ITaskService_GetProjectNoByContractNo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetProjectNoByContractNoResponse" message="tns:ITaskService_GetProjectNoByContractNo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetContractParamaters">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetContractParamaters" message="tns:ITaskService_GetContractParamaters_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetContractParamatersResponse" message="tns:ITaskService_GetContractParamaters_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetConnectContractGuid">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetConnectContractGuid" message="tns:ITaskService_GetConnectContractGuid_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetConnectContractGuidResponse" message="tns:ITaskService_GetConnectContractGuid_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCurrentContractShippingStatus">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetCurrentContractShippingStatus" message="tns:ITaskService_GetCurrentContractShippingStatus_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetCurrentContractShippingStatusResponse" message="tns:ITaskService_GetCurrentContractShippingStatus_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetKeyParameterName">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetKeyParameterName" message="tns:ITaskService_GetKeyParameterName_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetKeyParameterNameResponse" message="tns:ITaskService_GetKeyParameterName_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetParameterDesAndValueDes">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetParameterDesAndValueDes" message="tns:ITaskService_GetParameterDesAndValueDes_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetParameterDesAndValueDesResponse" message="tns:ITaskService_GetParameterDesAndValueDes_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetPCBaseInfo">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetPCBaseInfo" message="tns:ITaskService_GetPCBaseInfo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetPCBaseInfoResponse" message="tns:ITaskService_GetPCBaseInfo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdateEdsTaskInfo">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/UpdateEdsTaskInfo" message="tns:ITaskService_UpdateEdsTaskInfo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/UpdateEdsTaskInfoResponse" message="tns:ITaskService_UpdateEdsTaskInfo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveTestDrawingParamLst">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveTestDrawingParamLst" message="tns:ITaskService_SaveTestDrawingParamLst_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveTestDrawingParamLstResponse" message="tns:ITaskService_SaveTestDrawingParamLst_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CPARAGetDLL">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/CPARAGetDLL" message="tns:ITaskService_CPARAGetDLL_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/CPARAGetDLLResponse" message="tns:ITaskService_CPARAGetDLL_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAllPrivateParas">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetAllPrivateParas" message="tns:ITaskService_GetAllPrivateParas_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetAllPrivateParasResponse" message="tns:ITaskService_GetAllPrivateParas_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetGlobalParas">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetGlobalParas" message="tns:ITaskService_GetGlobalParas_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetGlobalParasResponse" message="tns:ITaskService_GetGlobalParas_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetBacklogRegula">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetBacklogRegula" message="tns:ITaskService_GetBacklogRegula_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetBacklogRegulaResponse" message="tns:ITaskService_GetBacklogRegula_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="InsertNonStandardTaskItem">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/InsertNonStandardTaskItem" message="tns:ITaskService_InsertNonStandardTaskItem_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/InsertNonStandardTaskItemResponse" message="tns:ITaskService_InsertNonStandardTaskItem_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddEdsTaskParaRef">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/AddEdsTaskParaRef" message="tns:ITaskService_AddEdsTaskParaRef_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/AddEdsTaskParaRefResponse" message="tns:ITaskService_AddEdsTaskParaRef_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEdsTaskParaRef">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetEdsTaskParaRef" message="tns:ITaskService_GetEdsTaskParaRef_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetEdsTaskParaRefResponse" message="tns:ITaskService_GetEdsTaskParaRef_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAllPara">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetAllPara" message="tns:ITaskService_GetAllPara_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetAllParaResponse" message="tns:ITaskService_GetAllPara_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CountForSql">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/CountForSql" message="tns:ITaskService_CountForSql_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/CountForSqlResponse" message="tns:ITaskService_CountForSql_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDrawingVersion">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetDrawingVersion" message="tns:ITaskService_GetDrawingVersion_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetDrawingVersionResponse" message="tns:ITaskService_GetDrawingVersion_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="QueryProjectMehtod">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/QueryProjectMehtod" message="tns:ITaskService_QueryProjectMehtod_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/QueryProjectMehtodResponse" message="tns:ITaskService_QueryProjectMehtod_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetProjectInfo">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetProjectInfo" message="tns:ITaskService_GetProjectInfo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetProjectInfoResponse" message="tns:ITaskService_GetProjectInfo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDensity">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetDensity" message="tns:ITaskService_GetDensity_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetDensityResponse" message="tns:ITaskService_GetDensity_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetTaskInfoViewWhere">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetTaskInfoViewWhere" message="tns:ITaskService_GetTaskInfoViewWhere_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetTaskInfoViewWhereResponse" message="tns:ITaskService_GetTaskInfoViewWhere_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdatePartProp">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/UpdatePartProp" message="tns:ITaskService_UpdatePartProp_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/UpdatePartPropResponse" message="tns:ITaskService_UpdatePartProp_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SumMassProperty">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SumMassProperty" message="tns:ITaskService_SumMassProperty_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SumMassPropertyResponse" message="tns:ITaskService_SumMassProperty_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEdsMOFContractList">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetEdsMOFContractList" message="tns:ITaskService_GetEdsMOFContractList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetEdsMOFContractListResponse" message="tns:ITaskService_GetEdsMOFContractList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddUnstatndDrawingVerionSO">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/AddUnstatndDrawingVerionSO" message="tns:ITaskService_AddUnstatndDrawingVerionSO_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/AddUnstatndDrawingVerionSOResponse" message="tns:ITaskService_AddUnstatndDrawingVerionSO_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddTaskQueue">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/AddTaskQueue" message="tns:ITaskService_AddTaskQueue_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/AddTaskQueueResponse" message="tns:ITaskService_AddTaskQueue_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="FreezeTask">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/FreezeTask" message="tns:ITaskService_FreezeTask_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/FreezeTaskResponse" message="tns:ITaskService_FreezeTask_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetNotFinishTask">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetNotFinishTask" message="tns:ITaskService_GetNotFinishTask_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetNotFinishTaskResponse" message="tns:ITaskService_GetNotFinishTask_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetNumber">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetNumber" message="tns:ITaskService_GetNumber_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetNumberResponse" message="tns:ITaskService_GetNumber_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetOnLineWorkStations">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetOnLineWorkStations" message="tns:ITaskService_GetOnLineWorkStations_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetOnLineWorkStationsResponse" message="tns:ITaskService_GetOnLineWorkStations_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetQueues">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetQueues" message="tns:ITaskService_GetQueues_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetQueuesResponse" message="tns:ITaskService_GetQueues_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetRuningTasks">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetRuningTasks" message="tns:ITaskService_GetRuningTasks_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetRuningTasksResponse" message="tns:ITaskService_GetRuningTasks_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetTaskFromQueue">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetTaskFromQueue" message="tns:ITaskService_GetTaskFromQueue_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetTaskFromQueueResponse" message="tns:ITaskService_GetTaskFromQueue_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEdsDllFilePrefab">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetEdsDllFilePrefab" message="tns:ITaskService_GetEdsDllFilePrefab_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetEdsDllFilePrefabResponse" message="tns:ITaskService_GetEdsDllFilePrefab_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ReportWorkStationLive">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/ReportWorkStationLive" message="tns:ITaskService_ReportWorkStationLive_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/ReportWorkStationLiveResponse" message="tns:ITaskService_ReportWorkStationLive_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveTaskBomStream">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveTaskBomStream" message="tns:ITaskService_SaveTaskBomStream_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveTaskBomStreamResponse" message="tns:ITaskService_SaveTaskBomStream_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveTaskComplete">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveTaskComplete" message="tns:ITaskService_SaveTaskComplete_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveTaskCompleteResponse" message="tns:ITaskService_SaveTaskComplete_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveTaskInfo">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveTaskInfo" message="tns:ITaskService_SaveTaskInfo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveTaskInfoResponse" message="tns:ITaskService_SaveTaskInfo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveTaskInfoWS">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveTaskInfoWS" message="tns:ITaskService_SaveTaskInfoWS_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveTaskInfoWSResponse" message="tns:ITaskService_SaveTaskInfoWS_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEpdTaskInfoWhere">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetEpdTaskInfoWhere" message="tns:ITaskService_GetEpdTaskInfoWhere_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetEpdTaskInfoWhereResponse" message="tns:ITaskService_GetEpdTaskInfoWhere_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEdsTaskInfoWSWhere">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetEdsTaskInfoWSWhere" message="tns:ITaskService_GetEdsTaskInfoWSWhere_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetEdsTaskInfoWSWhereResponse" message="tns:ITaskService_GetEdsTaskInfoWSWhere_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEpdTaskCompleteWhere">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetEpdTaskCompleteWhere" message="tns:ITaskService_GetEpdTaskCompleteWhere_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetEpdTaskCompleteWhereResponse" message="tns:ITaskService_GetEpdTaskCompleteWhere_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEpdTaskCompletesForID">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetEpdTaskCompletesForID" message="tns:ITaskService_GetEpdTaskCompletesForID_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetEpdTaskCompletesForIDResponse" message="tns:ITaskService_GetEpdTaskCompletesForID_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEdsCompleteTaskWSWhere">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetEdsCompleteTaskWSWhere" message="tns:ITaskService_GetEdsCompleteTaskWSWhere_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetEdsCompleteTaskWSWhereResponse" message="tns:ITaskService_GetEdsCompleteTaskWSWhere_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveTaskBom">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveTaskBom" message="tns:ITaskService_SaveTaskBom_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveTaskBomResponse" message="tns:ITaskService_SaveTaskBom_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetTaskBom">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetTaskBom" message="tns:ITaskService_GetTaskBom_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetTaskBomResponse" message="tns:ITaskService_GetTaskBom_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetTaskBomSO">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetTaskBomSO" message="tns:ITaskService_GetTaskBomSO_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetTaskBomSOResponse" message="tns:ITaskService_GetTaskBomSO_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetProgramCode">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetProgramCode" message="tns:ITaskService_GetProgramCode_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetProgramCodeResponse" message="tns:ITaskService_GetProgramCode_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveDllAndCode">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveDllAndCode" message="tns:ITaskService_SaveDllAndCode_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveDllAndCodeResponse" message="tns:ITaskService_SaveDllAndCode_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDLLForTest">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetDLLForTest" message="tns:ITaskService_GetDLLForTest_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetDLLForTestResponse" message="tns:ITaskService_GetDLLForTest_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDLLForTestTwo">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetDLLForTestTwo" message="tns:ITaskService_GetDLLForTestTwo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetDLLForTestTwoResponse" message="tns:ITaskService_GetDLLForTestTwo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDLLForHis">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetDLLForHis" message="tns:ITaskService_GetDLLForHis_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetDLLForHisResponse" message="tns:ITaskService_GetDLLForHis_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDLLForFormally">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetDLLForFormally" message="tns:ITaskService_GetDLLForFormally_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetDLLForFormallyResponse" message="tns:ITaskService_GetDLLForFormally_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDLLForFormallyTwo">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetDLLForFormallyTwo" message="tns:ITaskService_GetDLLForFormallyTwo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetDLLForFormallyTwoResponse" message="tns:ITaskService_GetDLLForFormallyTwo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetEdsParameterSO">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/GetEdsParameterSO" message="tns:ITaskService_GetEdsParameterSO_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/GetEdsParameterSOResponse" message="tns:ITaskService_GetEdsParameterSO_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveEdsBomOrgs">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsBomOrgs" message="tns:ITaskService_SaveEdsBomOrgs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsBomOrgsResponse" message="tns:ITaskService_SaveEdsBomOrgs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveEdsBomRootOrgs">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsBomRootOrgs" message="tns:ITaskService_SaveEdsBomRootOrgs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsBomRootOrgsResponse" message="tns:ITaskService_SaveEdsBomRootOrgs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveEdsPartOrgs">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsPartOrgs" message="tns:ITaskService_SaveEdsPartOrgs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsPartOrgsResponse" message="tns:ITaskService_SaveEdsPartOrgs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveEdsPartToRoutings">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsPartToRoutings" message="tns:ITaskService_SaveEdsPartToRoutings_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsPartToRoutingsResponse" message="tns:ITaskService_SaveEdsPartToRoutings_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveEdsFMOrgs">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsFMOrgs" message="tns:ITaskService_SaveEdsFMOrgs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsFMOrgsResponse" message="tns:ITaskService_SaveEdsFMOrgs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveEdsBomOrgSOs">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsBomOrgSOs" message="tns:ITaskService_SaveEdsBomOrgSOs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsBomOrgSOsResponse" message="tns:ITaskService_SaveEdsBomOrgSOs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveEdsBomRootOrgSOs">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsBomRootOrgSOs" message="tns:ITaskService_SaveEdsBomRootOrgSOs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsBomRootOrgSOsResponse" message="tns:ITaskService_SaveEdsBomRootOrgSOs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveEdsPartOrgSOs">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsPartOrgSOs" message="tns:ITaskService_SaveEdsPartOrgSOs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsPartOrgSOsResponse" message="tns:ITaskService_SaveEdsPartOrgSOs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveEdsPartToRoutingSOs">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsPartToRoutingSOs" message="tns:ITaskService_SaveEdsPartToRoutingSOs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsPartToRoutingSOsResponse" message="tns:ITaskService_SaveEdsPartToRoutingSOs_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveEdsPartSupplierOrgs">
      <wsdl:input wsaw:Action="http://tempuri.org/ITaskService/SaveEdsPartSupplierOrgs" message="tns:ITaskService_SaveEdsPartSupplierOrgs_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/ITaskService/SaveEdsPartSupplierOrgsResponse" message="tns:ITaskService_SaveEdsPartSupplierOrgs_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="NetTcpBinding_ITaskService" type="tns:ITaskService">
    <wsp:PolicyReference URI="#NetTcpBinding_ITaskService_policy" />
    <soap12:binding transport="http://schemas.microsoft.com/soap/tcp" />
    <wsdl:operation name="SaveEdsPartSupplierOrgSOs">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsPartSupplierOrgSOs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveEdsFMOrgSOs">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsFMOrgSOs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEdsDbPropertyAll">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetEdsDbPropertyAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEdsDbPropertyWSAll">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetEdsDbPropertyWSAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveEdsCompleteTaskWS">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsCompleteTaskWS" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsHaveThisPartCA">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/IsHaveThisPartCA" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsHaveThisPartSO">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/IsHaveThisPartSO" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDoorTypeCA">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetDoorTypeCA" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDoorTypeSO">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetDoorTypeSO" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FindPartCA">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/FindPartCA" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FindPartSO">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/FindPartSO" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetVEdsDllFunctionDataProject">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetVEdsDllFunctionDataProject" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetParameter">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetParameter" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDrawingRefParameters">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetDrawingRefParameters" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDrawingRefParametersForHis">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetDrawingRefParametersForHis" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccumulativeByProjectNOAndParamterNameAndParamterValue">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetAccumulativeByProjectNOAndParamterNameAndParamterValue" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddOrUpdateFunctionAccumulative">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/AddOrUpdateFunctionAccumulative" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetProjectNoByContractNo">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetProjectNoByContractNo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetContractParamaters">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetContractParamaters" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetConnectContractGuid">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetConnectContractGuid" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCurrentContractShippingStatus">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetCurrentContractShippingStatus" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetKeyParameterName">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetKeyParameterName" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetParameterDesAndValueDes">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetParameterDesAndValueDes" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPCBaseInfo">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetPCBaseInfo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateEdsTaskInfo">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/UpdateEdsTaskInfo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveTestDrawingParamLst">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveTestDrawingParamLst" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CPARAGetDLL">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/CPARAGetDLL" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllPrivateParas">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetAllPrivateParas" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetGlobalParas">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetGlobalParas" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetBacklogRegula">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetBacklogRegula" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InsertNonStandardTaskItem">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/InsertNonStandardTaskItem" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddEdsTaskParaRef">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/AddEdsTaskParaRef" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEdsTaskParaRef">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetEdsTaskParaRef" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAllPara">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetAllPara" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CountForSql">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/CountForSql" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDrawingVersion">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetDrawingVersion" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="QueryProjectMehtod">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/QueryProjectMehtod" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetProjectInfo">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetProjectInfo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDensity">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetDensity" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTaskInfoViewWhere">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetTaskInfoViewWhere" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdatePartProp">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/UpdatePartProp" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SumMassProperty">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SumMassProperty" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEdsMOFContractList">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetEdsMOFContractList" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddUnstatndDrawingVerionSO">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/AddUnstatndDrawingVerionSO" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddTaskQueue">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/AddTaskQueue" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FreezeTask">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/FreezeTask" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNotFinishTask">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetNotFinishTask" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetNumber">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetNumber" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetOnLineWorkStations">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetOnLineWorkStations" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetQueues">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetQueues" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetRuningTasks">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetRuningTasks" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTaskFromQueue">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetTaskFromQueue" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEdsDllFilePrefab">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetEdsDllFilePrefab" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReportWorkStationLive">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/ReportWorkStationLive" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveTaskBomStream">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveTaskBomStream" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveTaskComplete">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveTaskComplete" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveTaskInfo">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveTaskInfo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveTaskInfoWS">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveTaskInfoWS" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEpdTaskInfoWhere">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetEpdTaskInfoWhere" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEdsTaskInfoWSWhere">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetEdsTaskInfoWSWhere" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEpdTaskCompleteWhere">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetEpdTaskCompleteWhere" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEpdTaskCompletesForID">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetEpdTaskCompletesForID" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEdsCompleteTaskWSWhere">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetEdsCompleteTaskWSWhere" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveTaskBom">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveTaskBom" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTaskBom">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetTaskBom" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetTaskBomSO">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetTaskBomSO" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetProgramCode">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetProgramCode" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveDllAndCode">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveDllAndCode" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDLLForTest">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetDLLForTest" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDLLForTestTwo">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetDLLForTestTwo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDLLForHis">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetDLLForHis" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDLLForFormally">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetDLLForFormally" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDLLForFormallyTwo">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetDLLForFormallyTwo" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetEdsParameterSO">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/GetEdsParameterSO" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveEdsBomOrgs">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsBomOrgs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveEdsBomRootOrgs">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsBomRootOrgs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveEdsPartOrgs">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsPartOrgs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveEdsPartToRoutings">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsPartToRoutings" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveEdsFMOrgs">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsFMOrgs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveEdsBomOrgSOs">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsBomOrgSOs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveEdsBomRootOrgSOs">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsBomRootOrgSOs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveEdsPartOrgSOs">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsPartOrgSOs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveEdsPartToRoutingSOs">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsPartToRoutingSOs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveEdsPartSupplierOrgs">
      <soap12:operation soapAction="http://tempuri.org/ITaskService/SaveEdsPartSupplierOrgs" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="TaskService">
    <wsdl:port name="NetTcpBinding_ITaskService" binding="tns:NetTcpBinding_ITaskService">
      <soap12:address location="net.tcp://************:602/Task/TaskService" />
      <wsa10:EndpointReference>
        <wsa10:Address>net.tcp://************:602/Task/TaskService</wsa10:Address>
      </wsa10:EndpointReference>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
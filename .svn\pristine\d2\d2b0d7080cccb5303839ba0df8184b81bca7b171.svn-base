<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" />
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd5" namespace="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" />
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd6" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd7" namespace="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.Common" />
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd8" namespace="http://schemas.microsoft.com/Message" />
  <xs:element name="SaveEdsPartSupplierOrgSOs">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsPartSupplierOrgSO" nillable="true" type="q1:ArrayOfEdsPartSupplierOrgSO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsPartSupplierOrgSOsResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsFMOrgSOs">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsFMOrgSOs" nillable="true" type="q2:ArrayOfEdsFMOrgSO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsFMOrgSOsResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsDbPropertyAll">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsDbPropertyAllResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetEdsDbPropertyAllResult" nillable="true" type="q3:ArrayOfEdsDbProperty" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsDbPropertyWSAll">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsDbPropertyWSAllResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetEdsDbPropertyWSAllResult" nillable="true" type="q4:ArrayOfEdsDbPropertyWS" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsCompleteTaskWS">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsCompleteTaskWS" nillable="true" type="q5:EdsCompleteTaskWS" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsCompleteTaskWSResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="IsHaveThisPartCA">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="partNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="pcNo" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="IsHaveThisPartCAResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="IsHaveThisPartCAResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="IsHaveThisPartSO">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="partNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="pcNo" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="IsHaveThisPartSOResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="IsHaveThisPartSOResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDoorTypeCA">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="paraName" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDoorTypeCAResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetDoorTypeCAResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDoorTypeSO">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="paraName" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDoorTypeSOResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetDoorTypeSOResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="FindPartCA">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="partNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="pcNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="childCop" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="FindPartCAResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="FindPartCAResult" nillable="true" type="q6:EdsPartOrg" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="FindPartSO">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="partNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="childCop" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="FindPartSOResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="FindPartSOResult" nillable="true" type="q7:EdsPartOrgSO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetVEdsDllFunctionDataProject">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="contractNo" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetVEdsDllFunctionDataProjectResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetVEdsDllFunctionDataProjectResult" nillable="true" type="q8:ArrayOfVEdsDllFunctionData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetParameter">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetParameterResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q9="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" minOccurs="0" name="GetParameterResult" nillable="true" type="q9:Parameter" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDrawingRefParameters">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="drawingNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="pcNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="branch" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fileType" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDrawingRefParametersResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q10="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetDrawingRefParametersResult" nillable="true" type="q10:ArrayOfEdsDrawingRefParam" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDrawingRefParametersForHis">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="drawingNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="pcNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="branch" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fileType" type="xs:int" />
        <xs:element minOccurs="0" name="calculateTyp" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDrawingRefParametersForHisResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q11="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetDrawingRefParametersForHisResult" nillable="true" type="q11:ArrayOfEdsDrawingRefParam" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAccumulativeByProjectNOAndParamterNameAndParamterValue">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="proNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="parName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="parValue" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="isTest" type="xs:boolean" />
        <xs:element minOccurs="0" name="parametricStatisticalTask" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAccumulativeByProjectNOAndParamterNameAndParamterValueResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetAccumulativeByProjectNOAndParamterNameAndParamterValueResult" nillable="true" type="xs:decimal" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddOrUpdateFunctionAccumulative">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="proNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="conNO" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="parName" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="parValue" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="accumulative" nillable="true" type="xs:decimal" />
        <xs:element minOccurs="0" name="isTest" type="xs:boolean" />
        <xs:element minOccurs="0" name="script" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="parametricStatisticalTaskID" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="parameterValueDescription" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="parameterDescription" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddOrUpdateFunctionAccumulativeResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="AddOrUpdateFunctionAccumulativeResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetProjectNoByContractNo">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="conNO" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetProjectNoByContractNoResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetProjectNoByContractNoResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetContractParamaters">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q12="http://schemas.microsoft.com/2003/10/Serialization/" minOccurs="0" name="contractGuid" type="q12:guid" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetContractParamatersResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q13="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GetContractParamatersResult" nillable="true" type="q13:ArrayOfKeyValueOfstringstring" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetConnectContractGuid">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="contractNo" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetConnectContractGuidResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q14="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GetConnectContractGuidResult" nillable="true" type="q14:ArrayOfKeyValueOfstringguid" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCurrentContractShippingStatus">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q15="http://schemas.microsoft.com/2003/10/Serialization/" minOccurs="0" name="contractGuid" type="q15:guid" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCurrentContractShippingStatusResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetCurrentContractShippingStatusResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetKeyParameterName">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetKeyParameterNameResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetKeyParameterNameResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetParameterDesAndValueDes">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q16="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="paraAndValue" nillable="true" type="q16:ArrayOfKeyValueOfstringstring" />
        <xs:element minOccurs="0" name="equipmentType" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetParameterDesAndValueDesResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q17="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" minOccurs="0" name="GetParameterDesAndValueDesResult" nillable="true" type="q17:ArrayOfParameter" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetPCBaseInfo">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="currentPC" nillable="true" type="xs:string" />
        <xs:element xmlns:q18="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" minOccurs="0" name="pcBaseInfo" nillable="true" type="q18:PCBaseInfo" />
        <xs:element xmlns:q19="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="existPCBaseInfo" nillable="true" type="q19:ArrayOfstring" />
        <xs:element minOccurs="0" name="testRunDllFindType" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetPCBaseInfoResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q20="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" minOccurs="0" name="GetPCBaseInfoResult" nillable="true" type="q20:ArrayOfPCBaseInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateEdsTaskInfo">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q21="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsEpdTaskInfo" nillable="true" type="q21:EdsEpdTaskInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdateEdsTaskInfoResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveTestDrawingParamLst">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q22="http://schemas.microsoft.com/2003/10/Serialization/" minOccurs="0" name="epdTaskInfoId" type="q22:guid" />
        <xs:element minOccurs="0" name="drawingParams" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveTestDrawingParamLstResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="CPARAGetDLL">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="drawingNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="runTimepoint" nillable="true" type="xs:dateTime" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CPARAGetDLLResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="CPARAGetDLLResult" nillable="true" type="xs:base64Binary" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAllPrivateParas">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="drawingNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="calculateType" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAllPrivateParasResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q23="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GetAllPrivateParasResult" nillable="true" type="q23:ArrayOfKeyValueOfstringstring" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetGlobalParas">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="drawingNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="calculateType" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetGlobalParasResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q24="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GetGlobalParasResult" nillable="true" type="q24:ArrayOfKeyValueOfstringstring" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetBacklogRegula">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="contractNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="runTimePoint" nillable="true" type="xs:dateTime" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetBacklogRegulaResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q25="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetBacklogRegulaResult" nillable="true" type="q25:ArrayOfEdsBackLogRegulaInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="InsertNonStandardTaskItem">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q26="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="items" nillable="true" type="q26:ArrayOfEdsNonstandardTaskItem" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="InsertNonStandardTaskItemResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="InsertNonStandardTaskItemResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddEdsTaskParaRef">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q27="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="ddsTaskParaRefs" nillable="true" type="q27:ArrayOfEdsTaskParaRef" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddEdsTaskParaRefResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="AddEdsTaskParaRefResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsTaskParaRef">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q28="http://schemas.microsoft.com/2003/10/Serialization/" minOccurs="0" name="taskguid" type="q28:guid" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsTaskParaRefResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q29="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetEdsTaskParaRefResult" nillable="true" type="q29:ArrayOfEdsTaskParaRef" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAllPara">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="drawingNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="calculateType" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAllParaResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q30="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetAllParaResult" nillable="true" type="q30:ArrayOfEdsParameterSO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CountForSql">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="sql" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="calculateType" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CountForSqlResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="CountForSqlResult" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDrawingVersion">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="drawingNO" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="calculateType" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDrawingVersionResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q31="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetDrawingVersionResult" nillable="true" type="q31:EdsDrawingVersionSO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="QueryProjectMehtod">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="queryContent" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="strCondition" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="methodName" nillable="true" type="xs:string" />
        <xs:element xmlns:q32="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="objPara" nillable="true" type="q32:ArrayOfanyType" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="QueryProjectMehtodResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="QueryProjectMehtodResult" nillable="true" type="xs:anyType" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetProjectInfo">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="contractNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="calculateType" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="taskGuid" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetProjectInfoResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q33="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GetProjectInfoResult" nillable="true" type="q33:ArrayOfKeyValueOfstringstring" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDensity">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="partSuffix" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="calculateType" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDensityResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetDensityResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTaskInfoViewWhere">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pridecate" nillable="true" type="xs:string" />
        <xs:element xmlns:q34="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.Common" minOccurs="0" name="parameters" nillable="true" type="q34:ArrayOfESqlParameter" />
        <xs:element minOccurs="0" name="isTest" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTaskInfoViewWhereResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q35="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" minOccurs="0" name="GetTaskInfoViewWhereResult" nillable="true" type="q35:ArrayOfTaskInfoView" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdatePartProp">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q36="http://schemas.microsoft.com/2003/10/Serialization/" minOccurs="0" name="taskId" type="q36:guid" />
        <xs:element xmlns:q37="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="partList" nillable="true" type="q37:ArrayOfKeyValueOfstringArrayOfKeyValueOfstringstringty7Ep6D1" />
        <xs:element minOccurs="0" name="isTest" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdatePartPropResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SumMassProperty">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q38="http://schemas.microsoft.com/2003/10/Serialization/" minOccurs="0" name="taskId" type="q38:guid" />
        <xs:element minOccurs="0" name="isTest" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SumMassPropertyResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsMOFContractList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="contractNo" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsMOFContractListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q39="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetEdsMOFContractListResult" nillable="true" type="q39:ArrayOfEDSMOFContractInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddUnstatndDrawingVerionSO">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q40="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="drawingNOList" nillable="true" type="q40:ArrayOfstring" />
        <xs:element xmlns:q41="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="drawingNoMappings" nillable="true" type="q41:ArrayOfKeyValueOfstringstring" />
        <xs:element minOccurs="0" name="creater" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddUnstatndDrawingVerionSOResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="AddTaskQueue">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q42="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="taskInfo" nillable="true" type="q42:EdsTaskInfoWS" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddTaskQueueResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="FreezeTask">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="isFreeze" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="FreezeTaskResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetNotFinishTask">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetNotFinishTaskResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q43="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetNotFinishTaskResult" nillable="true" type="q43:ArrayOfEdsTaskInfoWS" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetNumber">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetNumberResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="GetNumberResult" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetOnLineWorkStations">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetOnLineWorkStationsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q44="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" minOccurs="0" name="GetOnLineWorkStationsResult" nillable="true" type="q44:ArrayOfWorkstation" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetQueues">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetQueuesResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q45="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetQueuesResult" nillable="true" type="q45:ArrayOfEdsTaskInfoWS" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetRuningTasks">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetRuningTasksResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q46="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetRuningTasksResult" nillable="true" type="q46:ArrayOfEdsTaskInfoWS" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTaskFromQueue">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTaskFromQueueResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q47="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetTaskFromQueueResult" nillable="true" type="q47:EdsTaskInfoWS" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsDllFilePrefab">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="childCop" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="drawingNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="pcNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="dllType" nillable="true" type="xs:int" />
        <xs:element minOccurs="0" name="runTimePoint" type="xs:dateTime" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsDllFilePrefabResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q48="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetEdsDllFilePrefabResult" nillable="true" type="q48:EdsDllFilePrefab" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReportWorkStationLive">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q49="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" minOccurs="0" name="workstation" nillable="true" type="q49:Workstation" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ReportWorkStationLiveResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveTaskBomStream">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q50="http://schemas.microsoft.com/Message" name="bomsStream" type="q50:StreamBody" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveTaskBomStreamResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveTaskComplete">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q51="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="taskComplete" nillable="true" type="q51:EdsEpdTaskComplete" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveTaskCompleteResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveTaskInfo">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q52="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="taskInfo" nillable="true" type="q52:EdsEpdTaskInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveTaskInfoResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveTaskInfoWS">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q53="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="taskInfo" nillable="true" type="q53:EdsTaskInfoWS" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveTaskInfoWSResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEpdTaskInfoWhere">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pridecate" nillable="true" type="xs:string" />
        <xs:element xmlns:q54="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.Common" minOccurs="0" name="parameters" nillable="true" type="q54:ArrayOfESqlParameter" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEpdTaskInfoWhereResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q55="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetEpdTaskInfoWhereResult" nillable="true" type="q55:ArrayOfEdsEpdTaskInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsTaskInfoWSWhere">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pridecate" nillable="true" type="xs:string" />
        <xs:element xmlns:q56="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.Common" minOccurs="0" name="parameters" nillable="true" type="q56:ArrayOfESqlParameter" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsTaskInfoWSWhereResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q57="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetEdsTaskInfoWSWhereResult" nillable="true" type="q57:ArrayOfEdsTaskInfoWS" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEpdTaskCompleteWhere">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pridecate" nillable="true" type="xs:string" />
        <xs:element xmlns:q58="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.Common" minOccurs="0" name="parameters" nillable="true" type="q58:ArrayOfESqlParameter" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEpdTaskCompleteWhereResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q59="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetEpdTaskCompleteWhereResult" nillable="true" type="q59:ArrayOfEdsEpdTaskComplete" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEpdTaskCompletesForID">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q60="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="taskIds" nillable="true" type="q60:ArrayOfguid" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEpdTaskCompletesForIDResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q61="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GetEpdTaskCompletesForIDResult" nillable="true" type="q61:ArrayOfKeyValueOfguidint" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsCompleteTaskWSWhere">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pridecate" nillable="true" type="xs:string" />
        <xs:element xmlns:q62="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.Common" minOccurs="0" name="parameters" nillable="true" type="q62:ArrayOfESqlParameter" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsCompleteTaskWSWhereResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q63="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetEdsCompleteTaskWSWhereResult" nillable="true" type="q63:ArrayOfEdsCompleteTaskWS" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveTaskBom">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q64="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" minOccurs="0" name="bom" nillable="true" type="q64:BOM" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveTaskBomResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTaskBom">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q65="http://schemas.microsoft.com/2003/10/Serialization/" minOccurs="0" name="taskID" type="q65:guid" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTaskBomResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q66="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" minOccurs="0" name="GetTaskBomResult" nillable="true" type="q66:BOM" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTaskBomSO">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q67="http://schemas.microsoft.com/2003/10/Serialization/" minOccurs="0" name="taskID" type="q67:guid" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTaskBomSOResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q68="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" minOccurs="0" name="GetTaskBomSOResult" nillable="true" type="q68:BOMSO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetProgramCode">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pcNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="drawingNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fileType" type="xs:int" />
        <xs:element minOccurs="0" name="childCop" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fileFlag" type="xs:int" />
        <xs:element minOccurs="0" name="isTest" type="xs:boolean" />
        <xs:element minOccurs="0" name="autoId" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetProgramCodeResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q69="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetProgramCodeResult" nillable="true" type="q69:EdsDllFileHis" />
        <xs:element minOccurs="0" name="isPrefab" nillable="true" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveDllAndCode">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="autoId" type="xs:int" />
        <xs:element minOccurs="0" name="isPrefab" nillable="true" type="xs:boolean" />
        <xs:element minOccurs="0" name="dll" nillable="true" type="xs:base64Binary" />
        <xs:element minOccurs="0" name="programCode" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveDllAndCodeResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="SaveDllAndCodeResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDLLForTest">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pcNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="drawingNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="childCop" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fileFlag" type="xs:int" />
        <xs:element xmlns:q70="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" minOccurs="0" name="pcLst" nillable="true" type="q70:ArrayOfPCBaseInfo" />
        <xs:element minOccurs="0" name="partNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="runTimePoint" type="xs:dateTime" />
        <xs:element minOccurs="0" name="MP" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDLLForTestResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q71="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GetDLLForTestResult" nillable="true" type="q71:ArrayOfKeyValueOfNullableOfintbase64BinaryRDHGY3MA" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDLLForTestTwo">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="pcNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="drawingNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="childCop" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fileFlag" type="xs:int" />
        <xs:element xmlns:q72="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" minOccurs="0" name="pcLst" nillable="true" type="q72:ArrayOfPCBaseInfo" />
        <xs:element minOccurs="0" name="partNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="runTimePoint" type="xs:dateTime" />
        <xs:element minOccurs="0" name="MP" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDLLForTestTwoResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q73="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GetDLLForTestTwoResult" nillable="true" type="q73:ArrayOfKeyValueOfNullableOfintEdsDllFileHis63Db_SSqR" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDLLForHis">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="drawingNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="childCop" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fileFlag" type="xs:int" />
        <xs:element minOccurs="0" name="runTimePoint" type="xs:dateTime" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDLLForHisResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q74="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GetDLLForHisResult" nillable="true" type="q74:ArrayOfKeyValueOfNullableOfintbase64BinaryRDHGY3MA" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDLLForFormally">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="drawingNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="childCop" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fileFlag" type="xs:int" />
        <xs:element minOccurs="0" name="runTimePoint" type="xs:dateTime" />
        <xs:element minOccurs="0" name="caNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="partNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="MP" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDLLForFormallyResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q75="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GetDLLForFormallyResult" nillable="true" type="q75:ArrayOfKeyValueOfNullableOfintEdsDllFileHis63Db_SSqR" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDLLForFormallyTwo">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="drawingNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="childCop" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="fileFlag" type="xs:int" />
        <xs:element minOccurs="0" name="runTimePoint" type="xs:dateTime" />
        <xs:element minOccurs="0" name="caNo" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="partNumber" nillable="true" type="xs:string" />
        <xs:element minOccurs="0" name="MP" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDLLForFormallyTwoResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q76="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GetDLLForFormallyTwoResult" nillable="true" type="q76:ArrayOfKeyValueOfNullableOfintEdsDllFileHis63Db_SSqR" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsParameterSO">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="name" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetEdsParameterSOResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q77="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="GetEdsParameterSOResult" nillable="true" type="q77:EdsParameterSO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsBomOrgs">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q78="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsBomOrgs" nillable="true" type="q78:ArrayOfEdsBomOrg" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsBomOrgsResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsBomRootOrgs">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q79="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsBomRootOrgs" nillable="true" type="q79:ArrayOfEdsBomRootOrg" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsBomRootOrgsResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsPartOrgs">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q80="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsPartOrgs" nillable="true" type="q80:ArrayOfEdsPartOrg" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsPartOrgsResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsPartToRoutings">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q81="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsPartToRoutings" nillable="true" type="q81:ArrayOfEdsPartToRouting" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsPartToRoutingsResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsFMOrgs">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q82="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsFMOrgs" nillable="true" type="q82:ArrayOfEdsFMOrg" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsFMOrgsResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsBomOrgSOs">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q83="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsBomOrgSOs" nillable="true" type="q83:ArrayOfEdsBomOrgSO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsBomOrgSOsResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsBomRootOrgSOs">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q84="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsBomRootOrgSOs" nillable="true" type="q84:ArrayOfEdsBomRootOrgSO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsBomRootOrgSOsResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsPartOrgSOs">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q85="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsPartOrgSOs" nillable="true" type="q85:ArrayOfEdsPartOrgSO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsPartOrgSOsResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsPartToRoutingSOs">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q86="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsPartToRoutingSOs" nillable="true" type="q86:ArrayOfEdsPartToRoutingOrgSO" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsPartToRoutingSOsResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsPartSupplierOrgs">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q87="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="edsPartSupplierOrg" nillable="true" type="q87:ArrayOfEdsPartSupplierOrg" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveEdsPartSupplierOrgsResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
</xs:schema>
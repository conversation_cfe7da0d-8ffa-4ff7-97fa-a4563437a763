<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:tns="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.Common" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.Common" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:complexType name="ArrayOfESqlParameter">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="ESqlParameter" nillable="true" type="tns:ESqlParameter" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfESqlParameter" nillable="true" type="tns:ArrayOfESqlParameter" />
  <xs:complexType name="ESqlParameter">
    <xs:sequence>
      <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Value" nillable="true" type="xs:anyType" />
    </xs:sequence>
    <xs:attribute ref="ser:Id" />
    <xs:attribute ref="ser:Ref" />
  </xs:complexType>
  <xs:element name="ESqlParameter" nillable="true" type="tns:ESqlParameter" />
</xs:schema>
# CADTaskServer 接口测试用例

## 1. 接口测试概述

### 1.1 测试目标
验证CADTaskServer系统提供的WCF服务接口的功能正确性、参数验证、返回值准确性、异常处理和性能表现。

### 1.2 接口列表
基于ICADWCFService接口定义，主要测试以下接口：
- RunCADCode - CAD代码执行接口
- TestServer - 服务器连通性测试接口
- SetCustomPropertyInfo - 设置自定义属性接口
- GetPropertys - 获取属性信息接口
- GetRefconfigList - 获取引用配置列表接口
- GetFeatureList - 获取特征列表接口
- GetDimlist - 获取尺寸列表接口
- GetChildPart - 获取子部件接口
- GetReferenceList - 获取引用列表接口
- CloseModel - 关闭模型接口

### 1.3 测试环境
- **协议**: TCP/IP
- **绑定**: NetTcpBinding
- **安全模式**: None
- **传输模式**: Streamed
- **测试工具**: WCF Test Client, Postman, 自定义测试客户端

## 2. RunCADCode接口测试用例

### TC_WCF_001: RunCADCode正常执行
**接口**: RunCADCode(CADTaskCode cadTask)
**测试目标**: 验证CAD代码正常执行流程

**测试步骤**:
1. 构造有效的CADTaskCode对象
2. 调用RunCADCode接口
3. 验证返回结果

**测试数据**:
```json
{
  "TaskId": "12345678-1234-1234-1234-123456789012",
  "SortId": 1,
  "PartId": 100,
  "CadCodeId": 1200,
  "Para1": "TestDrawing.dwg",
  "Para2": "DefaultConfig",
  "Para3": "TestModel"
}
```

**预期结果**:
- 接口调用成功，返回状态码200
- 返回的CADTaskCode对象包含执行结果
- Finished状态正确更新
- 执行时间合理（< 30秒）

### TC_WCF_002: RunCADCode参数验证
**测试目标**: 验证接口参数验证机制

**测试步骤**:
1. 传入null参数
2. 传入无效的TaskId
3. 传入无效的CadCodeId
4. 验证参数验证结果

**测试数据**:
```json
// 测试用例1: null参数
null

// 测试用例2: 无效TaskId
{
  "TaskId": "invalid-guid",
  "CadCodeId": 1200
}

// 测试用例3: 无效CadCodeId
{
  "TaskId": "12345678-1234-1234-1234-123456789012",
  "CadCodeId": -1
}
```

**预期结果**:
- null参数返回适当的错误信息
- 无效GUID格式被拒绝
- 无效CadCodeId被拒绝
- 错误信息明确具体

### TC_WCF_003: RunCADCode异常处理
**测试目标**: 验证接口异常处理机制

**测试步骤**:
1. 传入导致内部异常的参数
2. 模拟系统资源不足
3. 模拟文件访问异常
4. 验证异常处理

**预期结果**:
- 内部异常被正确捕获
- 返回友好的错误信息
- 系统不会崩溃
- 异常信息记录到日志

### TC_WCF_004: RunCADCode性能测试
**测试目标**: 验证接口性能表现

**测试步骤**:
1. 单次调用性能测试
2. 并发调用性能测试
3. 大数据量处理测试
4. 长时间运行稳定性测试

**预期结果**:
- 单次调用响应时间 < 5秒
- 支持至少10个并发调用
- 大数据量处理不超时
- 长时间运行无内存泄漏

### TC_WCF_005: RunCADCode边界测试
**测试目标**: 验证接口边界条件处理

**测试步骤**:
1. 测试最大参数长度
2. 测试特殊字符处理
3. 测试Unicode字符
4. 测试空字符串参数

**测试数据**:
```json
{
  "Para1": "很长的字符串..." // 4000字符
  "Para2": "特殊字符!@#$%^&*()",
  "Para3": "Unicode测试中文日本語",
  "Para4": ""
}
```

**预期结果**:
- 长字符串正确处理或适当截断
- 特殊字符不影响处理
- Unicode字符正确显示
- 空字符串不导致异常

## 3. TestServer接口测试用例

### TC_WCF_006: TestServer连通性测试
**接口**: TestServer()
**测试目标**: 验证服务器连通性检测功能

**测试步骤**:
1. 在正常状态下调用TestServer
2. 验证返回值
3. 检查响应时间

**预期结果**:
- 返回true表示服务正常
- 响应时间 < 1秒
- 无异常抛出

### TC_WCF_007: TestServer服务异常测试
**测试目标**: 验证服务异常时的检测能力

**测试步骤**:
1. 模拟服务内部异常
2. 调用TestServer接口
3. 验证异常检测

**预期结果**:
- 能够检测到服务异常
- 返回false或抛出异常
- 错误信息明确

### TC_WCF_008: TestServer并发测试
**测试目标**: 验证并发连通性检测

**测试步骤**:
1. 多个客户端同时调用TestServer
2. 验证并发处理能力
3. 检查响应一致性

**预期结果**:
- 支持多客户端并发检测
- 所有调用都能正确响应
- 响应结果一致

## 4. 属性操作接口测试用例

### TC_WCF_009: GetPropertys正常获取
**接口**: GetPropertys(string filePath, string refConfig)
**测试目标**: 验证属性信息获取功能

**测试步骤**:
1. 传入有效的文件路径和配置
2. 调用GetPropertys接口
3. 验证返回的属性字典

**测试数据**:
```
filePath: "D:\Models\TestPart.prt"
refConfig: "DefaultConfig"
```

**预期结果**:
- 返回Dictionary<string, string>格式的属性信息
- 属性键值对正确
- 属性值类型正确
- 无重复属性键

### TC_WCF_010: GetPropertys文件不存在
**测试目标**: 验证文件不存在时的处理

**测试步骤**:
1. 传入不存在的文件路径
2. 调用GetPropertys接口
3. 验证错误处理

**测试数据**:
```
filePath: "D:\NonExistent\File.prt"
refConfig: "DefaultConfig"
```

**预期结果**:
- 返回空字典或null
- 记录文件不存在的错误
- 不抛出未处理异常

### TC_WCF_011: SetCustomPropertyInfo正常设置
**接口**: SetCustomPropertyInfo(string filePath, string configName, Dictionary<string, string> propertyInfo)
**测试目标**: 验证自定义属性设置功能

**测试步骤**:
1. 准备属性字典数据
2. 调用SetCustomPropertyInfo接口
3. 验证属性设置结果

**测试数据**:
```json
{
  "filePath": "D:\Models\TestPart.prt",
  "configName": "DefaultConfig",
  "propertyInfo": {
    "Material": "Steel",
    "Weight": "10.5",
    "Description": "Test Part"
  }
}
```

**预期结果**:
- 属性成功设置到模型
- 设置后可以通过GetPropertys获取
- 原有属性不受影响
- 操作完成无异常

### TC_WCF_012: SetCustomPropertyInfo参数验证
**测试目标**: 验证属性设置的参数验证

**测试步骤**:
1. 传入null属性字典
2. 传入空属性字典
3. 传入无效属性值
4. 验证参数验证机制

**预期结果**:
- null字典被正确处理
- 空字典不导致异常
- 无效值被拒绝或转换
- 参数验证错误信息明确

## 5. 模型信息获取接口测试用例

### TC_WCF_013: GetRefconfigList正常获取
**接口**: GetRefconfigList(string filePath, string configName)
**测试目标**: 验证引用配置列表获取功能

**测试步骤**:
1. 传入有效的模型文件路径
2. 调用GetRefconfigList接口
3. 验证返回的配置列表

**预期结果**:
- 返回List<string>格式的配置列表
- 配置名称正确
- 列表顺序合理
- 无重复配置项

### TC_WCF_014: GetFeatureList正常获取
**接口**: GetFeatureList(string filePath, string configName)
**测试目标**: 验证特征列表获取功能

**测试步骤**:
1. 打开包含特征的模型文件
2. 调用GetFeatureList接口
3. 验证特征列表内容

**预期结果**:
- 返回完整的特征列表
- 特征名称正确
- 特征类型信息准确
- 列表格式正确

### TC_WCF_015: GetDimlist正常获取
**接口**: GetDimlist(string filePath, string configName)
**测试目标**: 验证尺寸列表获取功能

**测试步骤**:
1. 打开包含尺寸的模型文件
2. 调用GetDimlist接口
3. 验证尺寸信息

**预期结果**:
- 返回完整的尺寸列表
- 尺寸名称和值正确
- 尺寸单位信息准确
- 数据格式正确

### TC_WCF_016: GetChildPart正常获取
**接口**: GetChildPart(string filePath, string configName)
**测试目标**: 验证子部件列表获取功能

**测试步骤**:
1. 打开装配体文件
2. 调用GetChildPart接口
3. 验证子部件信息

**预期结果**:
- 返回所有子部件列表
- 子部件路径正确
- 层级关系准确
- 部件状态信息正确

### TC_WCF_017: GetReferenceList正常获取
**接口**: GetReferenceList(string filePath, string configName)
**测试目标**: 验证引用列表获取功能

**测试步骤**:
1. 打开包含引用的模型文件
2. 调用GetReferenceList接口
3. 验证引用信息

**预期结果**:
- 返回Dictionary<string, Dictionary<string, string>>格式
- 引用关系正确
- 引用属性完整
- 数据结构正确

## 6. 模型操作接口测试用例

### TC_WCF_018: CloseModel正常关闭
**接口**: CloseModel(string filePath)
**测试目标**: 验证模型关闭功能

**测试步骤**:
1. 打开测试模型文件
2. 调用CloseModel接口
3. 验证模型关闭状态

**预期结果**:
- 模型成功关闭
- 释放相关资源
- 后续操作不受影响
- 无内存泄漏

### TC_WCF_019: CloseModel重复关闭
**测试目标**: 验证重复关闭模型的处理

**测试步骤**:
1. 关闭已经关闭的模型
2. 验证重复操作处理
3. 检查系统稳定性

**预期结果**:
- 重复关闭不导致异常
- 系统状态保持稳定
- 返回适当的状态信息

### TC_WCF_020: CloseModel文件不存在
**测试目标**: 验证关闭不存在文件的处理

**测试步骤**:
1. 尝试关闭不存在的文件
2. 验证错误处理机制
3. 检查异常处理

**预期结果**:
- 优雅处理文件不存在情况
- 返回明确的错误信息
- 不影响其他操作

## 7. 接口安全测试用例

### TC_WCF_021: 接口访问权限验证
**测试目标**: 验证接口访问权限控制

**测试步骤**:
1. 使用未授权客户端访问接口
2. 验证权限控制机制
3. 检查安全响应

**预期结果**:
- 未授权访问被拒绝
- 返回适当的安全错误
- 不泄露敏感信息

### TC_WCF_022: 输入数据安全验证
**测试目标**: 验证输入数据的安全性检查

**测试步骤**:
1. 传入恶意构造的参数
2. 尝试路径遍历攻击
3. 验证输入过滤机制

**测试数据**:
```
恶意路径: "../../windows/system32/config"
SQL注入: "'; DROP TABLE Users; --"
脚本注入: "<script>alert('xss')</script>"
```

**预期结果**:
- 恶意输入被正确过滤
- 路径遍历攻击被阻止
- 注入攻击无效
- 系统安全不受影响

### TC_WCF_023: 数据传输安全
**测试目标**: 验证数据传输的安全性

**测试步骤**:
1. 监控网络传输数据
2. 检查数据加密状态
3. 验证敏感信息保护

**预期结果**:
- 敏感数据传输加密
- 无明文密码传输
- 通信协议安全

## 8. 接口性能测试用例

### TC_WCF_024: 接口响应时间测试
**测试目标**: 验证各接口的响应时间性能

**测试步骤**:
1. 单独测试每个接口响应时间
2. 记录平均响应时间
3. 分析性能瓶颈

**性能标准**:
- TestServer: < 1秒
- GetPropertys: < 3秒
- RunCADCode: < 30秒
- 其他接口: < 5秒

### TC_WCF_025: 接口并发性能测试
**测试目标**: 验证接口并发处理能力

**测试步骤**:
1. 模拟多客户端并发访问
2. 逐步增加并发数量
3. 监控系统资源使用

**预期结果**:
- 支持至少10个并发连接
- 并发时响应时间合理
- 系统资源使用稳定
- 无死锁或阻塞

### TC_WCF_026: 接口压力测试
**测试目标**: 验证接口在高负载下的稳定性

**测试步骤**:
1. 持续高频率调用接口
2. 监控系统稳定性
3. 检查内存和CPU使用

**预期结果**:
- 高负载下系统稳定
- 无内存泄漏
- 响应时间在可接受范围
- 错误率低于1%

## 9. 接口兼容性测试用例

### TC_WCF_027: 客户端兼容性测试
**测试目标**: 验证不同客户端的兼容性

**测试步骤**:
1. 使用不同版本的WCF客户端
2. 测试.NET不同版本的兼容性
3. 验证跨平台兼容性

**预期结果**:
- 支持.NET Framework 4.0+
- 向后兼容旧版本客户端
- 跨平台调用正常

### TC_WCF_028: 协议兼容性测试
**测试目标**: 验证不同传输协议的兼容性

**测试步骤**:
1. 测试TCP绑定
2. 测试HTTP绑定（如果支持）
3. 验证协议切换

**预期结果**:
- TCP协议稳定可靠
- 协议切换无数据丢失
- 性能表现符合预期

## 10. 接口错误处理测试用例

### TC_WCF_029: 网络异常处理
**测试目标**: 验证网络异常时的处理机制

**测试步骤**:
1. 模拟网络中断
2. 模拟网络延迟
3. 验证超时处理

**预期结果**:
- 网络异常被正确检测
- 超时机制正常工作
- 客户端收到明确错误信息

### TC_WCF_030: 服务异常恢复
**测试目标**: 验证服务异常后的恢复能力

**测试步骤**:
1. 模拟服务崩溃
2. 重启服务
3. 验证服务恢复

**预期结果**:
- 服务能够正常重启
- 客户端能够重新连接
- 数据状态保持一致

---

**文档版本**: v1.0
**编写日期**: 2024年1月
**编写人**: 测试团队
**审核人**: 接口测试专家

﻿using Neuxa.EDS.ExcelAddIn.LanguagePackage;
using System;
using System.Windows.Forms;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;
using Zxtech.EDS.ExcelAddIn.Core;
using Zxtech.EGI.PlatformService.Contract;

namespace Zxtech.CADTaskServer
{
    public partial class Frm_Login : Form
    {
        public Frm_Login()
        {
            InitializeComponent();
        }

        public string LoginUrl { get; set; }

        public void LoadUserName(string userName)
        {
            if (userName == null)
            {
                textBox1.Enabled = true;
                return;
            }
            textBox1.Text = userName;
        }


        private void Frm_Login_Load(object sender, EventArgs e)
        {
            
        }

        private void button1_Click(object sender, EventArgs e)
        {
            try
            {
                RestClientPoxy.SetDefaultSessionId(null);
                RestClientPoxy.SetDefaultJSessionId(null);
                RestClientPoxy.SetDefaultJwtAuth(null);

                if (string.IsNullOrEmpty(LoginUrl))
                {
                    DBServiceClient.Instance.EGIRbacServiceClient.Login(textBox1.Text, textBox2.Text);
                }
                else
                {
                    DBServiceClient.Instance.EGIRbacServiceClient.Login1(textBox1.Text, textBox2.Text, LoginUrl);
                }

                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception exception)
            {
                //"登录失败!"
                MessageBox.Show(LanguageHelper.GetString("LoginFailed") + exception.ToString());
            }

        }

        private void button2_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }
    }
}

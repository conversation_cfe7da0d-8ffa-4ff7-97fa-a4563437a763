﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Xml;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;
using Zxtech.CADTaskServer.Properties;

namespace Zxtech.CADTaskServer
{
    public partial class FormParameterSet : Form
    {
        string _StationType = string.Empty;
        public FormParameterSet(string Language)
        {
            if (!string.IsNullOrEmpty(Language))
            {
                Thread.CurrentThread.CurrentUICulture = new CultureInfo(Language);
                Resources.Culture = Thread.CurrentThread.CurrentUICulture;
            }

            InitializeComponent();
        }

        public event Action<FormParameterSet> RefreshConfigHanlder;
        private void button3_Click(object sender, EventArgs e)
        {
            folderBrowserDialog1.Description = LanguageHelper.GetString("NodifySelectDirectory");
            folderBrowserDialog1.RootFolder = Environment.SpecialFolder.MyComputer;
            folderBrowserDialog1.ShowNewFolderButton = true;
            if (textBox_PdsPath.Text.Length > 0) folderBrowserDialog1.SelectedPath = textBox_PdsPath.Text;
            if (folderBrowserDialog1.ShowDialog() == DialogResult.OK)
            {
                textBox_PdsPath.Text = folderBrowserDialog1.SelectedPath;
            }
        }
        private void button4_Click(object sender, EventArgs e)
        {
            folderBrowserDialog2.Description = LanguageHelper.GetString("NodifySelectDirectory");
            folderBrowserDialog2.RootFolder = Environment.SpecialFolder.MyComputer;
            folderBrowserDialog2.ShowNewFolderButton = true;
            if (textBox_WorkPath.Text.Length > 0) folderBrowserDialog2.SelectedPath = textBox_WorkPath.Text;
            if (folderBrowserDialog2.ShowDialog() == DialogResult.OK)
            {
                textBox_WorkPath.Text = folderBrowserDialog2.SelectedPath;
            }
        }

        /// <summary>
        /// 保存
        /// </summary>
        /// <param name="sender"></param> 
        /// <param name="e"></param>
        private void buttonSave_Click(object sender, EventArgs e)
        {
            string xmlpath = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location).TrimEnd('\\') + "\\CADTaskClientConfig.xml";
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(File.ReadAllText(xmlpath, Encoding.UTF8));

            if (doc != null)
            {
                //モデルテンプレート	格納場所 
                XmlNode xlPdsPath = LoadXmlNodeSet("PdsPath", doc);
                xlPdsPath.InnerText = textBox_PdsPath.Text;

                //モデル	出力場所
                XmlNode xlWorkPath = LoadXmlNodeSet("WorkPath", doc);
                xlWorkPath.InnerText = textBox_WorkPath.Text;

                //サーバ	IP	アドレス
                XmlNode xlEGIServicex = LoadXmlNodeSet("EGIService", doc);
                //xlEGIServicex.InnerText =  textBox_SendbackUrl.Text;
                xlEGIServicex.InnerText = textBox_EgiUrl.Text;
                //コールバックパース
                XmlNode xlSendbackUrl = LoadXmlNodeSet("SendbackUrl", doc);
                //xlSendbackUrl.InnerText = textBox_EgiUrl.Text;
                if (xlSendbackUrl!=null)//配置文件中可能没有回调地址, 回调地址是由ecs传入的
                {
                    xlSendbackUrl.InnerText = textBox_SendbackUrl.Text;
                }



                if (_StationType.Equals(StationTypeList.AutoCAD))
                {
                    //結果	をMongodb	に	出力要否
                    XmlNode xlUseDocumentDbResult = LoadXmlNodeSet("UseDocumentDbResult", doc);
                    xlUseDocumentDbResult.InnerText = checkBox_UseDocumentDbResult.Checked == true ? "true" : "false";

                    //ブロック	分解要否
                    XmlNode xlMakeProjectMap = LoadXmlNodeSet("MakeProjectMap", doc);
                    xlMakeProjectMap.InnerText = checkBox_MakeProjectMap.Checked == true ? "true" : "false";

                    //  打断尺寸线
                    XmlNode xlIsCutLine = LoadXmlNodeSet("IsCutline", doc);
                    xlIsCutLine.InnerText = checkBox_BreakDimensionLines.Checked == true ? "true" : "false";
                }

                if (_StationType.Equals(StationTypeList.SolidWorks))
                {
                    //伸び	線出力要否,展开图是否显示折弯线
                    XmlNode xlIsDXFBendLines = LoadXmlNodeSet("IsDXFBendLines", doc);
                    if (xlIsDXFBendLines != null)
                    {
                        xlIsDXFBendLines.InnerText = checkBox_IsDXFBendLines.Checked == true ? "true" : "false";
                    }
                    //板金部品	の	厚さ	出力要否
                    XmlNode xlIsDXFReadThickness = LoadXmlNodeSet("IsDXFReadThickness", doc);
                    if (xlIsDXFReadThickness != null)
                    {
                        xlIsDXFReadThickness.InnerText = checkBox_IsDXFReadThickness.Checked == true ? "true" : "false";
                    }
                }

                if (!_StationType.Equals(StationTypeList.Revit))
                {
                    //DXF	ファイル	出力要否
                    XmlNode xlMakeSpreadMap = LoadXmlNodeSet("MakeSpreadMap", doc);
                    xlMakeSpreadMap.InnerText = checkBox_MakeSpreadMap.Checked == true ? "true" : "false";

                    //DWG	ファイル	出力要否
                    XmlNode xlMakeDWG = LoadXmlNodeSet("MakeDWG", doc);
                    xlMakeDWG.InnerText = checkBox_MakeDWG.Checked == true ? "true" : "false";

                    //PDF	ファイル	出力要否
                    XmlNode xlMakePDF = LoadXmlNodeSet("MakePDF", doc);
                    xlMakePDF.InnerText = checkBox_MakePDF.Checked == true ? "true" : "false";
                }


                //出力	ファイル	種類
                XmlNode xlAttachmentType = LoadXmlNodeSet("AttachmentType", doc);

                xlAttachmentType.InnerText = textBox_OutputFileType.Text;


                //タスク	種類
                XmlNode xlTaskType = LoadXmlNodeSet("TaskType", doc);
                foreach (RadioButton item in groupBox_TaskType.Controls)
                {
                    if (item.Checked)
                    {
                        //"测试"
                        if (item.Text == LanguageHelper.GetString("Test"))
                        {
                            xlTaskType.InnerText = "100";
                        }
                        else
                        {
                            switch (_StationType)
                            {
                                case StationTypeList.AutoCAD:
                                    xlTaskType.InnerText = "600";
                                    break;
                                case StationTypeList.Revit:
                                    xlTaskType.InnerText = "2200";
                                    break;
                                case StationTypeList.SolidWorks:
                                    xlTaskType.InnerText = "300";
                                    break;
                                case StationTypeList.decorate:
                                    xlTaskType.InnerText = "1200";
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }

                //サーバ	IP	アドレス	のユーザ	名
                XmlNode xlEGIUserNo = LoadXmlNodeSet("EGIUserNo", doc);
                if (xlEGIUserNo != null)
                {
                    xlEGIUserNo.InnerText = textBox_EGIUserNo.Text;
                }

                //サーバ	IP	アドレス	のパスワード
                XmlNode xlEGIPsw = LoadXmlNodeSet("EGIPsw", doc);
                if (xlEGIPsw != null)
                {
                    xlEGIPsw.InnerText = textBox_EGIPsw.Text;
                }

                //言語
                XmlNode xlLanguage = LoadXmlNodeSet("Language", doc);
                foreach (RadioButton item in groupBox_Language.Controls)
                {
                    if (item.Checked)
                    {
                        if (xlLanguage != null)
                        {
                            xlLanguage.InnerText = item.Text;
                        }
                    }
                }

                try
                {
                    doc.Save(xmlpath);
                    MessageBox.Show(LanguageHelper.GetString("Save_Success"), "", MessageBoxButtons.OK);
                    OnRefreshConfigHanlder();
                }
                catch (Exception)
                {
                    MessageBox.Show(LanguageHelper.GetString("Save_Failed"), "", MessageBoxButtons.OK);
                }
            }
        }

        /// <summary>
        /// 参数初始化显示
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FormParameterSet_Load(object sender, EventArgs e)
        {
            string xmlpath = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location).TrimEnd('\\') + "\\CADTaskClientConfig.xml";
            XmlDocument doc = new XmlDocument();
            doc.LoadXml(File.ReadAllText(xmlpath, Encoding.UTF8));
            if (doc != null)
            {
                //根据配置文件区分工作站类型
                _StationType = LoadXmlNode("StationType", doc);

                CheckSetUpContro();

                //モデルテンプレート	格納場所 
                textBox_PdsPath.Text = LoadXmlNode("PdsPath", doc);

                //モデル	出力場所
                textBox_WorkPath.Text = LoadXmlNode("WorkPath", doc);

                //サーバ	IP	アドレス
                //textBox_SendbackUrl.Text = LoadXmlNode("EGIService", doc);
                textBox_SendbackUrl.Text = LoadXmlNode("SendbackUrl", doc);
                //コールバックパース
                //textBox_EgiUrl.Text = LoadXmlNode("SendbackUrl", doc);
                textBox_EgiUrl.Text = LoadXmlNode("EGIService", doc);
                //ブロック	分解要否
                string MakeProjectMapStr = LoadXmlNode("MakeProjectMap", doc);
                if (MakeProjectMapStr != "" && MakeProjectMapStr == "true")
                {
                    checkBox_MakeProjectMap.Checked = true;
                }
                else
                {
                    checkBox_MakeProjectMap.Checked = false;
                }

                //DXF	ファイル	出力要否
                string MakeSpreadMapStr = LoadXmlNode("MakeSpreadMap", doc);
                if (MakeSpreadMapStr != "" && MakeSpreadMapStr == "true")
                {
                    checkBox_MakeSpreadMap.Checked = true;
                }
                else
                {
                    checkBox_MakeSpreadMap.Checked = false;
                }

                //伸び	線出力要否
                string IsDXFBendLinesStr = LoadXmlNode("IsDXFBendLines", doc);
                if (IsDXFBendLinesStr != "" && IsDXFBendLinesStr == "true")
                {
                    checkBox_IsDXFBendLines.Checked = true;
                }
                else
                {
                    checkBox_IsDXFBendLines.Checked = false;
                }

                //板金部品	の	厚さ	出力要否
                string IsDXFReadThicknessStr = LoadXmlNode("IsDXFReadThickness", doc);
                if (IsDXFReadThicknessStr != "" && IsDXFReadThicknessStr == "true")
                {
                    checkBox_IsDXFReadThickness.Checked = true;
                }
                else
                {
                    checkBox_IsDXFReadThickness.Checked = false;
                }

                //DWG	ファイル	出力要否
                string MakeDWGStr = LoadXmlNode("MakeDWG", doc);
                if (MakeDWGStr != "" && MakeDWGStr == "true")
                {
                    checkBox_MakeDWG.Checked = true;
                }
                else
                {
                    checkBox_MakeDWG.Checked = false;
                }

                //PDF	ファイル	出力要否
                string MakePDFStr = LoadXmlNode("MakePDF", doc);
                if (MakePDFStr != "" && MakePDFStr == "true")
                {
                    checkBox_MakePDF.Checked = true;
                }
                else
                {
                    checkBox_MakePDF.Checked = false;
                }

                //出力	ファイル	種類
                string AttachmentTypeStr = LoadXmlNode("AttachmentType", doc);
                textBox_OutputFileType.Text = AttachmentTypeStr;

                //タスク	種類
                string TaskTypeStr = LoadXmlNode("TaskType", doc);
                if (TaskTypeStr != "100")
                {
                    radioButton1.Checked = true;
                }
                else
                {
                    radioButton2.Checked = true;
                }

                //サーバ	IP	アドレス	のユーザ	名
                string EGIUserNoStr = LoadXmlNode("EGIUserNo", doc);
                textBox_EGIUserNo.Text = EGIUserNoStr;

                //サーバ	IP	アドレス	のパスワード
                string EGIPswStr = LoadXmlNode("EGIPsw", doc);
                textBox_EGIPsw.Text = EGIPswStr;

                //言語
                string LanguageStr = LoadXmlNode("Language", doc);
                if (LanguageStr == "ja-JP")
                {
                    radioButton6.Checked = true;
                }
                else if (LanguageStr == "en-US")
                {
                    radioButton5.Checked = true;
                }
                else
                {
                    radioButton3.Checked = true;
                }

                //結果	をMongodb	に	出力要否
                string UseDocumentDbResultStr = LoadXmlNode("UseDocumentDbResult", doc);
                if (UseDocumentDbResultStr != "" && UseDocumentDbResultStr == "true")
                {
                    checkBox_UseDocumentDbResult.Checked = true;
                }
                else
                {
                    checkBox_UseDocumentDbResult.Checked = false;
                }
                //是否打断autocad标注尺寸线, 土建图使用
                string isCutLine = LoadXmlNode("IsCutline", doc);
                if (isCutLine != "" && isCutLine == "true")
                {
                    checkBox_BreakDimensionLines.Checked = true;
                }
                else
                {
                    checkBox_BreakDimensionLines.Checked = false;
                }
            }
        }

        #region XML通用
        private string LoadXmlNode(string nodeStr, XmlDocument doc)
        {
            string ReturnValue = string.Empty;

            if (doc != null)
            {
                XmlNode xlValue = doc.SelectSingleNode($"/CADTaskClientConfig/{nodeStr}");
                if (xlValue != null)
                {
                    ReturnValue = xlValue.InnerText;
                }
            }

            return ReturnValue;
        }

        private XmlNode LoadXmlNodeSet(string nodeStr, XmlDocument doc)
        {
            XmlNode parentNode = doc.SelectSingleNode("/CADTaskClientConfig");
            XmlNode targetNode = parentNode.SelectSingleNode(nodeStr);
            if (targetNode == null)
            {
                targetNode = doc.CreateElement(nodeStr);
                parentNode.AppendChild(targetNode);
            }
            return targetNode;
        }

        #endregion


        private void CheckSetUpContro()
        {
            switch (_StationType)
            {
                case StationTypeList.AutoCAD:
                    checkBox_UseDocumentDbResult.Visible = true;
                    checkBox_MakeProjectMap.Visible = true;
                    checkBox_BreakDimensionLines.Visible = true;
                    checkBox_IsDXFBendLines.Visible = false;
                    checkBox_IsDXFReadThickness.Visible = false;
                    break;
                case StationTypeList.Revit:
                    checkBox_BreakDimensionLines.Visible = false;
                    this.checkBox_MakeProjectMap.Visible = false;
                    this.checkBox_MakeSpreadMap.Visible = false;
                    this.checkBox_IsDXFBendLines.Visible = false;
                    this.checkBox_IsDXFReadThickness.Visible = false;
                    this.checkBox_MakeDWG.Visible = false;
                    this.checkBox_MakePDF.Visible = false;
                    break;
                case StationTypeList.SolidWorks:
                    checkBox_UseDocumentDbResult.Visible = false;
                    checkBox_MakeProjectMap.Visible = false;
                    checkBox_BreakDimensionLines.Visible = false;
                    checkBox_IsDXFBendLines.Visible = true;
                    checkBox_IsDXFReadThickness.Visible = true;
                    tableLayoutPanel3.ColumnStyles[2].SizeType = SizeType.Absolute;
                    tableLayoutPanel3.ColumnStyles[2].Width = 0;
                    break;
                default:

                    break;

                   

                    
            }
        }

        public struct StationTypeList
        {
            public const string AutoCAD = "AutoCAD";
            public const string Revit = "Revit";
            public const string SolidWorks = "SolidWorks";
            public const string decorate = "decorate";//装潢
        }

        private void button2_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        protected virtual void OnRefreshConfigHanlder()
        {
            RefreshConfigHanlder?.Invoke(this);
        }
    }
}

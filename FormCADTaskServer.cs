﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Configuration;
using System.Data.Linq;
using System.Drawing;
using System.IO;
using System.Net;
using System.ServiceModel;
using System.ServiceModel.Description;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using Timer=System.Timers.Timer;
using Zxtech.CADTaskServer.Properties;
using System.Text.RegularExpressions;
using Zxtech.EDS.PDS.CadWsOutsideControl;
using Zxtech.PdsConstant;
using System.Diagnostics;
using System.Globalization;
using System.Runtime.Serialization;
using System.IO.Compression;
using System.Net.Sockets;
using System.Runtime.Serialization.Json;
using System.Security.Cryptography;
using System.Threading;
using System.Web.Script.Serialization;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;
using Zxtech.EDS.ExcelAddIn.Commons;
using Zxtech.EDS.ExcelAddIn.Core;
using Zxtech.EDS.ExcelAddIn.Core.Criterion;
using Zxtech.EDS.ExcelAddIn.Core.Utils;
using Zxtech.EGI.PlatformService.Contract;
using Zxtech.EGI.PlatformService.Contract.Dto;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;
using Environment = System.Environment;
using Zxtech.CADTaskServer.Contract;
using System.Data;

namespace Zxtech.CADTaskServer
{
    [Serializable]
    public partial class FormCADTaskServer : Form
    {
        public Dictionary<string, Guid?> FileGuidMap = new Dictionary<string, Guid?>();

        private readonly string basePath = Path.GetDirectoryName(System.Reflection.Assembly
            .GetExecutingAssembly().Location).TrimEnd('\\');

        //private readonly string basePath = @"D:\";
//System.IO.Path.GetDirectoryName(typeof(FormCADTaskServer).Assembly.CodeBase.Replace("file:///", ""));

        //Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
        public readonly IPlatformService CADDbConnect;
        public readonly IPlatformService EdsTaskConnect;
        public readonly IPlatformService CADDbOnLineConnect;
        public string RabbiMqFileName;

        private readonly String cfgFileName = Path.GetDirectoryName(System.Reflection.Assembly
            .GetExecutingAssembly().Location).TrimEnd('\\') + "\\CADTaskServerConfig.xml";
        public String cfgClientFileName = Path.GetDirectoryName(System.Reflection.Assembly
            .GetExecutingAssembly().Location).TrimEnd('\\') + "\\CADTaskClientConfig.xml";
        private readonly string CadWsOutConfigName = Path.GetDirectoryName(System.Reflection.Assembly
            .GetExecutingAssembly().Location).TrimEnd('\\') + "\\Zxtech.PdsCadWsOutsideControl.exe.config";
        public readonly List<string> FileTypeListToSer = new List<string>();
        private IPlatformService factory;
        public bool isAuto = false;
        private List<FileInfo> listFile;
        //private PdsUser loginUserInfo;
        private bool loginWorkstation;
        private FileStream logTxt;
        public EpdTaskInfoDto taskInfo;
        public IPlatformService UpdateModelConnect;
        public Timer timerWSTimelimit;
        private IList<int> taskTypes;
        public IList<int> subTaskTypes;
        private int taskType;
        public string taskFloderPath;

        public int TaskType
        {
            get { return taskType; }
        }

        public EpdTaskInfoDto selectedTaskInfo;
        private bool isSelectedTaskInfo;//是否是手动选择(可选)
        private string ServerConfigText;
        private string ClientConfigText;
        private ICadWsOutsideControl cadWs;
        private ChannelFactory<ICadWsOutsideControl> cadWsFactory;
        private string logPath;
        /// <summary>
        /// 运行最长时间（分钟）
        /// </summary>
        private int MaxRuntime = -1;
        /// <summary>
        /// 发关邮件计时器
        /// </summary>
        private System.Threading.Timer sentMailTimer;

        public int CadType { get; set; }


       // private bool IsAllowReloadProe;
        private bool IsCadWsAuto;
        private string password;
        public volatile bool IsWCFConnect = true;
        public volatile bool TaskRunning;

        public FormCADTaskServer()
        {
            try
            {
                if (!Directory.Exists(this.basePath + "\\CadTaskLog"))
                {
                    Directory.CreateDirectory(/*Path.GetDirectoryName(System.Reflection.Assembly
                .GetExecutingAssembly().Location).TrimEnd('\\')*/this.basePath + "\\CadTaskLog");
                }

                this.ServiceConfigInfo = new WCFServiceConfigInfo();
                this.GetServiceConfigInfo();
                if (!string.IsNullOrEmpty(this.ServiceConfigInfo.Language))
                {
                    Thread.CurrentThread.CurrentUICulture = new CultureInfo(this.ServiceConfigInfo.Language);
                    Resources.Culture = Thread.CurrentThread.CurrentUICulture;
                }


                this.InitializeComponent();
                this.CADDbConnect = DBServiceClient.Instance.EGIPlatformService;
                this.EdsTaskConnect = DBServiceClient.Instance.EGIPlatformService;
                if (this.ServiceConfigInfo.IsUpdateModel)
                {
                 this.UpdateModelConnect = DBServiceClient.Instance.EGIPlatformService;
                }
                this.toolStripTaskType.Text = this.GetTaskType(this.taskType.ToString());
                this.toolStripWorkStation.Text = this.ServiceConfigInfo.CadWsRemark;
                this.Text = this.GetTaskType(this.taskType.ToString()) + "_"+ this.Text;
                this.CADDbOnLineConnect = DBServiceClient.Instance.EGIPlatformService;
            }
            catch (CommunicationException ex)
            {
                SentLogErrorMessage(ex.ToString());
                this.toolStripServerConnect.Text = Resources.NotConnected;
                this.IsWCFConnect = false;
            }
            catch (Exception ex)
            {
                SentLogErrorMessage(ex.ToString());
            }
            if (!Directory.Exists(this.basePath + "\\CadTaskLog"))
            {
                Directory.CreateDirectory(this.basePath + "\\CadTaskLog");
            }
            this.ISHasCadTask = false;
           
        }

        #region 数据库连接

        private void GetServiceConfigInfo()
        {
              ServerConfigText = File.ReadAllText(this.cfgFileName, Encoding.UTF8);
              ClientConfigText = File.ReadAllText(this.cfgClientFileName, Encoding.UTF8);
            this.ServiceConfigInfo.WCFServicePath = this.GetElement("WCFServicePath");
            this.ServiceConfigInfo.EndpointUrl = this.GetElement("EndpointUrl");
            this.ServiceConfigInfo.MaxArrayLength = this.GetElement("MaxArrayLength");
            this.ServiceConfigInfo.MaxDepth = this.GetElement("MaxDepth");
            this.ServiceConfigInfo.MaxBytesPerRead = this.GetElement("MaxBytesPerRead");
            this.ServiceConfigInfo.MaxNameTableCharCount = this.GetElement("MaxNameTableCharCount");
            this.ServiceConfigInfo.MaxStringContentLength = this.GetElement("MaxStringContentLength");
            this.ServiceConfigInfo.MaxReceivedMessageSize =
                Convert.ToInt64(this.GetElement("MaxReceivedMessageSize"));
            this.ServiceConfigInfo.ExpendViewName = this.GetElement("ExpendViewName");
            this.ServiceConfigInfo.PdsPath = this.GetElement("PdsPath");
            this.ServiceConfigInfo.WorkPath = this.GetElement("WorkPath");
            this.ServiceConfigInfo.CachePath = this.GetElement("CachePath");
            this.ServiceConfigInfo.BakPath = this.GetElement("BakPath");
            this.ServiceConfigInfo.DxfPath = this.GetElement("DxfPath");
            this.ServiceConfigInfo.DxfCopyPath = this.GetElement("DxfCopyPath");
            this.ServiceConfigInfo.EDrawingPath = this.GetElement("EDrawingPath");
            this.ServiceConfigInfo.PocResultPath = this.GetElement("PocResultPath");
            this.ServiceConfigInfo.ProviderNo = this.GetElement("ProviderNo");
            this.ServiceConfigInfo.DWGVersion = this.GetElement("DWGVersion");
            try
            {
                this.ServiceConfigInfo.PuTimes = Convert.ToInt32(this.GetElement("PuTimes"));
            }
            catch (Exception exception)
            {
                this.ServiceConfigInfo.PuTimes = 0;
                //SentLogErrorMessage(exception.ToString());
               
            }
            this.ServiceConfigInfo.AutoAlignDimension = this.GetBool(this.GetElement("AutoAlignDimension"));
            this.ServiceConfigInfo.Language = this.GetElement("Language");
            this.ServiceConfigInfo.IsCopyDxf = this.GetBool(this.GetElement("IsCopyDXF"));
            this.ServiceConfigInfo.MaxLogFileSize = this.GetElement("MaxLogFileSize");//日志最大是多大
            this.ServiceConfigInfo.DeleteSubRevitModel = this.GetBool(this.GetElement("DeleteSubRevitModel"));//是否清理revit的子模型
            this.ServiceConfigInfo.IsUpdateModel = this.GetBool(this.GetElement("IsUpdateModel"));
            this.ServiceConfigInfo.UpdateModelServer = this.GetElement("UpdateModelServer");
            this.ServiceConfigInfo.bRebuildNewInst = this.GetBool(this.GetElement("RebuildNewInst"));
            LanguageHelper.LoadLanguage(ServiceConfigInfo.Language);
            this.ServiceConfigInfo.bRebuildBaseMode = this.GetBool(this.GetElement("RebuildBaseMode"));

            this.ServiceConfigInfo.bRebuildBaseModelInstTable =
                this.GetBool(this.GetElement("RebuildBaseModelInstTable"));
            try
            {
                this.ServiceConfigInfo.nFontStrokeMode = Convert.ToInt32(this.GetElement("FontStrokeMode"));
            }
            catch (Exception exception)
            {
                SentLogErrorMessage(exception.ToString());
                //MessageBox.Show(exception.Message);
            }
            //ServiceConfigInfo:由配置文件读取的值
            this.ServiceConfigInfo.bIsBackupModel = this.GetBool(this.GetElement("IsBackupModel"));
            var strTaskType = this.GetElement("TaskType");
            this.taskTypes = new List<int>();
            if (!string.IsNullOrEmpty(strTaskType))
            {
                this.taskTypes = strTaskType.Split(new[] {','}, StringSplitOptions.RemoveEmptyEntries).Select(o => Convert.ToInt32(o)).ToList();
            }

            this.taskType = taskTypes.Any() ? taskTypes[0] : Convert.ToInt32(this.GetElement("TaskType"));
            var strSubTaskType = this.GetElement("SubTaskType");
            this.subTaskTypes = new List<int>();
            if (!string.IsNullOrEmpty(strSubTaskType))
            {
                this.subTaskTypes = strSubTaskType.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).Select(o => Convert.ToInt32(o)).ToList();
            }
            var CreatUser = this.GetElement("CreateUser");
            this.ServiceConfigInfo.CreateUser = GetArrayString(CreatUser);
            this.ServiceConfigInfo.WCFCADServer = this.GetElement("WCFCADServer");
            this.ServiceConfigInfo.WCFEdsServer = this.GetElement("WCFEdsServer");
            this.ServiceConfigInfo.SendMailServer = this.GetElement("SendMailServer");
            this.ServiceConfigInfo.BendAllowanceDefaultPath = this.GetElement("BendAllowanceDefaultPath");
            this.ServiceConfigInfo.bMakeDRW = this.GetBool(this.GetElement("MakeProjectMap"));
            this.ServiceConfigInfo.bMakeDXF = this.GetBool(this.GetElement("MakeSpreadMap"));
            this.ServiceConfigInfo.hasSheetPattern= this.GetBool(this.GetElement("HasSheetPattern"));
            this.ServiceConfigInfo.bMakeEDrawing = this.GetBool(this.GetElement("MakeEDrawing"));//按配置文件确定是否生成edrawing
            this.ServiceConfigInfo.StationType =this.GetElement("StationType");//获取工作站类型
            this.ServiceConfigInfo.bDXFBendLine = this.GetBool(this.GetElement("IsDXFBendLines")); //折弯线配置
            this.ServiceConfigInfo.bDXFReadThickness = this.GetBool(this.GetElement("IsDXFReadThickness")); //折弯厚度
            this.ServiceConfigInfo.bCADReturnStatus = this.GetBool(this.GetElement("IsReturnCADstatus"));//是否回写状态
            this.ServiceConfigInfo.SendbackUrl = this.GetElement("SendbackUrl"); //获取网络资源URL
            this.ServiceConfigInfo.bSendMail = this.GetBool(this.GetElement("IsSendMail"));//邮件发送
            this.ServiceConfigInfo.bSend3dError = this.GetBool(this.GetElement("IsSend3dError"));//3derror发送,2024-01-02弗朗茨
            this.ServiceConfigInfo.InterFerenceDetection = this.GetBool(this.GetElement("InterFerenceDetection"));//发送是否干涉
            this.ServiceConfigInfo.bMakeDWG = this.GetBool(this.GetElement("MakeDWG"));
            this.ServiceConfigInfo.bMakePDF = this.GetBool(this.GetElement("MakePDF"));
            this.ServiceConfigInfo.bDeleteConfig = this.GetBool(this.GetElement("DeleteUnuseConfig"));
            this.ServiceConfigInfo.FactoryNOs = this.GetArrayString(this.GetElement("FactoryNo"));
            this.ServiceConfigInfo.DrawingNOs = GetArrayString(this.GetElement("SelectPTId"));
            this.ServiceConfigInfo.AttachmentType = this.GetElement("AttachmentType");
            this.ServiceConfigInfo.IsHaveAttachment = this.GetBool(this.GetElement("IsHaveAttachment"));
            
            this.ServiceConfigInfo.AttachmentSaveObject = this.GetElement("SaveObject");
            //this.ServiceConfigInfo.IsCutline = this.GetBool(this.GetElement("IsCutline"));
            //20221202,syp1404,配置文件要改成多个租户配置是否打断尺寸线 , 不直接获取布尔值, 根据租户名称来获取
            this.ServiceConfigInfo.IsCutline = this.GetElement("IsCutline");

            this.ServiceConfigInfo.EGIService = this.GetElement("EGIService");
            this.ServiceConfigInfo.LoginUrl = this.GetElement("LoginUrl");
            this.ServiceConfigInfo.EGIUserNo = this.GetElement("EGIUserNo");
            this.ServiceConfigInfo.EGIPsw = this.GetElement("EGIPsw");
            this.ServiceConfigInfo.ZipMode = GetInt(this.GetElement("ZipMode"), -1);
            this.ServiceConfigInfo.SchedulerUri = this.GetElement("SchedulerUri");
            this.ServiceConfigInfo.UseModelCache = GetBool(this.GetElement("UseModelCache"));
            this.ServiceConfigInfo.KeepOpenLastDoc = GetBool(this.GetElement("KeepOpenLastDoc"));
            this.ServiceConfigInfo.UseDocumentDbTemplate = GetBool(this.GetElement("UseDocumentDbTemplate"));
            this.ServiceConfigInfo.UseDocumentDbResult = GetBool(this.GetElement("UseDocumentDbResult"));
            this.ServiceConfigInfo.CadWsRemark = this.GetElement("CadWsRemark");
            this.ServiceConfigInfo.IsCallBackForTaskFailed = GetBool(this.GetElement("IsCallBackForTaskFailed"));
            this.ServiceConfigInfo.ErrRerunCount = GetInt(this.GetElement("ErrRerunCount"), 4);
            this.ServiceConfigInfo.CurrentIp = DoGetHostEntry();
            this.ServiceConfigInfo.DefaultOrgNum = this.GetElement("DefaultOrgNum");
            this.ServiceConfigInfo.Status400RerunAfterMinutes = GetInt(this.GetElement("Status400RerunAfterMinutes"), 60);
            this.ServiceConfigInfo.DeleteNoStandardParam = GetBool(this.GetElement("DeleteNoStandardParam"));
            this.ServiceConfigInfo.NoStandardLayer = this.GetElement("NoStandardLayer");
            this.ServiceConfigInfo.NostandardJsonPath = this.GetElement("NostandardJsonPath");
            
            this.ServiceConfigInfo.CreoExePath = this.GetElement("CreoExePath");
            

            //
            //            if (!isInitialized)
            {
                RestClientPoxy.BaseUri = this.ServiceConfigInfo.EGIService;
                RestClient.EquipmentType = "EPD";
                ExcelEventBus.Offline += ExcelEventBus_Offline;
                RestClientPoxy.SetDefaultSessionId(Guid.NewGuid().ToString());
                RestClientPoxy.SetDefaultJSessionId(Guid.NewGuid().ToString());
                RestClientPoxy.SetDefaultJwtAuth(Guid.NewGuid().ToString());
                if (!String.IsNullOrEmpty(this.ServiceConfigInfo.DefaultOrgNum))
                {
                    RestClientPoxy.SetDefaultOrgnizationNum(this.ServiceConfigInfo.DefaultOrgNum);
                }
                
               
                isInitialized = true;
            }
           

            foreach (string t in this.ServiceConfigInfo.AttachmentType.Split(';'))
            {
                if (!String.IsNullOrEmpty(t))
                {
                    this.FileTypeListToSer.Add("." + t.ToUpper());
                }
            }
        }
        /// <summary>
        /// 获得本机IP地址
        /// </summary>
        /// <param name="hostname"></param>
        /// <returns></returns>
        private static string DoGetHostEntry()
        {
            System.Net.IPHostEntry IpEntry = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
            string localhostipv4Address = "";

            for (int i = 0; i != IpEntry.AddressList.Length; i++)
            {
                if (!IpEntry.AddressList[i].IsIPv6LinkLocal && IpEntry.AddressList[i].AddressFamily == AddressFamily.InterNetwork)
                {
                    localhostipv4Address = IpEntry.AddressList[i].ToString();
                    break;
                }
            }
            if (string.IsNullOrEmpty(localhostipv4Address))
            {
                localhostipv4Address = System.Net.Dns.GetHostName();
            }
            return localhostipv4Address;
        }
        private static bool isInitialized = false;
        private bool ExcelEventBus_Offline()
        {
            IsWCFConnect = false;
            Frm_Login frm = new Frm_Login();
            frm.LoadUserName(null);
            frm.LoginUrl = this.ServiceConfigInfo.LoginUrl;
            bool res = false;
            this.Invoke(new Action(() =>
            {
                if (!string.IsNullOrEmpty(this.ServiceConfigInfo.EGIUserNo) && !string.IsNullOrEmpty(this.ServiceConfigInfo.EGIPsw))
                {
                    var userNo = this.ServiceConfigInfo.EGIUserNo;
                    //this.ServiceConfigInfo.EGIUserNo = null;
                    //"使用租户:{1} 的用户名:{0} 登陆..."
                    SendLogMessage(taskInfo !=null ? this.taskInfo.EpdTaskId : Guid.Empty, string.Format(LanguageHelper.GetString("tenantLogin"), userNo,RestClientPoxy.Session.OrgnizationNum), 1);
                    try
                    {
                        if (string.IsNullOrEmpty(this.ServiceConfigInfo.LoginUrl))
                        {
                            DBServiceClient.Instance.EGIRbacServiceClient.Login(userNo, this.ServiceConfigInfo.EGIPsw);
                        }
                        else
                        {
                            DBServiceClient.Instance.EGIRbacServiceClient.Login1(userNo, this.ServiceConfigInfo.EGIPsw, this.ServiceConfigInfo.LoginUrl);
                        }
                        IsWCFConnect = true;
                        res = true;
                        //"使用租户:{1} 的用户名:{0} 登陆成功!"
                        SendLogMessage(taskInfo != null ? this.taskInfo.EpdTaskId : Guid.Empty, string.Format(LanguageHelper.GetString("tenantLoginSuccess"), userNo, RestClientPoxy.Session.OrgnizationNum), 1);
                    }
                    catch (Exception ex)
                    {
                        CovertHandGet(null, null);
                        //使用租户:{1} 的用户名:{0} 登陆失败!{2}
                        SendLogMessage(taskInfo != null ? this.taskInfo.EpdTaskId : Guid.Empty, string.Format(LanguageHelper.GetString("tenantLoginFail"), userNo, RestClientPoxy.Session.OrgnizationNum, ex.ToString()), 1);
                        //MessageBox.Show(ex.ToString(), ToString());
                        res = false;
                        throw;
                    }

                }
                else
                {
                    if (frm.ShowDialog(this) == DialogResult.OK)
                    {
                        IsWCFConnect = true;
                        res = true;
                    }
                    else
                    {
                        Application.Exit();
                        res = false;
                    }
                }
                
            }));
            return res;
        }

        private string[] GetArrayString(string text)
        {
            if(String.IsNullOrEmpty(text))
                return null;
            return text.Split(new char[]{',',';'}, StringSplitOptions.RemoveEmptyEntries);
        }
        private string GetElement(string elementName)
        {
            string regexStr = String.Format(@"<{0}>\s*(.*)\s*</{0}>", elementName);
            var re = new Regex(regexStr, RegexOptions.IgnoreCase);
            Match mc = re.Match(ServerConfigText);
            if (mc.Success)
            {
                return mc.Groups[1].ToString();
            }
            mc = re.Match(ClientConfigText);
            if (mc.Success)
            {
                return mc.Groups[1].ToString();
            }
            return string.Empty;
        }
        private int GetInt(string text, int defaultValue)
        {
            int v = 0;
            if (int.TryParse(text, out v))
            {
                return v - 1;
            }

            return defaultValue;
        }
        private bool GetBool(String boolText)
        {
            if (boolText.ToLower() == "true")
            {
                return true;
            }
            return false;
        }

         #endregion

        public WCFServiceConfigInfo ServiceConfigInfo { get; set; }


        public List<EpdCADCommandDto> taskCadList { get; private set; }
        public bool ISHasCadTask { get; private set; }
        public ServerState ServerState { get; private set; }
        public event EventHandler Closed;
        public event EventHandler GetNewCADTask;
        public event EventHandler AutoGetNewCADTask;
        public event EventHandler CovertHandGet;

        public void OnGetNewCADTask(EpdTaskInfoDto taskInfo)
        {
            this.selectedTaskInfo = taskInfo;
            this.taskInfo = taskInfo;
            this.selectedTaskInfo.CreateUser = taskInfo.CreateUser;
            this.isSelectedTaskInfo = true;
            this.isAuto = false;
            this.GetNewCADTask(null, null);
        }


        //连接属性


        private void btnStop_Click(object sender, EventArgs e)
        {
            this.ServerState = ServerState.Stop;
          
            this.Refresh();

            this.btnStop.Enabled = false;
            this.btnStart.Enabled = true;
            //タスクサービス：停止
            this.labelInfo.Text = LanguageHelper.GetString("TaskServiceStop");

            this.toolStripButtonGetTask.Visible = true;
            this.toolStripButtonGetTaskSelect.Visible = true;
            this.isAuto = false;
            this.IsCadWsAuto = false;
            this.CovertHandGet(sender, e);
            var processes = Process.GetProcessesByName("Zxtech.CAD.MQListener.exe");
            if (processes.Length > 0)
            {
                foreach (var process in processes)
                {
                    process.Kill();
                }
            }
            else
            {
                processes = Process.GetProcessesByName("Zxtech.CAD.MQListener");
                foreach (var process in processes)
                {
                    process.Kill();
                }
            }
            //todo: 向中间文件中写入[Stop!]命令
            this.WriteCmd("[Stop!]");
            try
            {
                if ( IsAllowReloadProeWs())
                    cadWs.StopWatch();
            }
            catch(Exception ex)
            {
                //守护程序：
                SentLogErrorMessage(LanguageHelper.GetString("Daemons") + ex.ToString());
            }

            this.Refresh();
        }


        public List<EpdCADCommandDto> GetTaskCadCodeList()
        {
            List<EpdCADCommandDto> task = null;
            if (this.IsWCFConnect)
            {
                try
                {
                    task = this.GetNewCADTaskInfo();
                    if (task != null && this.taskInfo != null)
                    {
                        var EpdtaskinfoDto = CADDbConnect.QueryEpdTaskInfoWithParameterByTaskId(this.taskInfo.EpdTaskId.ToString(),
                        TemplateRunTaskType.IsTest(taskType) ? "Test" : "Formal");
                        //任务执行失败次数:
                        SendLogMessage(taskInfo.EpdTaskId, LanguageHelper.GetString("TaskFailCount") + EpdtaskinfoDto.EpdTaskInfoDto.cadRunCount, -1);
                        taskInfo = EpdtaskinfoDto.EpdTaskInfoDto;
                        if (EpdtaskinfoDto.EpdTaskInfoDto.cadRunCount > this.ServiceConfigInfo.ErrRerunCount)
                        {
                            //任务执行失败次数超过4次，不予执行!
                            SendLogMessage(taskInfo.EpdTaskId, LanguageHelper.GetString("TaskFailCountOver4"), -1);
                            SetModelError();
                            taskInfo.Failed = false;

                            if (string.IsNullOrEmpty(this.taskInfo.CallbackUrl))
                            {
                                //获取回写协议通过配置文件：{0}
                                SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolByFile"), this.ServiceConfigInfo.SendbackUrl));

                                bool getstatus = RequestWebAPI(Convert.ToString(this.taskInfo.EpdTaskId), this.ServiceConfigInfo.SendbackUrl);
                                //获取回写协议状态：{0}
                                SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolStatus"), getstatus));

                                getstatus = PostWebapi("", this.ServiceConfigInfo.SendbackUrl);
                                //获取回写协议状态：{0}
                                SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolStatus"), getstatus));
                            }
                            else
                            {
                                //获取回写协议通过前系统EDS传递：{0} 
                                SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolByEDS"), this.taskInfo.CallbackUrl));
                                bool getstatus = RequestWebAPI(Convert.ToString(this.taskInfo.EpdTaskId), this.taskInfo.CallbackUrl);
                                //获取回写协议状态：{0}
                                SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolStatus"), getstatus));
                                getstatus = PostWebapi("", this.taskInfo.CallbackUrl);
                                //获取回写协议状态：{0}
                                SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolStatus"), getstatus));
                            }

                            return null;
                        }

                        if (EpdtaskinfoDto.EpdTaskInfoDto.Status == 500 || EpdtaskinfoDto.EpdTaskInfoDto.Status == 750)
                        {
                            //SendLogMessage(taskInfo.EpdTaskId, "任务状态为" + EpdtaskinfoDto.EpdTaskInfoDto.Status + "，不予执行!", -1);
                            //任务状态为{0}，不予执行!
                            SendLogMessage(taskInfo.EpdTaskId, string.Format(LanguageHelper.GetString("TaskStatusNotExecuted"), EpdtaskinfoDto.EpdTaskInfoDto.Status), -1);
                            
                            taskInfo.Failed = false;
                            return null;
                        }
                        EpdtaskinfoDto.EpdTaskInfoDto.cadRunCount = !EpdtaskinfoDto.EpdTaskInfoDto.cadRunCount.HasValue ? 0 : EpdtaskinfoDto.EpdTaskInfoDto.cadRunCount + 1;
                        EpdtaskinfoDto.EpdTaskInfoDto.CadWsIp = this.ServiceConfigInfo.CurrentIp;
                        if (taskInfo.Status== Zxtech.CADTaskServer.TemplateRunTaskType.SLDToEDRAWING)
                        {
                            EpdtaskinfoDto.EpdTaskInfoDto.Status = Zxtech.CADTaskServer.TemplateRunTaskType.SLDToEDRAWINGRuning;
                        }
                        else
                        {
                            EpdtaskinfoDto.EpdTaskInfoDto.Status = 400;
                        }
                        
                        CADDbConnect.SaveEpdTaskInfoEntities(new List<EpdTaskInfoWithParameterDto>() { EpdtaskinfoDto });
                        if (this.isAuto && !string.IsNullOrEmpty(this.RabbiMqFileName) && taskInfo != null)
                        {
                            //taskInstance.WorkPath = stationInfo.WorkPath + "\\" + taskInfo.OrganizationNum + "\\" + taskInstance.NewFileDir + "\\";
                            var initCode = task.FirstOrDefault(o => o.CommandType == 1100);
                            if (initCode != null && !string.IsNullOrEmpty(initCode.Para3))
                            {
                                initCode.Para3 = RestClientPoxy.Session.OrgnizationNum + "\\" + initCode.Para3.TrimStart('\\');
                            }
                            else
                            {
                                if (!string.IsNullOrEmpty(taskInfo.CADAttachmentPath))
                                {
                                    taskInfo.CADAttachmentPath = RestClientPoxy.Session.OrgnizationNum + "\\" + taskInfo.CADAttachmentPath.TrimStart('\\');
                                }
                                else if (!string.IsNullOrEmpty(taskInfo.Name))
                                {
                                    taskInfo.Name = RestClientPoxy.Session.OrgnizationNum + "\\" + taskInfo.Name.TrimStart('\\');
                                }
                            }
                        }
                        return task;
                    }
                    else
                    {
                        WriteCmd("[Done!]");
                        return null;
                    }
                    
                }
                catch (CommunicationException ex)
                {
                    SentLogErrorMessage(ex.ToString());
                    this.toolStripServerConnect.Text = Resources.NotConnected;
                    this.IsWCFConnect = false;
                    WriteCmd("[Done!]");
                    return null;
                }
                catch (Exception ex)
                {
                    SentLogErrorMessage(ex.ToString());
                    WriteCmd("[Done!]");
                    return null;
                }
                return task;
            }
            SentLogErrorMessage("IsWCFConnect=false");
            return null;
        }

        private List<EpdCADCommandDto> GetNewCADTaskInfo()
        {
            this.rtbRunInfo.Clear();          
            this.ISHasCadTask = false;
            if (this.selectedTaskInfo != null)
            {
                taskInfo = selectedTaskInfo;
            }
            else
            {
                GetCADTaskInfoFirst();
            }

            if (this.taskInfo != null)
            {
                string path = null;
                if (string.IsNullOrEmpty(RestClientPoxy.Session.OrgnizationNum))
                {
                    path = Path.Combine(this.basePath, "CadTaskLog");
                }
                else
                {
                    path = Path.Combine(this.basePath, "CadTaskLog", RestClientPoxy.Session.OrgnizationNum);
                }

                taskFloderPath = path;
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
                //this.taskCadList = this.CADDbConnect.GetTaskCadCodeList(this.taskInfo.EpdTaskId);
                if (!string.IsNullOrEmpty(this.ServiceConfigInfo.CadWsRemark))
                {
                    this.taskCadList = this.CADDbConnect.GetTaskCadCodeListByCadType(this.taskInfo.EpdTaskId,
                      this.ServiceConfigInfo.CadWsRemark);
                }
                else
                {
                    this.taskCadList = this.CADDbConnect.GetTaskCadCodeList(this.taskInfo.EpdTaskId);
                }
                if (taskCadList!=null && this.taskCadList.Count > 0)
                {
                    taskCadList = taskCadList.OrderBy(o => o.SortId).ToList();
                    if (this.ServiceConfigInfo.UseModelCache)
                    {
                        GenerateDocumentId(taskCadList);
                    }
                    this.ISHasCadTask = true;
                    if (isAuto && IsAllowReloadProeWs())
                    {
                        this.cadWs.StartTask();
                        var logPath1 = Path.Combine(path, "CadTaskLog_" + taskInfo.EpdTaskId + ".txt"); 
                        var fi = new FileInfo(logPath1);
                        if (fi.Exists && this.taskInfo.EpdTaskId != Guid.Empty)
                        {
                            //开始扫描崩溃日志,日志文件为：
                            SendLogMessage(LanguageHelper.GetString("ScanCrashLog") + logPath1);
                            SaveCadLogFile(logPath1, this.taskInfo.EpdTaskId, null);
                        }
                        else
                        {
                         
                        }
                    }
                    if (isAuto)
                    { 
                    
                    }
                    //CAD任务服务 - 任务名:{0} ({1})
                    this.Text = string.Format(LanguageHelper.GetString("CadTaskService"), this.taskInfo.Name,this.taskInfo.ProductNo);
                    this.Refresh();
                    return this.taskCadList;
                }
                else
                {
                    this.SendLogMessage(string.Format(Resources.GetTask, this.taskInfo.EpdTaskId));

                    if (!string.IsNullOrEmpty(this.ServiceConfigInfo.CadWsRemark))
                    {
                        bool succ = this.CADDbConnect.SetTaskComplatedByCadType(this.taskInfo.EpdTaskId,this.ServiceConfigInfo.CadWsRemark, TemplateRunTaskState.CadWsRuning);
                    }
                    else
                    {
                        bool succ = this.CADDbConnect.SetTaskComplated(this.taskInfo.EpdTaskId, TemplateRunTaskState.CadWsRuning, "{}");
                    }
                  
                }
            }
            return null;
        }

        private void GenerateDocumentId(List<EpdCADCommandDto> commands)
        {
            List<CADCommand> totalCommands = new List<CADCommand>();
            CADCommand current = null;
            foreach (var command in commands)
            {
                if (command.CommandType == 1200)
                {
                    var tempCmd = new CADCommand() { InitCommand = command, Parent = current, Children = new List<CADCommand>()};
                    totalCommands.Add(tempCmd);
                    if (current != null)
                    {
                        current.Children.Add(tempCmd);
                    }
                    current = tempCmd;
                }
                else if (command.CommandType == 1300)
                {
                    if (current != null)
                    {
                        current.DriveCommand = command;
                        current = current.Parent;
                    }
                }
            }

            var roots = totalCommands.Where(o => o.Parent == null).ToList();
            foreach (var root in roots)
            {
                GenerateDocumentId(root);
            }
        }

        private void GenerateDocumentId(CADCommand root)
        {
            IDictionary<string, Guid> childIds = new Dictionary<string, Guid>();
            foreach (var child in root.Children)
            {
                GenerateDocumentId(child);
                childIds[child.InitCommand.Para5] = child.InitCommand.DocumentId.Value;
            }

            IDictionary<string, string> dic = new Dictionary<string, string>();
            dic["TemplateName"] = root.InitCommand.Para1;
            dic["TemplateType"] = root.InitCommand.Para2;
            dic["TemplatePath"] = root.InitCommand.Para3;
            dic["RefConfigName"] = root.InitCommand.Para4;
            dic["NewConfigName"] = root.InitCommand.Para5;
            dic["NewDrwName"] = root.InitCommand.Para6;
            dic["NewDwgName"] = root.InitCommand.Para7;
            dic["NewPdfName"] = root.InitCommand.Para8;
            dic["NewDxfName"] = root.InitCommand.Para9;
            dic["HideInvalidDim"] = root.InitCommand.Para10;
            dic["ActiveConfigName"] = root.InitCommand.Para12;
            dic["Properties"] = root.DriveCommand.Para1;
            dic["Dimensions"] = root.DriveCommand.Para2;
            dic["Feature"] = root.DriveCommand.Para3;
            dic["ChildStatus"] = root.DriveCommand.Para4;
            dic["Children"] = string.Join(";", childIds.Select(o => string.Format("{0}={1}", o.Key, o.Value)));
            if (!string.IsNullOrEmpty(dic["ChildStatus"]))
            {
                var tempArr = dic["ChildStatus"].Split(',');
                var needRebuilt = false;
                for (int i = 0; i < tempArr.Length; i++)
                {
                    if (i % 2 == 1)
                    {
                        var str = tempArr[i];
                        if (!string.IsNullOrEmpty(str))
                        {
                            str = str.Trim('\'');
                            if (childIds.ContainsKey(str))
                            {
                                str = childIds[str].ToString();
                                tempArr[i] = string.Format("'{0}'", str);
                                needRebuilt = true;
                            }
                        }
                    }
                }

                if (needRebuilt)
                {
                    dic["ChildStatus"] = string.Join(",", tempArr);
                }
            }

            var modelFile = ServiceConfigInfo.PdsPath.TrimEnd('\\') + "\\" /*+ RestClientPoxy.Session.OrgnizationNum +"\\"*/+ root.InitCommand.Para3.TrimStart('\\');
            if (!File.Exists(modelFile))
            {
                dic["TemplateMd5"] = null;
            }
            else
            {
                dic["TemplateMd5"] = GetMD5ByHashAlgorithm(modelFile);
            }

            var commandStr = string.Join(Environment.NewLine, dic.Select(o => string.Format("{0}:{1}", o.Key, o.Value)));
            var documentId = GetGuid(commandStr);
            root.InitCommand.DocumentId = documentId;
            root.InitCommand.ModelMd5 = dic["TemplateMd5"];
            root.DriveCommand.DocumentId = documentId;
            root.DriveCommand.ModelMd5 = dic["TemplateMd5"];
        }

        public IList<string> GetDocoumentById(string modelName, string documentId, string workspace)
        {
            DocumentInfoDto dto = new DocumentInfoDto();
            dto.DocExt1 = modelName.Replace("\\", "@");
            dto.RefExt1 = documentId;
            dto.DocType = "CADModelCache";
            var documents = DBServiceClient.Instance.EGIRbacServiceClient.findDocumentFileList(dto);
            if (documents != null && documents.Any() && documents.First().DocumentFileOutputDto != null && documents.First().DocumentFileOutputDto.BinaryDto != null
                && documents.First().DocumentFileOutputDto.BinaryDto.Length > 0)
            {
                var buf = documents.First().DocumentFileOutputDto.BinaryDto;
                var fileId = Guid.NewGuid().ToString();
                var zipFile = Path.Combine(workspace, fileId + ".zip");
                File.WriteAllBytes(zipFile, buf);
                var unzipPath = Path.Combine(workspace, fileId);
                ZipHelper1.UnZip(zipFile, unzipPath, null);
                var files = Directory.GetFiles(unzipPath);
                var results = new List<string>();
                foreach (var file in files)
                {
                    var trgFile = file.Replace(fileId + "\\", "");
                    File.Copy(file, trgFile, true);
                    File.Delete(file);
                    results.Add(trgFile);
                }
                File.Delete(zipFile);
                Directory.Delete(unzipPath, true);
                return results.Any() ? results : null;
            }

            return null;
        }
        public IList<string> GetDocoumentById2(string cachePath, string modelName, string /*Guid*/ documentId, string workspace)
        {
            DocumentInfoDto dto = new DocumentInfoDto();
            dto.DocExt1 = modelName.Replace("\\", "@");
            dto.RefExt1 = documentId;
            dto.DocType = "CADModelCache";
            var documents = DBServiceClient.Instance.EGIRbacServiceClient.findDocumentFileList(dto);

            if (documents != null && documents.Count!=0 )//获取到一个document！=null，但是document.count=0的情况
            {
                DocumentRefFileOutputDto targetDocumentDto = documents.Where(x => x.RefUpdateTime.HasValue)
                    .OrderByDescending(x => x.RefUpdateTime)
                    .FirstOrDefault();
                if (targetDocumentDto != null && targetDocumentDto.RefExt1 != null)
                {
                    string cacheTaskIdPath = Path.Combine(cachePath, targetDocumentDto.RefExt3);
                    string documentIdTxtPath = Path.Combine(cacheTaskIdPath, targetDocumentDto.RefExt1 + ".txt");
                    var files = new List<string>();
                    if (!string.IsNullOrEmpty(documentIdTxtPath))
                    {
                        if (File.Exists(documentIdTxtPath))
                        {
                            //将documentId.txt文件中的内容读到一个list中,这个list是要移动的文件
                            List<string> lines = new List<string>(File.ReadAllLines(documentIdTxtPath));

                            foreach (string line in lines)
                            {
                                files.Add(Path.Combine(cacheTaskIdPath, line));
                            }
                            string lastTaskReaultPath = Path.GetDirectoryName(lines[0]);
                            List<string> movedFileNames = CopyToNewFolder(lastTaskReaultPath, workspace, files);
                            return movedFileNames.Any() ? movedFileNames : null;
                        }

                    }
                }
            }

            return null;
        }

        public void SaveDocument(string modelName, string documentId, string md5, Guid taskId, IList<string> files, string workspace)
        {
            if (files == null || !files.Any())
            {
                return;
            }

            var fileId = Guid.NewGuid().ToString();
            var trgPath = Path.Combine(workspace, fileId);
            Directory.CreateDirectory(trgPath);
            foreach (var file in files)
            {
                //var trgFile = Path.Combine(trgPath, file.Substring(workspace.Length));
                var trgFile= Path.Combine(trgPath, Path.GetFileName(file));
                File.Copy(file, trgFile, true);
            }

            var buf = ZipHelper1.ZipDirectoryToBuffer(trgPath, true);
            if (buf == null || buf.Length == 0)
            {
                return;
            }

            Directory.Delete(trgPath, true);
            DocumentInputDto document = new DocumentInputDto();
            document.DocType = "CADModelCache";
            document.DocExt1 = modelName.Replace("\\", "@");
            document.RefExt1 = documentId;
            document.RefExt2 = md5;
            document.RefExt3 = taskId.ToString();
            document.documentFileInputDto = new DocumentFileInputDto();
            document.documentFileInputDto.fileContent = buf;
            document.documentFileInputDto.fileName = fileId + ".zip";
            document.documentFileInputDto.fileSize = buf.LongLength + "";
            DBServiceClient.Instance.EGIRbacServiceClient.saveDocumentList(new List<DocumentInputDto>() {document});
        }

        //public void SaveDocument(string modelName, string documentId, string md5, Guid taskId, IList<string> files, string workspace)
        //{
        public void SaveDocument2(string CachePath, string modelName, Guid documentId, string md5, Guid taskId, List<string> files, string workspace/*, string idSource, int partId, bool isTest*/)
        {
            if (files == null || !files.Any()) return;
            string cacheTaskIdPath = Path.Combine(CachePath, taskId.ToString());
            if (!Directory.Exists(cacheTaskIdPath)) Directory.CreateDirectory(cacheTaskIdPath);
            string documentIdTxtPath = Path.Combine(cacheTaskIdPath, documentId + ".txt");
            try
            {
                if (!File.Exists(documentIdTxtPath)) File.Create(documentIdTxtPath).Close();
           //     List<string> movedFileNames = CopyToNewFolder(workspace, cacheTaskIdPath, files);
                using (StreamWriter writer = new StreamWriter(documentIdTxtPath))
                {
                    foreach (string file in files)
                    {
                        writer.WriteLine(file);
                    }
                }
            }
            catch (Exception)
            {

                throw;
            }

            var buf = File.ReadAllBytes(documentIdTxtPath);
            if (buf == null || buf.Length == 0)
            {
                return;
            }

            var fileId = Guid.NewGuid().ToString();
            DocumentInputDto document = new DocumentInputDto();
            document.DocType = "CADModelCache";
            document.DocExt1 = modelName.Replace("\\", "@");
            document.RefExt1 = documentId.ToString();
            document.RefExt2 = md5;
            document.RefExt3 = taskId.ToString();
            document.documentFileInputDto = new DocumentFileInputDto();
            document.documentFileInputDto.fileContent = buf;
            document.documentFileInputDto.fileName = documentId + ".txt";
            document.documentFileInputDto.fileSize = buf.LongLength + "";
            DBServiceClient.Instance.EGIRbacServiceClient.saveDocumentList(new List<DocumentInputDto>() { document });
        }
        private List<string> CopyToNewFolder(string oldFolder, string newFolder, List<string> files)
        {
            List<string> movedFileNames = new List<string>();
            // 创建新的目录结构
            List<string> subFolders = files.Select(f => f.Replace(oldFolder, "")).Select(f => f.Substring(0, f.LastIndexOf('\\'))).Distinct().ToList();
            foreach (string subFolder in subFolders)
            {
                string fullPath = Path.Combine(newFolder, subFolder.TrimStart('\\'));
                if (!Directory.Exists(fullPath))
                {
                    Directory.CreateDirectory(fullPath);
                }
            }
            // 移动每个原始文件
            foreach (string file in files)
            {
                string relativePath = file.Replace(oldFolder, "");
                string fullPath = Path.Combine(newFolder, relativePath.TrimStart('\\'));
                if ( !string.IsNullOrEmpty(fullPath))
                {
                    File.Copy(file, fullPath,true);
                    SendLogMessage( "MoveCacheFiles:"+file+ "-->"+fullPath);
                    movedFileNames.Add(fullPath);
                }
            }
            return movedFileNames;
        }




        private static Guid GetGuid(string text)
        {
            if (string.IsNullOrEmpty(text)) throw new ArgumentNullException("text");
            MD5CryptoServiceProvider MP = new MD5CryptoServiceProvider();
            byte[] md5 = MP.ComputeHash(Encoding.UTF8.GetBytes(text));

            return new Guid(md5);

        }

        private static string GetMD5ByHashAlgorithm(string path)
        {
            if (!File.Exists(path)) return "";
            int bufferSize = 1024 * 16;//自定义缓冲区大小16K            
            byte[] buffer = new byte[bufferSize];
            Stream inputStream = File.Open(path, FileMode.Open, FileAccess.Read, FileShare.Read);
            HashAlgorithm hashAlgorithm = new MD5CryptoServiceProvider();
            int readLength = 0;//每次读取长度            
            var output = new byte[bufferSize];
            while ((readLength = inputStream.Read(buffer, 0, buffer.Length)) > 0)
            {
                //计算MD5                
                hashAlgorithm.TransformBlock(buffer, 0, readLength, output, 0);
            }
            //完成最后计算，必须调用(由于上一部循环已经完成所有运算，所以调用此方法时后面的两个参数都为0)            		  
            hashAlgorithm.TransformFinalBlock(buffer, 0, 0);
            string md5 = BitConverter.ToString(hashAlgorithm.Hash);
            hashAlgorithm.Clear();
            inputStream.Close();
            return md5.Replace("-", "");
        }

        private class CADCommand
        {
            public EpdCADCommandDto InitCommand { get; set; }
            public EpdCADCommandDto DriveCommand { get; set; }
            public IList<CADCommand> Children { get; set; }
            public CADCommand Parent { get; set; }
        }

        private void GetCADTaskInfoFirst()
        {
            //todo: 判断中间文件名字段是否为空。
            //todo: 若为空：使用原有方法；
            //todo: 若不为空：读取中间文件内容，并判断是否第一行是以[Done!]\[Failed!]开头，若是：taskInfo = null; 若不是：将第二行以后的字符串拼接起来后json反序列化为EpdTaskInfoDto对象并赋值给taskInfo，返回
            if (string.IsNullOrEmpty(RabbiMqFileName))
            {
                var criteria = GetWhereString();
                string orderby = "Pri descending,CreateTime";
                criteria.Orders.Add(Order.asc("createTime"));
                List<EpdTaskInfoDto> taskInfos = new List<EpdTaskInfoDto>();
                taskInfos = !string.IsNullOrEmpty(this.ServiceConfigInfo.CadWsRemark) ? this.CADDbConnect.QueryCADTaskInfosByCadTypeAndCriteria(criteria, this.ServiceConfigInfo.CadWsRemark, TemplateRunTaskType.IsTest(taskType) ? "Test" : "Formal") : this.CADDbConnect.QueryCADTaskInfosByCriteria(criteria, TemplateRunTaskType.IsTest(taskType) ? "Test" : "Formal");
                
                if (taskInfos != null && taskInfos.Any())
                {
                    taskInfos = taskInfos.Where(o => o.Status != 400 || (o.Status == 400 && o.UpdateTime.HasValue && o.UpdateTime.Value < DateTime.Now.AddMinutes(-this.ServiceConfigInfo.Status400RerunAfterMinutes))).OrderBy(o => o.CreateTime).ToList();
                    if (taskInfos.Any())
                    {
                        taskInfo = taskInfos[0];
                    }
                    else
                    {
                        taskInfo = null;
                    }
                }
                else
                {
                    SendLogMessage(string.Format("{0},{1}", WorkStationType.Cad, new JavaScriptSerializer().Serialize(criteria)));
                    taskInfo = null;
                }
            }
            else
            {
                if (!File.Exists(RabbiMqFileName))
                {
                    SendLogMessage(string.Format("{0},{1}", WorkStationType.Cad, RabbiMqFileName));
                    taskInfo = null;
                }
                else
                {
                    var content = File.ReadAllText(RabbiMqFileName, Encoding.UTF8);
                    if (string.IsNullOrEmpty(content))
                    {
                        SendLogMessage(string.Format("{0},{1}", WorkStationType.Cad, RabbiMqFileName));
                        taskInfo = null;
                    }
                    else
                    {
                        if (content.Contains("#@!"))
                        {
                            SendLogMessage(string.Format("{0},{1}", WorkStationType.Cad, RabbiMqFileName));
                            taskInfo = null;
                        }
                        else
                        {
                            var serializer = new JavaScriptSerializer() {MaxJsonLength = Int32.MaxValue};
                            try
                            {

                                var request = serializer.Deserialize(content, typeof(MessageRequestDto)) as MessageRequestDto;

                                if (request == null)
                                {
                                    taskInfo = null;
                                    SendLogMessage(string.Format("{0},Deserialize failed!{1} {2} {3}", WorkStationType.Cad, RabbiMqFileName, Environment.NewLine, content));
                                    WriteCmd("[Failed!]", content);
                                }
                                else
                                {
                                    if (request.OrgnizationNo != RestClientPoxy.Session.OrgnizationNum)
                                    {
                                        RestClientPoxy.SetDefaultSession(new SessionInfo(-1)
                                        {
                                            AccessCode = request.AccessCode,
                                            AccessType = request.AccessType,
                                            AppId = RestClient.EquipmentType,
                                            Code = request.Code,
                                            JSessionId = request.JSessionId,
                                            JwtAuth = request.JwtAuth,
                                            Language = request.Language,
                                            OrgnizationNum = request.OrgnizationNo,
                                            SessionId = request.SessionId,
                                            UserId = request.UserId
                                        });
                                        RestClientPoxy.Session.AccessCode = request.AccessCode;
                                        RestClientPoxy.Session.AccessType = request.AccessType;
                                        RestClientPoxy.Session.AppId = RestClient.EquipmentType;
                                        RestClientPoxy.Session.Code = request.Code;
                                        RestClientPoxy.Session.JSessionId = request.JSessionId;
                                        RestClientPoxy.Session.JwtAuth = request.JwtAuth;
                                        RestClientPoxy.Session.Language = request.Language;
                                        RestClientPoxy.Session.OrgnizationNum = request.OrgnizationNo;
                                        RestClientPoxy.Session.SessionId = request.SessionId;
                                        RestClientPoxy.Session.UserId = request.UserId;

                                    }
                                    taskInfo = serializer.DeserializeCustom(request.Parameter, typeof(EpdTaskInfoDto)) as EpdTaskInfoDto;
                                    if (taskInfo == null)
                                    {
                                        SendLogMessage(string.Format("{0},Deserialize failed!{1} {2} {3}", WorkStationType.Cad, RabbiMqFileName, Environment.NewLine, content));
                                        WriteCmd("[Failed!]", content);
                                    }

                                }
                            }
                            catch (Exception e)
                            {
                                SendLogMessage(string.Format("{0},Deserialize failed!{1} {2} {3} {4} {5}", WorkStationType.Cad, RabbiMqFileName, Environment.NewLine, content, Environment.NewLine, e.ToString()));
                                WriteCmd("[Failed!]", content);
                            }
                        }
                    }
                }
            }
            
        }

        public void WriteCmd(string cmd, string content = null)
        {
            if (!string.IsNullOrEmpty(RabbiMqFileName) && File.Exists(RabbiMqFileName))
            {
                if (content == null)
                {
                    content = File.ReadAllText(RabbiMqFileName, Encoding.UTF8);
                }
                content = cmd + "#@!" + content;
                SendLogMessage("Write file:" + RabbiMqFileName + " -> " + content);
                File.WriteAllText(RabbiMqFileName, content, Encoding.UTF8);
            }
        }

        private QueryCriteria GetWhereString()
        {
            if (isSelectedTaskInfo)
            {
                isSelectedTaskInfo = false;
                return new QueryCriteria(QueryUtils.Eq("epdTaskId", selectedTaskInfo.EpdTaskId));
          
            }
            else
            {
                return GetHandWereString(true);
            }
        }

        public QueryCriteria GetHandWereString(bool isTaskInfo)
        {
            QueryCriteria criteria = new QueryCriteria();
            // 任务类型，创建者，系列号
            //string whereG = string.Empty;
            if (this.ServiceConfigInfo.StationType.ToLower().Contains("edrawing"))
            {
                criteria.Add(QueryUtils.In("status", new object[] { Zxtech.CADTaskServer.TemplateRunTaskType.SLDToEDRAWING}));
            }
            else
            {
                criteria.Add(QueryUtils.In("status", new object[] { 100, 150, 400 }));
                //criteria.Add(QueryUtils.Or(QueryUtils.In("status", new object[] {100, 150}), QueryUtils.And(QueryUtils.Eq("status", 400), QueryUtils.Lt("updateTime", DateTime.Now.AddMinutes(-60)))));
            }
            if (taskTypes.Count > 1)
            {
                criteria.Add(QueryUtils.In("taskType", taskTypes));
            }
            else
            {
                criteria.Add(QueryUtils.Eq("taskType", taskType));

            }
            if (this.ServiceConfigInfo.CreateUser != null && this.ServiceConfigInfo.CreateUser.Length>0)
            {
                //whereG += GetSqlIn("CreatorUserNo", this.ServiceConfigInfo.CreateUser);
                criteria.Add(QueryUtils.In("createUser", this.ServiceConfigInfo.CreateUser.ToList()));
            }
            if (this.ServiceConfigInfo.DrawingNOs != null && this.ServiceConfigInfo.DrawingNOs.Length>0)
            {
                //whereG += GetSqlIn("TaskShowName", this.ServiceConfigInfo.DrawingNOs);
                criteria.Add(QueryUtils.In("productNo", this.ServiceConfigInfo.DrawingNOs.ToList()));
            }
            if (this.ServiceConfigInfo.FactoryNOs!=null&&this.ServiceConfigInfo.FactoryNOs.Length>0)
            {
                criteria.Add(QueryUtils.In("branch", this.ServiceConfigInfo.FactoryNOs.ToList()));
                //whereG += GetSqlIn("ChildCop", this.ServiceConfigInfo.FactoryNOs);
            }

            return criteria;
        }
        public string GetSqlIn(string colum, string[] condition)
        {
         
            //string inSql = string.Empty;
            //foreach (var item in condition)
            //{
            //    inSql += string.Format("'{0}',");
            //}
            //inSql = inSql.TrimEnd(',');
            //return string.Format(" &&({0} in ({1}))", colum, inSql);
            string inSql = string.Empty;
            foreach (var item in condition)
            {
                inSql += string.Format("{0}==\"{1}\" ||", colum, item);
            }
            inSql = inSql.TrimEnd('|').Trim();
            return string.Format(" &&({0})",  inSql);
        }


        public string GetHostName()
        {
            return Dns.GetHostName();
        }

        private string GetHostIpAddress()
        {
            System.Net.IPAddress addr;

            // 获得本机局域网IP地址

            addr = new System.Net.IPAddress(Dns.GetHostByName(Dns.GetHostName()).AddressList[0].Address);

            return addr.ToString();
        }

        public void SendLogMessage(string message)
        {
            Guid taskId = Guid.Empty;
         
            if (this.taskInfo != null)
            {
                taskId = this.taskInfo.EpdTaskId;
              
            }
            this.SendLogMessage(taskId, message, -1);
        }

        public void SentLogErrorMessage(string message)
        {
            Guid taskId = Guid.Empty;
          
            if (this.taskInfo != null)
            {
                taskId = this.taskInfo.EpdTaskId;
          
            }
            this.SendLogMessage(taskId, message, 2);
        }

        public void SendLogMessage(Guid taskId, string message, int messageType)
        {
            //this.Activate();
            //this.Refresh();
            string info;
            switch (messageType)
            {
                case 1:
                    info = string.Format("{0} !    {1}", DateTime.Now, message);
                    break;
                case 2:
                    info = string.Format("{0} !!   {1}", DateTime.Now, message);
                    break;
                default:
                    info = string.Format("{0} {1}", DateTime.Now, message);
                    break;
            }




            if (rtbRunInfo != null && rtbRunInfo.IsHandleCreated)
            {
//                if (messageType == 2 || string.IsNullOrEmpty(this.ServiceConfigInfo.Language) || this.ServiceConfigInfo.Language == "zh-CN" || this.ServiceConfigInfo.Language == "en-US")
                {
                    rtbRunInfo.BeginInvoke(new Action(() =>
                    {
                        switch (messageType)
                        {
                            case 1:
                                this.rtbRunInfo.SelectionColor = Color.Blue;
                                break;
                            case 2:
                                this.rtbRunInfo.SelectionColor = Color.Red;
                                break;
                            default:
                                break;
                        }
                        this.rtbRunInfo.AppendText(info);
                        this.rtbRunInfo.AppendText("\n");

                    }));
                }
                
            }

            if (!Directory.Exists(string.Format("{0}\\CadTaskLog\\{1}", this.basePath, RestClientPoxy.Session?.OrgnizationNum)))
            {
                Directory.CreateDirectory(string.Format("{0}\\CadTaskLog\\{1}", this.basePath, RestClientPoxy.Session?.OrgnizationNum));
            }
            // 写入日志
            logPath = string.Format("{0}\\CadTaskLog\\{2}\\CadTaskLog_{1}.txt", this.basePath, taskId, RestClientPoxy.Session?.OrgnizationNum);
            File.AppendAllText(logPath, info + Environment.NewLine);
            //            this.logTxt = new FileStream(logPath, FileMode.OpenOrCreate | FileMode.Append);
            //            var write = new StreamWriter(this.logTxt, Encoding.UTF8);
            //            write.WriteLine(info + "\n");
            //            write.Flush();
            //            this.logTxt.Close();
            OnLogHandler(info + Environment.NewLine);
            Application.DoEvents();
        }

        public event Action<string> LogHandler;

        private void UploadResult(string attachmentPath, Guid taskId)
        {
            SetFile(attachmentPath);
            if (ServiceConfigInfo.UseDocumentDbResult)
            {
               
                var newPath = Path.Combine(attachmentPath, Guid.NewGuid().ToString());
                Directory.CreateDirectory(newPath);
                foreach (var fileInfo in this.listFile)
                {
                    fileInfo.CopyTo(Path.Combine(newPath, Path.GetFileName(fileInfo.FullName)), true);
                }
                //打包上传结果...
                SendLogMessage(taskId, LanguageHelper.GetString("PackageUploadResults") + " ---- " + ServiceConfigInfo.ZipMode, 0);
                var buf = ServiceConfigInfo.ZipMode.HasValue && ServiceConfigInfo.ZipMode.Value == 1 ? ZipHelper1.ZipDirectoryToBuffer(newPath) : ZipHelper.ZipDirectoryToBuffer(newPath);
                if (buf != null)
                {
                    //File.WriteAllBytes(newPath + ".zip", buf);
                    var id = DBServiceClient.Instance.EGIPlatformService.UploadFile(buf, Path.GetFileName(attachmentPath), "EpdCadResult");
                    taskInfo.CADAttachmentPath = id.ToString();
                    //DBServiceClient.Instance.EGIPlatformService.SaveTaskModel(taskId.ToString(), this.ServiceConfigInfo.CadWsRemark, TemplateRunTaskType.IsTest(this.TaskType) ? "Test" : "Formal", buf);
                    //上传成功!
                    SendLogMessage(taskId, LanguageHelper.GetString("UploadSucceeded") + id.ToString(), 0);

                }
                //Directory.Delete(newPath, true);
            }
            else
            {
                taskInfo.CADAttachmentPath = attachmentPath;
                //无须打包上传结果...
                SendLogMessage(taskId, LanguageHelper.GetString("NoNeedPackageUpload"), 1);
            }
        }

        public bool UpdateTaskPartPropWithModel(Guid taskId, List<CADTaskPropFromModel> listTaskPropFromModel, Guid epdTaskId, string cadWorkDirNewpath)
        {
            this.listFile = new List<FileInfo>();
            this.SendLogMessage("UpdateTaskPartPropWithModel....");
            // SentLogErrorMessage(" if (conSer.IsWCFConnect)");
            string newAttAchmentPath = cadWorkDirNewpath;
            string AttachmentPath = string.Format("{0}\\{1}", this.ServiceConfigInfo.WorkPath,
                newAttAchmentPath);
            try
            {
                if (listTaskPropFromModel != null && listTaskPropFromModel.Count > 0)
                {
                    try
                    {
                        this.EdsTaskConnect.UpdatePartProp(epdTaskId,
                            listTaskPropFromModel.Where(o => o.EpdPartGuid.HasValue).GroupBy(o => o.EpdPartGuid).ToDictionary(o => o.Key + "",
                                o =>
                                {
                                    var dic = new Dictionary<string, string>();
                                    foreach (var model in o)
                                    {
                                        if (!string.IsNullOrWhiteSpace(model.ProName))
                                        {
                                            dic[model.ProName] = model.ProValue;
                                        }
                                    }

                                    return dic;
                                }), !TemplateRunTaskType.IsTest(taskType));
                        //this.EdsTaskConnect.SumMassProperty(epdTaskId, this.CadType == 200);
                    }
                    catch (Exception ex)
                    {
                        SendLogMessage("Write back Part Property Failed!!");
                        SentLogErrorMessage("Write back Part Property Failed!!" + ex.ToString());
                    }
                }


                // SentLogErrorMessage(String.Format("taskInfo.TaskId={0}, taskInfo.CadWSId={1}, loginUserInfo.Id={2}, WorkStationType.Cad={3},CADDbConnect.SetTaskComplated={4}...", taskInfo.TaskId, taskInfo.CadWSId, UserId, WorkStationType.Cad, succ));

                var succ = false;
                if (this.taskInfo != null)
                {
                    var isConvertPdfTask = taskInfo.TaskType == TemplateRunTaskType.DWGToPDF;
                    //string newAttAchmentPath1 = this.taskInfo.CADAttachmentPath.Replace("/", "_").Replace("\\", "_");
                    if (isConvertPdfTask) AttachmentPath = newAttAchmentPath;
                    this.SendLogMessage("AttachmentPath=" + AttachmentPath);




                    UploadResult(AttachmentPath, taskInfo.EpdTaskId);

                    // SendLogMessage("0表示磁盘");

                    //foreach (FileInfo info in listFile)
                    //{
                    //    if(File.Exists(info.FullName))
                    //        this.CADDbConnect.UploadFile(File.ReadAllBytes(info.FullName), info.Name);
                    //}


                    //如果是日立，保存工作目录 DXF
                    //cadType 100 是proe
                    //是否复制DXF文件：{0}
                    this.SendLogMessage(string.Format(LanguageHelper.GetString("CopyDxfFile"), this.ServiceConfigInfo.IsCopyDxf));
                    if (!isConvertPdfTask && this.ServiceConfigInfo.IsCopyDxf && this.ServiceConfigInfo.DxfCopyPath != null)
                    {
                        string DXfCopyPath = this.ServiceConfigInfo.DxfCopyPath.TrimEnd('\\') + "\\" +
                                             newAttAchmentPath + "\\";
                        if (!Directory.Exists(DXfCopyPath))
                        {
                            //Directory.Delete(path, true);
                            //Thread.Sleep(1000);
                            Directory.CreateDirectory(DXfCopyPath);

                        }

                        string provider = null;
                        if (!string.IsNullOrEmpty(this.ServiceConfigInfo.ProviderNo))
                        {
                            provider = "_" + this.ServiceConfigInfo.ProviderNo;
                        }

                        foreach (FileInfo info in this.listFile)
                        {
                            File.Copy(info.FullName, DXfCopyPath + info.Name.Trim(info.Extension.ToCharArray()) + provider + info.Extension, true);
                            ;
                        }
                    }

                    foreach (FileInfo info in this.listFile)
                    {
                        this.SendLogMessage(string.Format(LanguageHelper.GetString("SaveError"), info.FullName));
                        
                    }
                    // SendLogMessage(-1, "文件路径; " + taskInfo.AttachmentPath, 2); 


                    if (!isConvertPdfTask && !this.ServiceConfigInfo.IsHaveAttachment)
                    {
                        try
                        {
                            Directory.Delete(AttachmentPath, true);
                            this.SendLogMessage(string.Format(Resources.FolderDeleted, AttachmentPath));
                        }
                        catch (Exception ex)
                        {
                            this.SentLogErrorMessage(ex.ToString());
                        }
                    }

                    this.SendLogMessage("SetTaskComplated start...." + taskInfo.EpdTaskId + "  " + TemplateRunTaskState.CadWsComplete);

                    var epdtaskinfoDto = CADDbConnect.QueryEpdTaskInfoWithParameterByTaskId(this.taskInfo.EpdTaskId.ToString(),
                        TemplateRunTaskType.IsTest(taskType) ? "Test" : "Formal");
                    epdtaskinfoDto.EpdTaskInfoDto.cadRunCount = taskInfo.cadRunCount;
                    epdtaskinfoDto.EpdTaskInfoDto.CADAttachmentPath = taskInfo.CADAttachmentPath;
                    this.SendLogMessage("EpdTaskInfoDto CADAttachmentPath " + taskInfo.CADAttachmentPath);
                    epdtaskinfoDto.EpdTaskInfoDto.CadWsIp = this.ServiceConfigInfo.CurrentIp;
                    //epdtaskinfoDto.EpdTaskInfoDto.Status = TemplateRunTaskState.CadWsComplete;
                    CADDbConnect.SaveEpdTaskInfoEntities(new List<EpdTaskInfoWithParameterDto>() {epdtaskinfoDto});
                    //if (this.ServiceConfigInfo.bMakeEDrawing)
                    //{
                    //    succ = !string.IsNullOrEmpty(this.ServiceConfigInfo.CadWsRemark) ? this.CADDbConnect.SetTaskComplatedByCadType(this.taskInfo.EpdTaskId, this.ServiceConfigInfo.CadWsRemark, TemplateRunTaskState.CadWsCompleteNeedEdrawing) : this.CADDbConnect.SetTaskComplated(this.taskInfo.EpdTaskId, TemplateRunTaskState.CadWsCompleteNeedEdrawing, "{}");

                    //}
                    //else
                    //{
                        succ = !string.IsNullOrEmpty(this.ServiceConfigInfo.CadWsRemark) ? this.CADDbConnect.SetTaskComplatedByCadType(this.taskInfo.EpdTaskId, this.ServiceConfigInfo.CadWsRemark, TemplateRunTaskState.CadWsComplete) : this.CADDbConnect.SetTaskComplated(this.taskInfo.EpdTaskId, TemplateRunTaskState.CadWsComplete, "{}");

                    //}
                    this.SendLogMessage("SetTaskComplated end  " + succ);
                    //是否回写状态：{0}
                    this.SendLogMessage(string.Format(LanguageHelper.GetString("WriteBackStatus"), this.ServiceConfigInfo.bCADReturnStatus));
                    if (this.ServiceConfigInfo.bCADReturnStatus == true)
                    {
                        DwgWebApiinfo rootclass = new DwgWebApiinfo();
                        rootclass.EPD_taskId = taskInfo.EpdTaskId;
                        rootclass.FilePath = new List<string>();
                        if (!string.IsNullOrEmpty(this.ServiceConfigInfo.CadWsRemark))
                        {
                            bool istest = TemplateRunTaskType.IsTest(taskType);
                            switch (this.ServiceConfigInfo.CadWsRemark)
                            {
                                case "AutoCAD":
                                    rootclass.TaskType = istest ? "100" : "600";
                                    break;
                                case "Revit":
                                    rootclass.TaskType = istest ? "100" : "2200";
                                    break;
                                case "SolidWorks":
                                    rootclass.TaskType = istest ? "100" : "300";
                                    break;
                                default:
                                    rootclass.TaskType = this.taskInfo.TaskType.ToString();
                                    break;
                            }
                        }
                        else
                        {
                            rootclass.TaskType = this.taskInfo.TaskType.ToString();
                        }

                        foreach (var info in listFile)
                        {
                            if (info.FullName.ToLower().EndsWith("pdf") || info.FullName.ToLower().EndsWith("jpg") || info.FullName.ToLower().EndsWith("dwg") || info.FullName.ToLower().EndsWith("rfa") ||  this.FileTypeListToSer.Contains(Path.GetExtension(info.FullName).ToUpper()))

                                {
                                    rootclass.FilePath.Add(Path.GetFileName(info.FullName));
                            }
                        }

                        if (string.IsNullOrEmpty(this.taskInfo.CallbackUrl))
                        {
                            //获取回写协议通过配置文件：{0}
                            SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolByFile"), this.ServiceConfigInfo.SendbackUrl));

                            bool getstatus = RequestWebAPI(Convert.ToString(this.taskInfo.EpdTaskId), this.ServiceConfigInfo.SendbackUrl);
                            //获取回写协议状态：{0}
                            SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolStatus"), getstatus));

                            getstatus = PostWebapi(getJsonByObject(rootclass), this.ServiceConfigInfo.SendbackUrl);
                            //获取回写协议状态：{0}
                            SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolStatus"), getstatus));
                        }
                        else
                        {
                            //获取回写协议通过前系统EDS传递：{0}
                            SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolByEDS"), this.taskInfo.CallbackUrl));
                            bool getstatus = RequestWebAPI(Convert.ToString(this.taskInfo.EpdTaskId), this.taskInfo.CallbackUrl);
                            SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolStatus"), getstatus));
                            getstatus = PostWebapi(getJsonByObject(rootclass), this.taskInfo.CallbackUrl);
                            SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolStatus"), getstatus));
                        }


                    }

                    if (ServiceConfigInfo.ProviderNo != null)
                    {

                    }
                    //发送邮件：{0}
                    this.SendLogMessage(string.Format(LanguageHelper.GetString("SendMail"), this.ServiceConfigInfo.bSendMail));
                    if (this.ServiceConfigInfo.bSendMail == true)
                    {
                        try
                        {
                            //您有CAD任务
                            bool rt = this.CADDbConnect.CADSendMail(taskId, LanguageHelper.GetString("HasCadTaskService"));

                        }
                        catch (Exception ex)
                        {
                            // SendLogMessage("请检查邮件服务器");
                            //SentLogErrorMessage(ex.ToString() + ex.StackTrace); 
                            // SentLogErrorMessage("请检查邮件服务：" + this.ServiceConfigInfo.SendMailServer
                            //     +"发生错误：" + ex.ToString() 
                            //     + "错误发生在：" + ex.StackTrace);
                        }

                    }

                }
                else
                {
                    this.SentLogErrorMessage("UpdateTaskPartPropWithModel()  task is null! ");
                }

                // 
                // {
                ReloadCadWorkstation(taskId, AttachmentPath);
                // }
                return succ;
            }
            catch (CommunicationException ex)
            {
                //
                this.SentLogErrorMessage("UpdateTaskPartPropWithModel()   " + ex.ToString() + ex.StackTrace);
                this.toolStripServerConnect.Text = Resources.NotConnected;
                this.IsWCFConnect = false;
                return false;
            }
            catch (Exception ex)
            {
                SentLogErrorMessage(ex.ToString() + ex.StackTrace);
                return false;
            }
            finally
            {
                FreeMailTime();
                SaveCadLogFile(this.logPath, taskId, AttachmentPath);

            }

            this.Activate();
            this.Refresh();
            return false;
        }
        /// <summary>
        /// 转化JSon字符串
        /// </summary>

        /// <param name="obj">对象</param>
        private string getJsonByObject(Object obj)
        {
            //实例化DataContractJsonSerializer对象，需要待序列化的对象类型
            DataContractJsonSerializer serializer = new DataContractJsonSerializer(obj.GetType());
            //实例化一个内存流，用于存放序列化后的数据
            MemoryStream stream = new MemoryStream();
            //使用WriteObject序列化对象
            serializer.WriteObject(stream, obj);
            //写入内存流中
            byte[] dataBytes = new byte[stream.Length];
            stream.Position = 0;
            stream.Read(dataBytes, 0, (int)stream.Length);
            //通过UTF8格式转换为字符串
            string jsoninfo = Encoding.UTF8.GetString(dataBytes);
            return jsoninfo;

        }
        public void ReloadCadWorkstation(Guid taskId, string attachmentPath)
        {
            if (IsCadWsAuto)
            {
                try
                {
                    if (isAuto && IsAllowReloadProeWs() && cadWs.IsReachTimerOrTimes())
                    {
                        SaveCadLogFile(this.logPath, taskId, attachmentPath);

                        if (isAuto)
                        {
                            if (taskInfo != null)
                            {
                                //todo: 向中间文件中写入[Done!]命令
                                if (taskInfo.Failed.HasValue && taskInfo.Failed.Value)
                                {
                                    WriteCmd("[Failed!]");
                                }
                                else
                                {
                                    WriteCmd("[Done!]");
                                }

                            }
                        }
                        //todo: kill掉Zxtech.CAD.MQListener
//                            var process = Process.GetProcessesByName("Zxtech.CAD.MQListener.exe");
//                        if (process.Any())
//                        {
//                            foreach (var process1 in process)
//                            {
//                                process1.Kill();
//                            }
//                        }
                        Thread.Sleep(3000);
                        this.cadWs.ReloadProe();
                        if (File.Exists(logPath) && taskId != Guid.Empty)
                        {
                            SaveCadLogFile(this.logPath, taskId, attachmentPath);
                        }
                        else
                        {

                        }
                    }
                }
                catch (Exception ex)
                {
                    //守护程序：
                    SentLogErrorMessage(LanguageHelper.GetString("Daemons") + ex.ToString());
                }

            }
           
        }
        public void ReloadProe()
        {
            try
            {
                SentLogErrorMessage("守护程序：ReloadProe0");
                if (IsAllowReloadProeWs())
                {
                    this.cadWs.ReloadProe();
                }
                SentLogErrorMessage("守护程序：ReloadProe1");
            }
            catch (Exception ex)
            {
                SentLogErrorMessage("守护程序：" + ex.Message);
            }
        }
        private bool PostWebapi(string dwgJson, string url)
        {
            HttpWebRequest request = null;
            StreamReader reader = null;
            try
            {
                //获取Web——URL：{0},DWGINfo:{1}
                SendLogMessage(string.Format(LanguageHelper.GetString("GetWebUrl"), url, dwgJson));
                request = (HttpWebRequest)WebRequest.Create(url);

                request.Method = "POST";
                request.ContentType = "application/json";
                request.Timeout = 4000 * 60;
                //string strContent = JsonConvert.SerializeObject(dwgJson);
                using (StreamWriter dataStream = new StreamWriter(request.GetRequestStream()))
                {
                    dataStream.Write(dwgJson);
                    dataStream.Close();
                }

                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                Thread.Sleep(1000);
                string encoding = response.ContentEncoding;
                if (encoding == null || encoding.Length < 1)
                {
                    encoding = "UTF-8"; // 默认编码
                }

                reader = new StreamReader(response.GetResponseStream(), Encoding.GetEncoding(encoding));
                string retString = reader.ReadToEnd();
                retString = retString.Replace("\\", string.Empty);
                retString = retString.Trim('\"');
                bool status = GetBool(retString);
                return status;
            }
            catch (Exception e)
            {
                //获取Web服务错误：{0}
                SendLogMessage(string.Format(LanguageHelper.GetString("GetWebServiceError"), e.ToString()));
                if (reader != null) reader.Close();
                if (request != null) request.Abort();
                return false;
            }
            finally
            {
                if (reader != null) reader.Close();
                if (request != null) request.Abort();
            }

            return false;
        }
        private bool RequestWebAPI(string taskId, string url)
        {
            try
            {
           
                HttpWebRequest request = (HttpWebRequest)HttpWebRequest.Create(string.Format("{0}?EPD_taskId={1}",url, taskId));

                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                Stream getStream = response.GetResponseStream();

                StreamReader sr = new StreamReader(getStream);
                var text = sr.ReadToEnd();
            }
            catch (Exception e)
            {
                //获取Web服务错误：{0}
                SendLogMessage(string.Format(LanguageHelper.GetString("GetWebServiceError"), e.ToString()));
                return false;
            }
            return true;
        }

        private void SaveCadLogFile(string logFilePath,Guid taskId, string workPath)
        {
            try
            {
                //保存任务信息
                SendLogMessage(LanguageHelper.GetString("SaveTaskInfo"));
                var epdtaskinfoDto = CADDbConnect.QueryEpdTaskInfoWithParameterByTaskId(this.taskInfo.EpdTaskId.ToString(),
                    TemplateRunTaskType.IsTest(taskType) ? "Test" : "Formal");
                if (!File.Exists(logFilePath))
                {
                    //日志不存在‘{0}’
                    SentLogErrorMessage(string.Format(LanguageHelper.GetString("LogDoesnotExist"), logPath));
                }
                else
                {
                    Byte[] bytes = File.ReadAllBytes(logFilePath);
                    var fileId = CADDbConnect.UploadFile(bytes, Path.GetFileName(logFilePath));
                    epdtaskinfoDto.EpdTaskInfoDto.CADRunLogId = fileId;
                    try
                    {
                        var newPath = Path.Combine(workPath, Path.GetFileName(logFilePath));
                        File.Copy(logFilePath, newPath);
                        //日志截断
                         string maxLogFileSizeStr = this.ServiceConfigInfo.MaxLogFileSize; // 5MB  
                        if (!string.IsNullOrEmpty(maxLogFileSizeStr))
                        {
                            long maxLogFileSize = (long)((double.Parse(maxLogFileSizeStr) * 1024 * 1024));
                            if (File.Exists(logFilePath))
                            { 
                                long fileSize = new FileInfo(logFilePath).Length; 
                                if (fileSize >= maxLogFileSize)
                                {
                                    string newFileName = Path.GetFileNameWithoutExtension(logFilePath) + "_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".txt";
                                    string newLogFilePath = Path.Combine(Path.GetDirectoryName(logFilePath), newFileName);
                                    File.Move(logFilePath, newLogFilePath); 
                                }
                            }
                        }
                    }
                    catch (Exception e)
                    {
                    }
                }

                if (!epdtaskinfoDto.EpdTaskInfoDto.cadRunCount.HasValue || (taskInfo.cadRunCount.HasValue &&
                    taskInfo.cadRunCount.Value > epdtaskinfoDto.EpdTaskInfoDto.cadRunCount.Value))
                {
                    epdtaskinfoDto.EpdTaskInfoDto.cadRunCount = taskInfo.cadRunCount;
                }
                if (!string.IsNullOrEmpty(workPath))
                {
                    epdtaskinfoDto.EpdTaskInfoDto.CADAttachmentPath = taskInfo.CADAttachmentPath;
                }
                epdtaskinfoDto.EpdTaskInfoDto.CadWsIp = this.ServiceConfigInfo.CurrentIp;
                //epdtaskinfoDto.EpdTaskInfoDto.Status = TemplateRunTaskState.CadWsComplete;
                CADDbConnect.SaveEpdTaskInfoEntities(new List<EpdTaskInfoWithParameterDto>() { epdtaskinfoDto });
                //压缩log日志
                //var logZip = GZip(bytes, CompressionMode.Compress);
                // SendLogMessage("压缩log日志");
                //上传CAD运行日志暂时关闭
                //var fileId = this.CADDbConnect.UploadFile(bytes, taskId.ToString());
                //this.CADDbConnect.SaveCADTaskRunLogFile(taskId, fileId);
                //保存任务信息完成
                SendLogMessage(LanguageHelper.GetString("SaveTaskInfoSuccess"));
                //邮件发送
                //SendMailFun.MessageCenterClient mes = new SendMailFun.MessageCenterClient();
                // bool rt = mes.CADSendMail(taskId, "您有CAD任务");
            }
            catch (Exception ex)
            {
                SentLogErrorMessage(ex.ToString() + ex.StackTrace);
            }
        }
        public string SendOperation3dError(Operation3dErrorRecordEPDRequest operation3DErrorRecordEPDRequest)
        {

            return CADDbConnect.ObtainDataStationInformation(operation3DErrorRecordEPDRequest);
        }
        //public void SendOperation3dError(string  str)
        //{

        //     CADDbConnect.ObtainDataStationInformation(str);
        //}

        private void btnStart_Click(object sender, EventArgs e)
        {

            try
            {

                SetAutoRunToobar();
                if (IsAllowReloadProeWs())
                {
                    //SaveCadWsLoadInfo();
                    this.IsCadWsAuto = true;
                    this.AutoGetNewCADTask(null, null);
                    cadWs.StartWatch();
                    return;
                    // toolStripButtonGetTask_Click(null, null);

                }
                this.AutoGetNewCADTask(null, null);

            }
            catch (Exception ex)
            {
                SentLogErrorMessage(ex.ToString());
            }
        }

        private void SetAutoRunToobar()
        {
            this.ServerState = ServerState.Start;
            this.btnStart.Enabled = false;
            this.btnStop.Enabled = true;
            //タスクサービス：実行中……
            this.labelInfo.Text = LanguageHelper.GetString("TaskServiceRunning");


            this.toolStripButtonGetTask.Visible = false;
            this.toolStripButtonGetTaskSelect.Visible = false;
            this.toolStripButtonPriority.Visible = false;
            // this.AutoGetNewCADTask(sender, e);
            this.isAuto = true;
            this.Refresh();
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            DialogResult r = MessageBox.Show(this, Resources.ExitInfo, "", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (r == DialogResult.Yes)
            {
               
                try
                {
                    this.Close();
                  
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.ToString());
                }
            }
        }

        private void FormCADTaskServer_Load(object sender, EventArgs e)
        {
            //gd20110621
            // System.Diagnostics.Process.Start("AutoUpdateClient.exe");
            //Cadws
            this.IsCadWsAuto = GetCadWs();
            if (!this.IsCadWsAuto)
            {
                var tempFile = Path.Combine(Path.GetDirectoryName(this.GetType().Assembly.Location), "RabbiMQ", "TaskInfo", "taskInfo_" + Process.GetCurrentProcess().Id + ".info");
                if (Process.GetProcesses().Any(o => o.ProcessName.ToLower().StartsWith("zxtech.cad.mqlistener")) && File.Exists(tempFile) && (DateTime.Now - new FileInfo(tempFile).LastWriteTime).TotalSeconds < 180)
                {
                    this.IsCadWsAuto = true;
                }
            }
            if (this.IsCadWsAuto)
            {
                this.AutoGetNewCADTask(sender, e);
                SetAutoRunToobar();
                return;
            }


            //タスクサービス：停止
            this.labelInfo.Text = LanguageHelper.GetString("TaskServiceStop");

            this.btnExit.Enabled = true;

            var pdsPath = this.ServiceConfigInfo.PdsPath;
            if (pdsPath != null)
            {
                pdsPath.TrimEnd('\\');
                if (!Directory.Exists(pdsPath))
                {
                    Directory.CreateDirectory(pdsPath);
                    //SendLogMessage(string.Format("设置模型路径不存在 {0}", this.ServiceConfigInfo.PdsPath));
                    //btnStart.Enabled = false;
                    //toolStripButtonGetTask.Enabled = false;
                    //toolStripButtonGetTaskSelect.Enabled = false;
                }
            }

            btnStart.Enabled = true;
        }

        private void FormCADTaskServer_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                //if (this.loginUserInfo != null)
                //{
                //    if (this.IsWCFConnect)
                //    {
                //        this.CADDbOnLineConnect.UserLogout(this.loginUserInfo.Id);
                //    }
                //}//0513

                this.ServerState = ServerState.Exit;
                if (IsAllowReloadProeWs())
                    cadWs.StopWatch();
                e.Cancel = false;
            }
            catch (Exception ex)
            {
                SentLogErrorMessage(ex.ToString());
            }
        }

        private String GetTaskType(String value)
        {
           // FormCADTaskServer ss = new FormCADTaskServer();
            //if (ServiceConfigInfo.Language == "ja-JP" || ServiceConfigInfo.Language.Contains("JP"))
            //{
            //    switch (value)
            //    {
            //        case "100":
            //            return "開発テスト";
            //            break;
            //        case "200":
            //            return "業務テスト";
            //            break;
            //        case "300":
            //            return "業務正式実行";
            //            break;
            //        case "400":
            //            return "LDS開発テスト";
            //            break;
            //        case "500":
            //            return "LDS業務テスト";
            //            break;
            //        case "600":
            //            return "LDS業務実行";
            //            break;
            //        case "1000":
            //            return "DDS開発テスト";
            //            break;
            //        case "1100":
            //            return "DDS業務テスト";
            //            break;
            //        case "1200":
            //            return "DDS業務実行";
            //            break;
            //        case "2000":
            //            return "BIM開発テスト";
            //            break;
            //        case "2100":
            //            return "BIM業務テスト";
            //            break;
            //        case "2200":
            //            return "BIM業務実行";
            //            break;
            //        default:
            //            return "業務正式実行";
            //    }
            //}
            //else
            //{
                switch (value)
                {
                    case "100":

                        return TemplateRunTaskType.ConvertToString(100);
                        break;

                    case "200":
                        return TemplateRunTaskType.ConvertToString(200);
                        break;
                    case "300":
                        return TemplateRunTaskType.ConvertToString(300);
                        break;
                    case "400":
                        return TemplateRunTaskType.ConvertToString(400);
                        break;
                    case "500":
                        return TemplateRunTaskType.ConvertToString(500);
                        break;
                    case "600":
                        return TemplateRunTaskType.ConvertToString(600);
                        break;
                    case "1000":
                        return TemplateRunTaskType.ConvertToString(1000);
                        break;
                    case "1100":
                        return TemplateRunTaskType.ConvertToString(1100);
                        break;
                    case "1200":
                        return TemplateRunTaskType.ConvertToString(1200);
                        break;
                    case "2000":
                        return TemplateRunTaskType.ConvertToString(2000);
                        break;
                    case "2100":
                        return TemplateRunTaskType.ConvertToString(2100);
                        break;
                    case "2200":
                        return TemplateRunTaskType.ConvertToString(2200);
                        break;
                    default:
                        return TemplateRunTaskType.ConvertToString(300);
                        break;
                }
            //}
            
        }

        private void toolStripButtonGetTask_Click(object sender, EventArgs e)
        {
            this.isAuto = false;
            this.isSelectedTaskInfo = false;
            this.GetNewCADTask(sender, e);
            //gaodan 2008/12/12

        }



        private List<FileInfo> GetFileInfo(string dir, string[] fileTypes)
        {
            var fileinfos = new List<FileInfo>();
            var dirInfo = new DirectoryInfo(dir);
            if (dirInfo.Exists)
            {
                if (fileTypes == null)
                {
                    FileInfo[] _files = dirInfo.GetFiles();
                    foreach (FileInfo fileinfo in _files)
                    {
                        fileinfos.Add(fileinfo);
                    }
                }
                else
                {
                    foreach (string fileType in fileTypes)
                    {
                        if (!String.IsNullOrEmpty(fileType))
                        {
                            FileInfo[] _files = dirInfo.GetFiles("*." + fileType);
                            foreach (FileInfo fileinfo in _files)
                            {
                                fileinfos.Add(fileinfo);
                            }
                        }
                    }
                }
            }
            return fileinfos;
        }


        private Guid? GetEpdPartGuid(string fileName)
        {
            if (this.FileGuidMap.ContainsKey(fileName))
                return this.FileGuidMap[fileName];
            return null;
        }

      

        private Binary GetBin(String filename)
        {
            try
            {
                var f = GZip(File.ReadAllBytes(filename), CompressionMode.Compress);
                if (f != null)
                    return new Binary(f);
            }
            catch (Exception ex)
            {
                SentLogErrorMessage(ex.ToString() + ex.StackTrace);
            }
            return null;
            
        }
        /// 压缩 var r=GZip(要压缩的数据，CompressionMode.Compress)
        /// 解压 var r=GZip(要解压数据，CompressionMode.DeCompress)
          /// <summary>
        /// 提供内部使用压缩字流的方法
        /// </summary>
        /// <param name="data">字节</param>
        /// <param name="mode">解压或压缩</param>
        /// <returns>结果</returns>

        public static byte[] GZip(byte[] data, CompressionMode mode)
        {
            GZipStream zip = null;
            try
            {
                if (mode == CompressionMode.Compress)
                {
                    var ms = new MemoryStream();
                    zip = new GZipStream(ms, mode, true);
                    zip.Write(data, 0, data.Length);
                    zip.Close();
                    return ms.ToArray();
                }
                else
                {
                    var ms = new MemoryStream();
                    ms.Write(data, 0, data.Length);
                    ms.Flush();
                    ms.Position = 0;
                    zip = new GZipStream(ms, mode, true);
                    var os = new MemoryStream();
                    var SIZE = 1024;
                    var buf = new byte[SIZE];
                    var l = 0;
                    do
                    {
                        l = zip.Read(buf, 0, SIZE);
                        if (l == 0) l = zip.Read(buf, 0, SIZE);
                        os.Write(buf, 0, l);
                    } while (l != 0);
                    zip.Close();
                    return os.ToArray();
                }
            }
            catch
            {
               return null;
            }
            finally
            {
                if (zip != null) zip.Close();
            }
        }



        public void SetFile(string AttachmentPath)
        {
           
            this.FileTypeListToSer.ForEach((p) =>
            {
                var files = new DirectoryInfo(AttachmentPath).GetFiles("*"+p, SearchOption.AllDirectories);
                this.listFile.AddRange(files);
            });
            //this.listFile.AddRange(new DirectoryInfo(AttachmentPath).GetFiles("Result.json", SearchOption.AllDirectories));
            if (!string.IsNullOrEmpty(this.taskInfo.CADWsId))
            {
                var files = new DirectoryInfo(AttachmentPath).GetFiles("*.dxf", SearchOption.AllDirectories);
                this.listFile.AddRange(files);
            }

          
        }



        public void ListFiles(FileSystemInfo fileinfo)
        {

            if (!fileinfo.Exists)
            {
                return;
            }

            var dirinfo = fileinfo as DirectoryInfo;

            if (dirinfo == null)
            {
                return; //不是目录 
            }


            FileSystemInfo[] files = dirinfo.GetFileSystemInfos();

            for (int i = 0; i < files.Length; i++) //遍历目录下所有文件、子目录 
            {
                var file = files[i] as FileInfo;

                if (file != null) // 是文件 
                {
                    if (this.FileTypeListToSer.Contains(file.Extension.ToUpper()))
                    {
                        this.listFile.Add(file);
                    }
                }


                else //是目录 
                {
                    this.ListFiles(files[i]); //对子目录进行递归调用 
                }
            }
        }

        private void rtbRunInfo_TextChanged(object sender, EventArgs e)
        {

        }

        private void toolStripButtonGetTaskSelect_Click(object sender, EventArgs e)
        {
            try
            {
                var criteria = GetHandWereString(false);
                SendLogMessage(criteria.ToQueryString());
                //    string orderby = "Pri,CreateTime";
                string orderby = "CreateTime descending";
                List<EpdTaskInfoDto> ListTaskInfo=new List<EpdTaskInfoDto>();
                ListTaskInfo = !string.IsNullOrEmpty(this.ServiceConfigInfo.CadWsRemark) ? this.CADDbConnect.QueryCADTaskInfosByCadTypeAndCriteria(criteria,this.ServiceConfigInfo.CadWsRemark, TemplateRunTaskType.IsTest(taskType) ? "Test" : "Formal") : this.CADDbConnect.QueryCADTaskInfosByCriteria(criteria, TemplateRunTaskType.IsTest(taskType) ? "Test" : "Formal");
                

                if (ListTaskInfo != null)
                {
                    ListTaskInfo = ListTaskInfo.Where(o => o.Status != 400 || (o.Status == 400 && o.UpdateTime.HasValue && o.UpdateTime.Value < DateTime.Now.AddMinutes(-this.ServiceConfigInfo.Status400RerunAfterMinutes))).OrderByDescending(o => o.CreateTime).ToList();
                }
                FormTaskInfoSelect formSelect = new FormTaskInfoSelect(ListTaskInfo);

                if (formSelect.ShowDialog() == DialogResult.OK)
                {
                    //if (formSelect.selectedTaskInfo != null)
                    //{
                    //    this.selectedTaskInfo = formSelect.selectedTaskInfo;
                    //    this.selectedTaskInfo.CreateUser = formSelect.selectedTaskInfo.CreateUser;
                    //    this.isSelectedTaskInfo = true;
                    //    this.isAuto = false;
                    //    this.GetNewCADTask(sender, e);
                    //}

                    if (formSelect.selectedTaskInfos != null&& formSelect.selectedTaskInfos.Count >0)
                    {
                        foreach (var selectedTaskInfo in formSelect.selectedTaskInfos)
                        {
                            this.selectedTaskInfo = selectedTaskInfo;
                            this.selectedTaskInfo.CreateUser = selectedTaskInfo.CreateUser;
                            this.isSelectedTaskInfo = true;
                            this.isAuto = false;
                            this.GetNewCADTask(sender, e);
                        }
                        
                    }
                }
            }
            catch (Exception ex)
            {
                SentLogErrorMessage(ex.ToString() + ex.StackTrace);
            }
        }

        public bool GetCadWs()
        {
            try
            {
                var IsAllowReloadProe = IsAllowReloadProeWs();
                if (!IsAllowReloadProe) return false;
                if (cadWs.NaturalExit()) return false;

                return true;

            }
            catch (Exception ex)
            {
                cadWs = null;
                cadWsFactory = null;
                SentLogErrorMessage(ex.ToString() + ex.StackTrace);
                return false;
            }


        }

        private string GetBasePath()
        {
            string fileName =basePath+ "\\Zxtech.PdsCadWsOutsideControl.exe.config";
            if (!File.Exists(fileName)) return string.Empty;
            string configText=File.ReadAllText(fileName);
             string regexStr = "baseAddress=\"(.*)\"";
            var re = new Regex(regexStr, RegexOptions.IgnoreCase);
            Match mc = re.Match(configText);
            string baseAddress = "";
            string address = "";
            if (mc.Success)
            {
                baseAddress=mc.Groups[1].ToString();
            }
            else
            { return string.Empty; }

            regexStr = "\\s+Address=\"(\\w+)\"\\s+";
            re = new Regex(regexStr, RegexOptions.IgnoreCase);
             mc = re.Match(configText);
             if (mc.Success)
             {
                 address= mc.Groups[1].ToString();
             }
             else
              { return string.Empty; }
             if (!string.IsNullOrEmpty(baseAddress) && !string.IsNullOrEmpty(address))
                 return baseAddress + address;
            return string.Empty;
        
        }

        private bool IsAllowReloadProeWs()
        {
            try
            {
                string path = GetBasePath();
                if (string.IsNullOrEmpty(path))
                    path = @"net.tcp://localhost:530/CadService/Service";
          
                ServiceEndpoint NetEndpoint;
                var netTcpBinding = new NetTcpBinding();
                netTcpBinding.Security.Mode = SecurityMode.None;
                NetEndpoint = new ServiceEndpoint(ContractDescription.GetContract(typeof(ICadWsOutsideControl)), netTcpBinding, new EndpointAddress(path));

                ;
                cadWsFactory = new ChannelFactory<ICadWsOutsideControl>(NetEndpoint);
                


                //创造远程对象
                cadWs = cadWsFactory.CreateChannel();

                var IsAllowReloadProe = cadWs.IsAllowReloadProe();
                return IsAllowReloadProe;

            }
            catch (Exception ex)
            {
               // SendLogMessage("CAD 服务连接失败!");
                cadWs = null;
                return false;
            }
          
        }
        private string GetTcpPath()
        {
            try
            {
                string configTxt = File.ReadAllText(this.CadWsOutConfigName);
                var re2 = new Regex(@"net\.tcp://[^:\s]+:(\d+)/CadService", RegexOptions.None);
                var match = re2.Match(configTxt);
                if (match.Success)
                {
                    return string.Format(@"net.tcp://localhost:{0}/CadService/Service", match.Groups[1]);
                }
            }
            catch (Exception ex)
            {
                SentLogErrorMessage(ex.ToString() + ex.StackTrace);
            }
            return @"net.tcp://localhost:530/CadService/Service";
        }

        private void FormCADTaskServer_Shown(object sender, EventArgs e)
        {
         //   if (IsCadWsAuto) this.Hide();
        }
        public  T DeserializeDataContractFromStream<T>(Stream stream)  //反序列化
        {
            try
            {
                var dcs = new DataContractSerializer(typeof(T));

                var listSeries = (T)dcs.ReadObject(stream);
                stream.Close();
                return listSeries;
              
            }
            catch(Exception ex)
            {
                SentLogErrorMessage(ex.ToString()+ex.StackTrace);
                return default(T);
            }
        }
        /// <summary>
        /// 按属性名复制的方式，将对象转换为另一个对象。
        /// </summary>
        /// <param name="srcType">源类型</param>
        /// <param name="destType">目标类型</param>
        /// <param name="srcObject">源对象</param>
        /// <returns>目标对象</returns>
        public static object ConvertObject(Type srcType, Type destType, object srcObject)
        {
            var srcProps = srcType.GetProperties();

            var destObject = Activator.CreateInstance(destType);
            if (destObject == null)
            {
                throw new Exception("error data");
            }

            foreach (var prop in srcProps)
            {
                var destProp = destType.GetProperty(prop.Name);

                if (destProp != null)
                {
                    destProp.SetValue(destObject, prop.GetValue(srcObject, null), null);
                }
            }

            return destObject;
        }
        public void SetModelError()
        {
            SetModelError(TemplateRunTaskState.CadWsNoComplete);
        }

        //per发送邮件
        /// <summary>
        /// 发送错误提醒邮件
        /// </summary>
        /// <param name="finished">之前没有这个参数,如果这个是true表示任务完成了但是存在错误,暂时还是将这个状态设置成500完成</param>
        public void SetModelError(int CadWsState)
        {
            if (this.taskInfo != null)
            {
                if (CadWsState == TemplateRunTaskState.CadWsCompleteButExitError)
                {
                    if (!string.IsNullOrEmpty(this.ServiceConfigInfo.CadWsRemark))
                    {
                        bool succ = this.CADDbConnect.SetTaskComplatedByCadType(this.taskInfo.EpdTaskId, this.ServiceConfigInfo.CadWsRemark, TemplateRunTaskState.CadWsCompleteButExitError);
                    }
                    else
                    {
                        bool succ = this.CADDbConnect.SetTaskComplated(taskInfo.EpdTaskId, TemplateRunTaskState.CadWsCompleteButExitError, "{}");
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(this.ServiceConfigInfo.CadWsRemark))
                    {
                        bool succ = this.CADDbConnect.SetTaskComplatedByCadType(this.taskInfo.EpdTaskId, this.ServiceConfigInfo.CadWsRemark, TemplateRunTaskState.CadWsNoComplete);
                    }
                    else
                    {
                        bool succ = this.CADDbConnect.SetTaskComplated(taskInfo.EpdTaskId, TemplateRunTaskState.CadWsNoComplete, "{}");
                    }
                }
               
                

                if (ServiceConfigInfo.IsCallBackForTaskFailed)
                {
                    string url = this.ServiceConfigInfo.SendbackUrl;
                    if (!string.IsNullOrEmpty(this.taskInfo.CallbackUrl)) url = this.taskInfo.CallbackUrl;
                    //"获取回写协议通过前系统EDS传递：{0}"
                    SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolByEDS"), this.taskInfo.CallbackUrl));
                    bool getstatus = RequestWebAPI(Convert.ToString(this.taskInfo.EpdTaskId), this.taskInfo.CallbackUrl);
                    SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolStatus"), getstatus));
                    getstatus = PostWebapi(getJsonByObject(new DwgWebApiinfo() { EPD_taskId = taskInfo.EpdTaskId, IsFailed = true }), this.taskInfo.CallbackUrl);
                    SendLogMessage(string.Format(LanguageHelper.GetString("GetBackProtocolStatus"), getstatus));
                }

                try
                {
                    taskInfo.CadWsIp = this.ServiceConfigInfo.CurrentIp;
                    var path = Path.Combine(Path.GetDirectoryName(this.GetType().Assembly.Location), "MailTemplate");
                    if (Directory.Exists(path))
                    {
                        string content = null;
                        var failedTemplate = Path.Combine(path, taskInfo.OrganizationNum, "fail.html");
                        if (!File.Exists(failedTemplate))
                        {
                            failedTemplate = Path.Combine(path, "fail.html");
                            if (File.Exists(failedTemplate))
                            {
                                content = File.ReadAllText(failedTemplate);
                            }
                        }
                        else
                        {
                            content = File.ReadAllText(failedTemplate);
                        }

                        if (!string.IsNullOrEmpty(content))
                        {
                            string[] lines = null;
                            var mailConfigFile = Path.Combine(path, taskInfo.OrganizationNum, "fail.config");
                            if (!File.Exists(mailConfigFile))
                            {
                                mailConfigFile = Path.Combine(path, "fail.config");
                                if (File.Exists(mailConfigFile))
                                {
                                    lines = File.ReadAllLines(mailConfigFile);
                                }
                            }
                            else
                            {
                                lines = File.ReadAllLines(mailConfigFile);
                            }

                            if (lines != null && lines.Length >= 2)
                            {
                                var title = lines[0];
                                var tos = lines[1];
                                string cc = "";
                                if (lines.Length >= 3)
                                {
                                    cc = lines[2];
                                }
                                var props = taskInfo.GetType().GetProperties();
                                foreach (var prop in props)
                                {
                                    var key = "{" + prop.Name + "}";
                                    var value = prop.GetValue(taskInfo, null) + "";
                                    title = title.Replace(key, value);
                                    tos = tos.Replace(key, value);
                                    if (!string.IsNullOrEmpty(cc))
                                    {
                                        cc = cc.Replace(key, value);
                                    }
                                    content = content.Replace(key, value);
                                }
                                MailMessageDto mailMessageDto = new MailMessageDto();
                                mailMessageDto.Title = title;
                                mailMessageDto.To = tos.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
                                mailMessageDto.Cc = cc.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
                                mailMessageDto.Content = content;
                                mailMessageDto.Attachments = new List<MailAttachmentDto>();
                                logPath = string.Format("{0}\\CadTaskLog\\{2}\\CadTaskLog_{1}.txt", this.basePath, taskInfo.EpdTaskId, RestClientPoxy.Session.OrgnizationNum);
                                if (File.Exists(logPath))
                                {
                                    mailMessageDto.Attachments.Add(new MailAttachmentDto()
                                    {
                                        FileName = Path.GetFileName(logPath),
                                        Content = File.ReadAllBytes(logPath)
                                    });
                                }
                                //发送失败提醒邮件:
                                SendLogMessage(taskInfo.EpdTaskId, LanguageHelper.GetString("SendFailureReminderEmail") + title + Environment.NewLine + "to:" + tos + Environment.NewLine + "cc:" + cc, 1);
                                DBServiceClient.Instance.EGIRbacServiceClient.sendHtmlAttachmentsMail(mailMessageDto);
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    //发送邮件失败
                    SendLogMessage(taskInfo.EpdTaskId, LanguageHelper.GetString("SendMailFail") + e.ToString(), 1);
                }
                taskInfo.Failed = true;
                //WriteCmd("[Failed!]");
                //todo: 向中间文件中写入[Failed!]命令
            }

        }

        private void toolStrip1_ItemClicked(object sender, ToolStripItemClickedEventArgs e)
        {

        }

        private void FreeMailTime()
        {
            if (sentMailTimer != null)
            {
                sentMailTimer.Dispose();
            }
        }
        private void toolStripTaskType_Click(object sender, EventArgs e)
        {

        }

        private void toolStripButton1_Click(object sender, EventArgs e)
        {
            FormParameterSet formPri = new FormParameterSet(LanguageHelper.CurrentConfigLanguage);
            formPri.RefreshConfigHanlder += FormPri_RefreshConfigHanlder;
            formPri.ShowDialog(this);
        }

        private void FormPri_RefreshConfigHanlder(FormParameterSet obj)
        {
            this.GetServiceConfigInfo();
        }

        protected virtual void OnLogHandler(string log)
        {
            LogHandler?.Invoke(log);
        }
    }

    public class DwgWebApiinfo
    {
        /// <summary>
        /// 
        /// </summary>
        public Guid EPD_taskId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public List<string> FilePath { get; set; }
        public string TaskType { get; set; }
        public bool IsFailed { get; set; }
    }

    
    public class WCFServiceConfigInfo
    {
        private string _egiUserNo;
        private string _egiPsw;
        public string EndpointUrl { get; set; }
        public string MaxArrayLength { get; set; }
        public string MaxBytesPerRead { get; set; }
        public string MaxDepth { get; set; }
        public string MaxNameTableCharCount { get; set; }
        public long MaxReceivedMessageSize { get; set; }
        public string MaxStringContentLength { get; set; }
        public string WCFServicePath { get; set; }
        public string AttachmentType { get; set; }

        public string ExpendViewName { get; set; }
        public String PdsPath { get; set; }
        public String WorkPath { get; set; }
        public String CachePath { get; set; }
        public String BakPath { get; set; }
        public String DxfPath { get; set; }
        public String DxfCopyPath { get; set; }
        public string EDrawingPath { get; set; }
        public string PocResultPath { get; set; }
        public string ProviderNo { get; set; }
        public String DWGVersion { get; set; }
        public int PuTimes { get; set; }//pu的次数
        public string MaxLogFileSize { get; set; }
        public bool DeleteSubRevitModel { get; set; }
        public bool AutoAlignDimension { get; set; }
        public bool IsCopyDxf { get; set; }
        public bool bRebuildNewInst { get; set; } // 新的实例
        public bool bRebuildBaseMode { get; set; } // 基本模型
        public bool bRebuildBaseModelInstTable { get; set; } // 基本模型的实例表
        public bool IsUpdateModel { get; set; }
        public string UpdateModelServer { get; set; }
        public bool bIsBackupModel { get; set; }
        public int nFontStrokeMode { get; set; } // PDF输出字体
        public string[] CreateUser { get; set; }
        public string WCFCADServer { get; set; }
        public string SendMailServer { get; set; }
        public bool bMakeDRW { get; set; }
        public bool bMakeDXF { get; set; }
        public bool hasSheetPattern { get; set; }
        public bool bMakeEDrawing { get; set; }
        public string StationType { get; set; }//工作站类型，SldToEDrawing
        public bool bMakePDF { get; set; }
        public bool bMakeDWG { get; set; }
        public bool bDeleteConfig { get; set; }
        public bool bSendMail { get; set; }
        public bool bSend3dError { get; set; }
        public bool InterFerenceDetection { get; set; }
        public bool bDXFBendLine { get; set; }
        public bool bDXFReadThickness { get; set; }
        public bool bCADReturnStatus { get; set; }
        public string SendbackUrl { get; set; }
        public string[] FactoryNOs { get; set; }
        public string[] DrawingNOs { get; set; }
        public bool IsHaveAttachment { get; set; }
        //public bool IsCutline { get; set; }
        //20221202,配置文件要改成多个租户配置是否打断尺寸线 , 不直接获取布尔值, 根据租户名称来获取
        public string IsCutline { get; set; }
        
        public String AttachmentSaveObject { get; set; }

        /// <summary>
        /// 折弯模板默认路径
        /// </summary>
        public string BendAllowanceDefaultPath { get; set; }

        public string WCFEdsServer { get; set; }
        public string EGIService { get; set; }
        public string LoginUrl { get; set; }
        public string DefaultOrgNum { get; set; }
        public bool DeleteNoStandardParam { get; set; }
        public string NoStandardLayer{ get; set; }
        public string NostandardJsonPath { get; set; }
        public  string CreoExePath { get; set; }
        public string EGIUserNo
        {
            get
            {
                if (!string.IsNullOrEmpty(RestClientPoxy.Session.OrgnizationNum))
                {
                    var index = _egiUserNo.IndexOf(RestClientPoxy.Session.OrgnizationNum + "=");
                    if (index >= 0)
                    {
                        var startIndex = index + RestClientPoxy.Session.OrgnizationNum.Length + 1;
                        var endIndex = _egiUserNo.IndexOf(";", index);
                        if (endIndex >= 0)
                        {
                            var userNo = _egiUserNo.Substring(startIndex, endIndex - startIndex);
                            return userNo;
                        }
                        else
                        {
                            var userNo = _egiUserNo.Substring(startIndex);
                            return userNo;
                        }
                    }
                    else
                    {
                        return _egiUserNo;
                    }
                }

                return _egiUserNo;
            }
            set { _egiUserNo = value; }
        }

        public string EGIPsw
        {
            get
            {
                if (!string.IsNullOrEmpty(RestClientPoxy.Session.OrgnizationNum))
                {
                    var index = _egiPsw.IndexOf(RestClientPoxy.Session.OrgnizationNum + "=");
                    if (index >= 0)
                    {
                        var startIndex = index + RestClientPoxy.Session.OrgnizationNum.Length + 1;
                        var endIndex = _egiPsw.IndexOf(";", index);
                        if (endIndex >= 0)
                        {
                            var psw = _egiPsw.Substring(startIndex, endIndex - startIndex);
                            return psw;
                        }
                        else
                        {
                            var psw = _egiPsw.Substring(startIndex);
                            return psw;
                        }
                    }
                    else
                    {
                        return _egiPsw;
                    }
                }

                return _egiPsw;
            }
            set { _egiPsw = value; }
        }

        public string CadWsRemark { get; set; }
        public bool UseDocumentDbResult { get; set; }
        public bool UseDocumentDbTemplate { get; set; }
        public bool UseModelCache { get; set; }
        public bool KeepOpenLastDoc { get; set; }
        public int? ZipMode { get; set; }

        public string Language { get; set; }
        public string CurrentIp { get; set; }

        public bool IsCallBackForTaskFailed { get; set; }
        public int ErrRerunCount { get; set; }
        
        public string SchedulerUri { get; set; }
        public int Status400RerunAfterMinutes { get; set; }
    }

   

    /// <summary>
    /// 任务类型
    /// </summary>
    public struct TemplateRunTaskType
    {
        /// <summary>
        /// 开发测试
        /// </summary>
        public const int DesignTest = 100;

        /// <summary>
        /// 业务测试
        /// </summary>
        public const int BusinessTest = 200;

        /// <summary>
        /// 业务正式运行
        /// </summary>
        public const int BusinessFormal = 300;

        /// <summary>
        /// LDS开发测试
        /// </summary>
        public const int LDSDesignTest = 400;

        /// <summary>
        /// LDS业务测试
        /// </summary>
        public const int LDSBusinessTest = 500;

        /// <summary>
        /// LDS业务正式运行
        /// </summary>
        public const int LDSBusinessFormal = 600;
        ///// <summary>
        ///// POC土建插入子图, 插入标注
        ///// </summary>
        public const int PocInsertDwg = 900;
        ///// <summary>
        ///// 装潢开发测试
        ///// </summary>
        public const int DecDesignTest = 1000;
        ///// <summary>
        ///// 装潢业务测试
        ///// </summary>
        public const int DecBusinessTest = 1100;
        ///// <summary>
        ///// dwg -> pdf
        ///// </summary>
        public const int DWGToPDF = 1190;
        ///// <summary>
        ///// SLD模型 -> 轻量化edrawing,取任务使用
        ///// </summary>
        public const int SLDToEDRAWING = 2300;
        ///// <summary>
        ///// SLD模型 -> 轻量化edrawing，取到1500状态的任务后就修改为1400
        ///// </summary>
        public const int SLDToEDRAWINGRuning = 2400;
        ///// <summary>
        ///// 装潢业务运行
        ///// </summary>
        public const int DecBusinessFormal = 1200;
       
        ///// <summary>
        ///// BIm开发测试
        ///// </summary>
        public const int BIMDesignTest = 2000;
        ///// <summary>
        ///// BIM业务测试
        ///// </summary>
        public const int BIMBusinessTest = 2100;
        ///// <summary>
        ///// BIM业务运行
        ///// </summary>
        public const int BIMBusinessFormal = 2200;
        public static bool IsTest(int taskType)
        {
            return taskType == BusinessTest || taskType == DecBusinessTest || taskType == DecDesignTest || taskType == DesignTest || taskType == LDSBusinessTest || taskType == LDSDesignTest || taskType == BIMDesignTest || taskType == BIMBusinessTest;
        }

        /// <summary>
        /// 转化为说明
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static string ConvertToString(int type)
        {
           
            
                switch (type)
                {
                    case DesignTest:
                        return LanguageHelper.GetString("DesignTest");
                        break;
                    case BusinessTest:
                        return LanguageHelper.GetString("BusinessTest");
                    break;
                    case BusinessFormal:
                        return LanguageHelper.GetString("BusinessFormal");
                    break;
                    case LDSDesignTest:
                        return LanguageHelper.GetString("LDSDesignTest");
                    break;
                    case LDSBusinessTest:
                        return LanguageHelper.GetString("LDSBusinessTest");
                    break;
                    case LDSBusinessFormal:
                        return LanguageHelper.GetString("LDSBusinessFormal");
                    break;
                    case DecDesignTest:
                        return LanguageHelper.GetString("DecDesignTest");
                    break;
                    case DecBusinessTest:
                        return LanguageHelper.GetString("DecBusinessTest");
                    break;
                    case DecBusinessFormal:
                        return LanguageHelper.GetString("DecBusinessFormal");
                    break;
                    case BIMDesignTest:
                        return LanguageHelper.GetString("BIMDesignTest");
                    break;
                    case BIMBusinessTest:
                        return LanguageHelper.GetString("BIMBusinessTest");
                    break;
                    case BIMBusinessFormal:
                        return LanguageHelper.GetString("BIMBusinessFormal");
                    default:
                        return LanguageHelper.GetString("BusinessFormal");
            }
            }
            
        
    }

    public enum ServerState
    {
        Stop,
        Start,
        Exit
    }

    public class MessageRequestDto
    {
        public string GroupKey { get; set; }
        public string Parameter { get; set; }
        public string OrgnizationNo { get; set; }
        public string SessionId { get; set; }
        public string JSessionId { get; set; }
        public string JwtAuth { get; set; }

        public string Code { get; set; }

        public string AppId { get; set; }

        public string UserId { get; set; }

        public string Language { get; set; }

        public string AccessType { get; set; }

        public string AccessCode { get; set; }
    }
}
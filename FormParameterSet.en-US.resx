﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="textBox_OutputFileType.Size" type="System.Drawing.Size, System.Drawing">
    <value>480, 21</value>
  </data>
  <data name="textBox_EgiUrl.Size" type="System.Drawing.Size, System.Drawing">
    <value>480, 21</value>
  </data>
  <data name="EgiUrl.Text" xml:space="preserve">
    <value>EgiUrl</value>
  </data>
  <data name="textBox_SendbackUrl.Size" type="System.Drawing.Size, System.Drawing">
    <value>480, 21</value>
  </data>
  <data name="SendbackUrl.Text" xml:space="preserve">
    <value>ServerIPaddress</value>
  </data>
  <data name="groupBox_Language.Size" type="System.Drawing.Size, System.Drawing">
    <value>480, 32</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>692, 445</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>Configuration</value>
  </data>
  <data name="tableLayoutPanel3.Size" type="System.Drawing.Size, System.Drawing">
    <value>686, 90</value>
  </data>
  <data name="checkBox_MakeSpreadMap.Location" type="System.Drawing.Point, System.Drawing">
    <value>115, 3</value>
  </data>
  <data name="checkBox_MakeSpreadMap.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 24</value>
  </data>
  <data name="checkBox_MakeSpreadMap.Text" xml:space="preserve">
    <value>Export to DXF</value>
  </data>
  <data name="checkBox_MakeProjectMap.Location" type="System.Drawing.Point, System.Drawing">
    <value>575, 33</value>
  </data>
  <data name="checkBox_MakeProjectMap.Size" type="System.Drawing.Size, System.Drawing">
    <value>108, 24</value>
  </data>
  <data name="checkBox_MakeProjectMap.Text" xml:space="preserve">
    <value>Explode blocks</value>
  </data>
  <data name="checkBox_MakeDWG.Location" type="System.Drawing.Point, System.Drawing">
    <value>115, 33</value>
  </data>
  <data name="checkBox_MakeDWG.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 24</value>
  </data>
  <data name="checkBox_MakeDWG.Text" xml:space="preserve">
    <value>Export to DWG</value>
  </data>
  <data name="checkBox_MakePDF.Location" type="System.Drawing.Point, System.Drawing">
    <value>115, 63</value>
  </data>
  <data name="checkBox_MakePDF.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 24</value>
  </data>
  <data name="checkBox_MakePDF.Text" xml:space="preserve">
    <value>Export to PDF</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.Location" type="System.Drawing.Point, System.Drawing">
    <value>258, 3</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.Size" type="System.Drawing.Size, System.Drawing">
    <value>192, 24</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.Text" xml:space="preserve">
    <value>Export sheet metal thickness</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.Location" type="System.Drawing.Point, System.Drawing">
    <value>515, 63</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 24</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.Text" xml:space="preserve">
    <value>Save results to database</value>
  </data>
  <data name="checkBox_IsDXFBendLines.Location" type="System.Drawing.Point, System.Drawing">
    <value>240, 33</value>
  </data>
  <data name="checkBox_IsDXFBendLines.Size" type="System.Drawing.Size, System.Drawing">
    <value>210, 24</value>
  </data>
  <data name="checkBox_IsDXFBendLines.Text" xml:space="preserve">
    <value>Show bend lines in flat pattern</value>
  </data>
  <data name="checkBox_BreakDimensionLines.Location" type="System.Drawing.Point, System.Drawing">
    <value>533, 3</value>
  </data>
  <data name="checkBox_BreakDimensionLines.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 24</value>
  </data>
  <data name="checkBox_BreakDimensionLines.Text" xml:space="preserve">
    <value>Break dimension lines</value>
  </data>
  <data name="tableLayoutPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>686, 335</value>
  </data>
  <data name="tableLayoutPanel4.Size" type="System.Drawing.Size, System.Drawing">
    <value>480, 29</value>
  </data>
  <data name="textBox_WorkPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>429, 21</value>
  </data>
  <data name="button4.Location" type="System.Drawing.Point, System.Drawing">
    <value>438, 3</value>
  </data>
  <data name="tableLayoutPanel2.Size" type="System.Drawing.Size, System.Drawing">
    <value>480, 29</value>
  </data>
  <data name="textBox_PdsPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>429, 21</value>
  </data>
  <data name="button3.Location" type="System.Drawing.Point, System.Drawing">
    <value>438, 3</value>
  </data>
  <data name="label14.Text" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="groupBox_TaskType.Size" type="System.Drawing.Size, System.Drawing">
    <value>483, 34</value>
  </data>
  <data name="radioButton2.Text" xml:space="preserve">
    <value>Test</value>
  </data>
  <data name="radioButton1.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 16</value>
  </data>
  <data name="radioButton1.Text" xml:space="preserve">
    <value>Formal</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>TaskType</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>ModelTemplateLocation</value>
  </data>
  <data name="textBox_EGIPsw.Size" type="System.Drawing.Size, System.Drawing">
    <value>480, 21</value>
  </data>
  <data name="label13.Text" xml:space="preserve">
    <value>ServerIPAddressPassword</value>
  </data>
  <data name="textBox_EGIUserNo.Size" type="System.Drawing.Size, System.Drawing">
    <value>480, 21</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>ModelOutputAddress</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>ServerIPAddressUsername</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>OutputFileType</value>
  </data>
  <data name="button2.Location" type="System.Drawing.Point, System.Drawing">
    <value>629, 467</value>
  </data>
  <data name="button2.Text" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="button1.Location" type="System.Drawing.Point, System.Drawing">
    <value>548, 467</value>
  </data>
  <data name="button1.Text" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>716, 500</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>System settings</value>
  </data>
</root>
﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="toolStripLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 22</value>
  </data>
  <data name="toolStripLabel1.Text" xml:space="preserve">
    <value>任务名称</value>
  </data>
  <data name="toolStripTextBoxFind.Font" type="System.Drawing.Font, System.Drawing">
    <value>Microsoft YaHei UI, 9pt</value>
  </data>
  <data name="toolStripTextBoxFind.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 25</value>
  </data>
  <data name="toolStripButton1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFySURBVDhPjdHPS8JgGAdw7/0LXetWdAi6dgnqUMfoT+jU
        LYgkkDoWEUQXqTzULSQUqZhFHiKQJJUp05BBkyka/kDByRx+2/PODcXN+cBzep7vZ+/DPGadB14wTQ/W
        x4uGbkU7s4tb9ogboGkaA4Jcyh6ZBFC4o6gMEOWGPTIM/NXb2N27GunPb9E6wexB1CgTMMMxQUa02kW4
        3ENQqFrITTCOw9N7Z4AWX9MS1H4fT7/AWRK4zAIhWWGzbKHsDnAVBSERCOhBQrgicFswZkmh6A6ESyou
        UsCzBCSqQL4JfNWMGZ3hCHTVHlt61G/254C3EsDXgZYKNFSNzWLxH2eg1VbYF2gxIrXxoD89rgOZlgHf
        XftR8M05A/QH5EoTYrHGAsN97D1B1LuOTsLHEFvADOfECvi8jERGYi+iZ6/Nz+BgdRn8/oKFDKJGEeAU
        5j4ERN55C8kdLY0jBFCY7pvUJuLbXsHO5sYoQHdN04QYYY/nH2NpYqWPb9BIAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="toolStripButton1.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="toolStripButton1.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 22</value>
  </data>
  <data name="toolStripButton1.Text" xml:space="preserve">
    <value>查询</value>
  </data>
  <data name="toolStripButton1.ToolTipText" xml:space="preserve">
    <value>查询</value>
  </data>
  <data name="toolStripComboBoxPri.Items" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="toolStripComboBoxPri.Items1" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="toolStripComboBoxPri.Items2" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="toolStripComboBoxPri.Items3" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="toolStripComboBoxPri.Items4" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="toolStripComboBoxPri.Items5" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="toolStripComboBoxPri.Items6" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="toolStripComboBoxPri.Items7" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="toolStripComboBoxPri.Items8" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="toolStripComboBoxPri.Items9" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="toolStripComboBoxPri.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 25</value>
  </data>
  <data name="toolStripButtonSetPri.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAIDSURBVDhPpZLrS5NhGMb3j4SWh0oRQVExD4gonkDpg4hG
        YKxG6WBogkMZKgPNCEVJFBGdGETEvgwyO9DJE5syZw3PIlPEE9pgBCLZ5XvdMB8Ew8gXbl54nuf63dd9
        0OGSnwCahxbPRNPAPMw9Xpg6ZmF46kZZ0xSKzJPIrhpDWsVnpBhGkKx3nAX8Pv7z1zg8OoY/cITdn4fw
        bf/C0kYAN3Ma/w3gWfZL5kzTKBxjWyK2DftwI9tyMYCZKXbNHaD91bLYJrDXsYbrWfUKwJrPE9M2M1Oc
        VzOOpHI7Jr376Hi9ogHqFIANO0/MmmmbmSmm9a8ze+I4MrNWAdjtoJgWcx+PSzg166yZZ8xM8XvXDix9
        c4jIqFYAjoriBV9AhEPv1mH/sonogha0afbZMMZz+yreTGyhpusHwtNNCsA5U1zS4BLxzJIfg299qO32
        Ir7UJtZfftyATqeT+8o2D8JSjQrAJblrncYL7ZJ2+bfaFnC/1S1NjL3diRat7qrO7wLRP3HjWsojBeCo
        mDEo5mNjuweFGvjWg2EBhCbpkW78htSHHwRyNdmgAFzPEee2iFkzayy2OLXzT4gr6UdUnlXrullsxxQ+
        kx0g8BTA3aZlButjSTyjODq/WcQcW/B/Je4OQhLvKQDnzN1mp0nnkvAhR8VuMzNrpm1mpjgkoVwB/v8D
        TgDQASA1MVpwzwAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="toolStripButtonSetPri.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="toolStripButtonSetPri.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 22</value>
  </data>
  <data name="toolStripButtonSetPri.Text" xml:space="preserve">
    <value>设置优先级</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="toolStripButtonSetPri.TextImageRelation" type="System.Windows.Forms.TextImageRelation, System.Windows.Forms">
    <value>TextAboveImage</value>
  </data>
  <data name="toolStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="toolStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>829, 25</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="toolStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="toolStrip1.Text" xml:space="preserve">
    <value>toolStrip1</value>
  </data>
  <data name="&gt;&gt;toolStrip1.Name" xml:space="preserve">
    <value>toolStrip1</value>
  </data>
  <data name="&gt;&gt;toolStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStrip1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;toolStrip1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="groupBox1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="chId.Text" xml:space="preserve">
    <value>序号</value>
  </data>
  <data name="chTaskName.Text" xml:space="preserve">
    <value>任务名称</value>
  </data>
  <data name="chTaskName.Width" type="System.Int32, mscorlib">
    <value>108</value>
  </data>
  <data name="chContractName.Text" xml:space="preserve">
    <value>任务标识</value>
  </data>
  <data name="chContractName.Width" type="System.Int32, mscorlib">
    <value>116</value>
  </data>
  <data name="chTemplateName.Text" xml:space="preserve">
    <value>模板名称</value>
  </data>
  <data name="chTemplateName.Width" type="System.Int32, mscorlib">
    <value>149</value>
  </data>
  <data name="chCreator.Text" xml:space="preserve">
    <value>创建者</value>
  </data>
  <data name="chCreator.Width" type="System.Int32, mscorlib">
    <value>114</value>
  </data>
  <data name="chCreateTime.Text" xml:space="preserve">
    <value>创建时间</value>
  </data>
  <data name="chCreateTime.Width" type="System.Int32, mscorlib">
    <value>150</value>
  </data>
  <data name="chPriority.Text" xml:space="preserve">
    <value>优先级</value>
  </data>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>297, 17</value>
  </metadata>
  <data name="ToolStripMenuItemSelectAll.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 22</value>
  </data>
  <data name="ToolStripMenuItemSelectAll.Text" xml:space="preserve">
    <value>全选</value>
  </data>
  <data name="toolStripMenuItemSelectNone.Size" type="System.Drawing.Size, System.Drawing">
    <value>112, 22</value>
  </data>
  <data name="toolStripMenuItemSelectNone.Text" xml:space="preserve">
    <value>全不选</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 48</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Name" xml:space="preserve">
    <value>contextMenuStrip1</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="listViewTaskInfo.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="listViewTaskInfo.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 17</value>
  </data>
  <data name="listViewTaskInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>823, 324</value>
  </data>
  <data name="listViewTaskInfo.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;listViewTaskInfo.Name" xml:space="preserve">
    <value>listViewTaskInfo</value>
  </data>
  <data name="&gt;&gt;listViewTaskInfo.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;listViewTaskInfo.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;listViewTaskInfo.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 28</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>829, 344</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>任务列表</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>132, 17</value>
  </metadata>
  <data name="imageList1.ImageSize" type="System.Drawing.Size, System.Drawing">
    <value>16, 16</value>
  </data>
  <data name="buttonOk.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonOk.Location" type="System.Drawing.Point, System.Drawing">
    <value>650, 389</value>
  </data>
  <data name="buttonOk.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="buttonOk.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="buttonOk.Text" xml:space="preserve">
    <value>确定</value>
  </data>
  <data name="&gt;&gt;buttonOk.Name" xml:space="preserve">
    <value>buttonOk</value>
  </data>
  <data name="&gt;&gt;buttonOk.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonOk.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonOk.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="buttonCancel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonCancel.Location" type="System.Drawing.Point, System.Drawing">
    <value>740, 389</value>
  </data>
  <data name="buttonCancel.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="buttonCancel.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="buttonCancel.Text" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Name" xml:space="preserve">
    <value>buttonCancel</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonCancel.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonCancel.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ja-JP</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>829, 421</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>优先运算</value>
  </data>
  <data name="&gt;&gt;toolStripLabel1.Name" xml:space="preserve">
    <value>toolStripLabel1</value>
  </data>
  <data name="&gt;&gt;toolStripLabel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripTextBoxFind.Name" xml:space="preserve">
    <value>toolStripTextBoxFind</value>
  </data>
  <data name="&gt;&gt;toolStripTextBoxFind.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripTextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripButton1.Name" xml:space="preserve">
    <value>toolStripButton1</value>
  </data>
  <data name="&gt;&gt;toolStripButton1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripComboBoxPri.Name" xml:space="preserve">
    <value>toolStripComboBoxPri</value>
  </data>
  <data name="&gt;&gt;toolStripComboBoxPri.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripButtonSetPri.Name" xml:space="preserve">
    <value>toolStripButtonSetPri</value>
  </data>
  <data name="&gt;&gt;toolStripButtonSetPri.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chId.Name" xml:space="preserve">
    <value>chId</value>
  </data>
  <data name="&gt;&gt;chId.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chTaskName.Name" xml:space="preserve">
    <value>chTaskName</value>
  </data>
  <data name="&gt;&gt;chTaskName.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chContractName.Name" xml:space="preserve">
    <value>chContractName</value>
  </data>
  <data name="&gt;&gt;chContractName.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chTemplateName.Name" xml:space="preserve">
    <value>chTemplateName</value>
  </data>
  <data name="&gt;&gt;chTemplateName.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chCreator.Name" xml:space="preserve">
    <value>chCreator</value>
  </data>
  <data name="&gt;&gt;chCreator.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chCreateTime.Name" xml:space="preserve">
    <value>chCreateTime</value>
  </data>
  <data name="&gt;&gt;chCreateTime.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chPriority.Name" xml:space="preserve">
    <value>chPriority</value>
  </data>
  <data name="&gt;&gt;chPriority.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ToolStripMenuItemSelectAll.Name" xml:space="preserve">
    <value>ToolStripMenuItemSelectAll</value>
  </data>
  <data name="&gt;&gt;ToolStripMenuItemSelectAll.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripMenuItemSelectNone.Name" xml:space="preserve">
    <value>toolStripMenuItemSelectNone</value>
  </data>
  <data name="&gt;&gt;toolStripMenuItemSelectNone.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;imageList1.Name" xml:space="preserve">
    <value>imageList1</value>
  </data>
  <data name="&gt;&gt;imageList1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ImageList, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>FormTaskInfoPriority</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
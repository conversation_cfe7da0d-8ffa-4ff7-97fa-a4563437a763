﻿using System;

namespace Zxtech.CADTaskServer
{
    partial class FormParameterSet
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormParameterSet));
            this.folderBrowserDialog1 = new System.Windows.Forms.FolderBrowserDialog();
            this.textBox_OutputFileType = new System.Windows.Forms.TextBox();
            this.textBox_EgiUrl = new System.Windows.Forms.TextBox();
            this.EgiUrl = new System.Windows.Forms.Label();
            this.textBox_SendbackUrl = new System.Windows.Forms.TextBox();
            this.SendbackUrl = new System.Windows.Forms.Label();
            this.groupBox_Language = new System.Windows.Forms.GroupBox();
            this.radioButton3 = new System.Windows.Forms.RadioButton();
            this.radioButton5 = new System.Windows.Forms.RadioButton();
            this.radioButton6 = new System.Windows.Forms.RadioButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.tableLayoutPanel3 = new System.Windows.Forms.TableLayoutPanel();
            this.checkBox_MakeSpreadMap = new System.Windows.Forms.CheckBox();
            this.checkBox_MakeProjectMap = new System.Windows.Forms.CheckBox();
            this.checkBox_MakeDWG = new System.Windows.Forms.CheckBox();
            this.checkBox_MakePDF = new System.Windows.Forms.CheckBox();
            this.checkBox_IsDXFReadThickness = new System.Windows.Forms.CheckBox();
            this.checkBox_UseDocumentDbResult = new System.Windows.Forms.CheckBox();
            this.checkBox_IsDXFBendLines = new System.Windows.Forms.CheckBox();
            this.checkBox_BreakDimensionLines = new System.Windows.Forms.CheckBox();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.tableLayoutPanel4 = new System.Windows.Forms.TableLayoutPanel();
            this.textBox_WorkPath = new System.Windows.Forms.TextBox();
            this.button4 = new System.Windows.Forms.Button();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.textBox_PdsPath = new System.Windows.Forms.TextBox();
            this.button3 = new System.Windows.Forms.Button();
            this.label14 = new System.Windows.Forms.Label();
            this.groupBox_TaskType = new System.Windows.Forms.GroupBox();
            this.radioButton2 = new System.Windows.Forms.RadioButton();
            this.radioButton1 = new System.Windows.Forms.RadioButton();
            this.label11 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.textBox_EGIPsw = new System.Windows.Forms.TextBox();
            this.label13 = new System.Windows.Forms.Label();
            this.textBox_EGIUserNo = new System.Windows.Forms.TextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label10 = new System.Windows.Forms.Label();
            this.button2 = new System.Windows.Forms.Button();
            this.folderBrowserDialog2 = new System.Windows.Forms.FolderBrowserDialog();
            this.button1 = new System.Windows.Forms.Button();
            this.groupBox_Language.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.tableLayoutPanel3.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.tableLayoutPanel4.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            this.groupBox_TaskType.SuspendLayout();
            this.SuspendLayout();
            // 
            // textBox_OutputFileType
            // 
            resources.ApplyResources(this.textBox_OutputFileType, "textBox_OutputFileType");
            this.textBox_OutputFileType.Name = "textBox_OutputFileType";
            // 
            // textBox_EgiUrl
            // 
            resources.ApplyResources(this.textBox_EgiUrl, "textBox_EgiUrl");
            this.textBox_EgiUrl.Name = "textBox_EgiUrl";
            // 
            // EgiUrl
            // 
            resources.ApplyResources(this.EgiUrl, "EgiUrl");
            this.EgiUrl.Name = "EgiUrl";
            // 
            // textBox_SendbackUrl
            // 
            resources.ApplyResources(this.textBox_SendbackUrl, "textBox_SendbackUrl");
            this.textBox_SendbackUrl.Name = "textBox_SendbackUrl";
            // 
            // SendbackUrl
            // 
            resources.ApplyResources(this.SendbackUrl, "SendbackUrl");
            this.SendbackUrl.Name = "SendbackUrl";
            // 
            // groupBox_Language
            // 
            this.groupBox_Language.Controls.Add(this.radioButton3);
            this.groupBox_Language.Controls.Add(this.radioButton5);
            this.groupBox_Language.Controls.Add(this.radioButton6);
            resources.ApplyResources(this.groupBox_Language, "groupBox_Language");
            this.groupBox_Language.Name = "groupBox_Language";
            this.groupBox_Language.TabStop = false;
            // 
            // radioButton3
            // 
            resources.ApplyResources(this.radioButton3, "radioButton3");
            this.radioButton3.Checked = true;
            this.radioButton3.Name = "radioButton3";
            this.radioButton3.TabStop = true;
            this.radioButton3.UseVisualStyleBackColor = true;
            // 
            // radioButton5
            // 
            resources.ApplyResources(this.radioButton5, "radioButton5");
            this.radioButton5.Name = "radioButton5";
            this.radioButton5.TabStop = true;
            this.radioButton5.UseVisualStyleBackColor = true;
            // 
            // radioButton6
            // 
            resources.ApplyResources(this.radioButton6, "radioButton6");
            this.radioButton6.Name = "radioButton6";
            this.radioButton6.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Controls.Add(this.tableLayoutPanel3);
            this.groupBox1.Controls.Add(this.tableLayoutPanel1);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // tableLayoutPanel3
            // 
            resources.ApplyResources(this.tableLayoutPanel3, "tableLayoutPanel3");
            this.tableLayoutPanel3.Controls.Add(this.checkBox_MakeSpreadMap, 0, 0);
            this.tableLayoutPanel3.Controls.Add(this.checkBox_MakeProjectMap, 2, 1);
            this.tableLayoutPanel3.Controls.Add(this.checkBox_MakeDWG, 0, 1);
            this.tableLayoutPanel3.Controls.Add(this.checkBox_MakePDF, 0, 2);
            this.tableLayoutPanel3.Controls.Add(this.checkBox_IsDXFReadThickness, 1, 0);
            this.tableLayoutPanel3.Controls.Add(this.checkBox_UseDocumentDbResult, 2, 2);
            this.tableLayoutPanel3.Controls.Add(this.checkBox_IsDXFBendLines, 1, 1);
            this.tableLayoutPanel3.Controls.Add(this.checkBox_BreakDimensionLines, 2, 0);
            this.tableLayoutPanel3.Name = "tableLayoutPanel3";
            // 
            // checkBox_MakeSpreadMap
            // 
            resources.ApplyResources(this.checkBox_MakeSpreadMap, "checkBox_MakeSpreadMap");
            this.checkBox_MakeSpreadMap.Name = "checkBox_MakeSpreadMap";
            this.checkBox_MakeSpreadMap.UseVisualStyleBackColor = true;
            // 
            // checkBox_MakeProjectMap
            // 
            resources.ApplyResources(this.checkBox_MakeProjectMap, "checkBox_MakeProjectMap");
            this.checkBox_MakeProjectMap.Name = "checkBox_MakeProjectMap";
            this.checkBox_MakeProjectMap.UseVisualStyleBackColor = true;
            // 
            // checkBox_MakeDWG
            // 
            resources.ApplyResources(this.checkBox_MakeDWG, "checkBox_MakeDWG");
            this.checkBox_MakeDWG.Name = "checkBox_MakeDWG";
            this.checkBox_MakeDWG.UseVisualStyleBackColor = true;
            // 
            // checkBox_MakePDF
            // 
            resources.ApplyResources(this.checkBox_MakePDF, "checkBox_MakePDF");
            this.checkBox_MakePDF.Name = "checkBox_MakePDF";
            this.checkBox_MakePDF.UseVisualStyleBackColor = true;
            // 
            // checkBox_IsDXFReadThickness
            // 
            resources.ApplyResources(this.checkBox_IsDXFReadThickness, "checkBox_IsDXFReadThickness");
            this.checkBox_IsDXFReadThickness.Name = "checkBox_IsDXFReadThickness";
            this.checkBox_IsDXFReadThickness.UseVisualStyleBackColor = true;
            // 
            // checkBox_UseDocumentDbResult
            // 
            resources.ApplyResources(this.checkBox_UseDocumentDbResult, "checkBox_UseDocumentDbResult");
            this.checkBox_UseDocumentDbResult.Name = "checkBox_UseDocumentDbResult";
            this.checkBox_UseDocumentDbResult.UseVisualStyleBackColor = true;
            // 
            // checkBox_IsDXFBendLines
            // 
            resources.ApplyResources(this.checkBox_IsDXFBendLines, "checkBox_IsDXFBendLines");
            this.checkBox_IsDXFBendLines.Name = "checkBox_IsDXFBendLines";
            this.checkBox_IsDXFBendLines.UseVisualStyleBackColor = true;
            // 
            // checkBox_BreakDimensionLines
            // 
            resources.ApplyResources(this.checkBox_BreakDimensionLines, "checkBox_BreakDimensionLines");
            this.checkBox_BreakDimensionLines.Name = "checkBox_BreakDimensionLines";
            this.checkBox_BreakDimensionLines.UseVisualStyleBackColor = true;
            // 
            // tableLayoutPanel1
            // 
            resources.ApplyResources(this.tableLayoutPanel1, "tableLayoutPanel1");
            this.tableLayoutPanel1.Controls.Add(this.tableLayoutPanel4, 1, 1);
            this.tableLayoutPanel1.Controls.Add(this.tableLayoutPanel2, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.groupBox_Language, 1, 8);
            this.tableLayoutPanel1.Controls.Add(this.label14, 0, 8);
            this.tableLayoutPanel1.Controls.Add(this.groupBox_TaskType, 1, 7);
            this.tableLayoutPanel1.Controls.Add(this.label11, 0, 7);
            this.tableLayoutPanel1.Controls.Add(this.label2, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.textBox_OutputFileType, 1, 4);
            this.tableLayoutPanel1.Controls.Add(this.textBox_EgiUrl, 1, 3);
            this.tableLayoutPanel1.Controls.Add(this.textBox_EGIPsw, 1, 6);
            this.tableLayoutPanel1.Controls.Add(this.label13, 0, 6);
            this.tableLayoutPanel1.Controls.Add(this.EgiUrl, 0, 3);
            this.tableLayoutPanel1.Controls.Add(this.textBox_EGIUserNo, 1, 5);
            this.tableLayoutPanel1.Controls.Add(this.label1, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.label12, 0, 5);
            this.tableLayoutPanel1.Controls.Add(this.textBox_SendbackUrl, 1, 2);
            this.tableLayoutPanel1.Controls.Add(this.SendbackUrl, 0, 2);
            this.tableLayoutPanel1.Controls.Add(this.label10, 0, 4);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            // 
            // tableLayoutPanel4
            // 
            resources.ApplyResources(this.tableLayoutPanel4, "tableLayoutPanel4");
            this.tableLayoutPanel4.Controls.Add(this.textBox_WorkPath, 0, 0);
            this.tableLayoutPanel4.Controls.Add(this.button4, 1, 0);
            this.tableLayoutPanel4.Name = "tableLayoutPanel4";
            // 
            // textBox_WorkPath
            // 
            resources.ApplyResources(this.textBox_WorkPath, "textBox_WorkPath");
            this.textBox_WorkPath.Name = "textBox_WorkPath";
            this.textBox_WorkPath.UseWaitCursor = true;
            // 
            // button4
            // 
            resources.ApplyResources(this.button4, "button4");
            this.button4.Name = "button4";
            this.button4.UseVisualStyleBackColor = true;
            this.button4.Click += new System.EventHandler(this.button4_Click);
            // 
            // tableLayoutPanel2
            // 
            resources.ApplyResources(this.tableLayoutPanel2, "tableLayoutPanel2");
            this.tableLayoutPanel2.Controls.Add(this.textBox_PdsPath, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.button3, 1, 0);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            // 
            // textBox_PdsPath
            // 
            resources.ApplyResources(this.textBox_PdsPath, "textBox_PdsPath");
            this.textBox_PdsPath.Name = "textBox_PdsPath";
            this.textBox_PdsPath.UseWaitCursor = true;
            // 
            // button3
            // 
            resources.ApplyResources(this.button3, "button3");
            this.button3.Name = "button3";
            this.button3.UseVisualStyleBackColor = true;
            this.button3.Click += new System.EventHandler(this.button3_Click);
            // 
            // label14
            // 
            resources.ApplyResources(this.label14, "label14");
            this.label14.Name = "label14";
            // 
            // groupBox_TaskType
            // 
            this.groupBox_TaskType.Controls.Add(this.radioButton2);
            this.groupBox_TaskType.Controls.Add(this.radioButton1);
            resources.ApplyResources(this.groupBox_TaskType, "groupBox_TaskType");
            this.groupBox_TaskType.Name = "groupBox_TaskType";
            this.groupBox_TaskType.TabStop = false;
            // 
            // radioButton2
            // 
            resources.ApplyResources(this.radioButton2, "radioButton2");
            this.radioButton2.Name = "radioButton2";
            this.radioButton2.TabStop = true;
            this.radioButton2.UseVisualStyleBackColor = true;
            // 
            // radioButton1
            // 
            resources.ApplyResources(this.radioButton1, "radioButton1");
            this.radioButton1.Checked = true;
            this.radioButton1.Name = "radioButton1";
            this.radioButton1.TabStop = true;
            this.radioButton1.UseVisualStyleBackColor = true;
            // 
            // label11
            // 
            resources.ApplyResources(this.label11, "label11");
            this.label11.Name = "label11";
            // 
            // label2
            // 
            resources.ApplyResources(this.label2, "label2");
            this.label2.Name = "label2";
            // 
            // textBox_EGIPsw
            // 
            resources.ApplyResources(this.textBox_EGIPsw, "textBox_EGIPsw");
            this.textBox_EGIPsw.Name = "textBox_EGIPsw";
            // 
            // label13
            // 
            resources.ApplyResources(this.label13, "label13");
            this.label13.Name = "label13";
            // 
            // textBox_EGIUserNo
            // 
            resources.ApplyResources(this.textBox_EGIUserNo, "textBox_EGIUserNo");
            this.textBox_EGIUserNo.Name = "textBox_EGIUserNo";
            // 
            // label1
            // 
            resources.ApplyResources(this.label1, "label1");
            this.label1.Name = "label1";
            // 
            // label12
            // 
            resources.ApplyResources(this.label12, "label12");
            this.label12.Name = "label12";
            // 
            // label10
            // 
            resources.ApplyResources(this.label10, "label10");
            this.label10.Name = "label10";
            // 
            // button2
            // 
            resources.ApplyResources(this.button2, "button2");
            this.button2.Name = "button2";
            this.button2.UseVisualStyleBackColor = true;
            this.button2.Click += new System.EventHandler(this.button2_Click);
            // 
            // button1
            // 
            resources.ApplyResources(this.button1, "button1");
            this.button1.Name = "button1";
            this.button1.UseVisualStyleBackColor = true;
            this.button1.Click += new System.EventHandler(this.buttonSave_Click);
            // 
            // FormParameterSet
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            resources.ApplyResources(this, "$this");
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.button2);
            this.Controls.Add(this.button1);
            this.Name = "FormParameterSet";
            this.ShowIcon = false;
            this.Load += new System.EventHandler(this.FormParameterSet_Load);
            this.groupBox_Language.ResumeLayout(false);
            this.groupBox_Language.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.tableLayoutPanel3.ResumeLayout(false);
            this.tableLayoutPanel3.PerformLayout();
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            this.tableLayoutPanel4.ResumeLayout(false);
            this.tableLayoutPanel4.PerformLayout();
            this.tableLayoutPanel2.ResumeLayout(false);
            this.tableLayoutPanel2.PerformLayout();
            this.groupBox_TaskType.ResumeLayout(false);
            this.groupBox_TaskType.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.FolderBrowserDialog folderBrowserDialog1;
        private System.Windows.Forms.TextBox textBox_OutputFileType;
        private System.Windows.Forms.TextBox textBox_EgiUrl;
        private System.Windows.Forms.Label EgiUrl;
        private System.Windows.Forms.TextBox textBox_SendbackUrl;
        private System.Windows.Forms.Label SendbackUrl;
        private System.Windows.Forms.GroupBox groupBox_Language;
        private System.Windows.Forms.RadioButton radioButton5;
        private System.Windows.Forms.RadioButton radioButton6;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.CheckBox checkBox_UseDocumentDbResult;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox textBox_EGIPsw;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.TextBox textBox_EGIUserNo;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.GroupBox groupBox_TaskType;
        private System.Windows.Forms.RadioButton radioButton2;
        private System.Windows.Forms.RadioButton radioButton1;
        private System.Windows.Forms.CheckBox checkBox_MakePDF;
        private System.Windows.Forms.CheckBox checkBox_MakeDWG;
        private System.Windows.Forms.CheckBox checkBox_IsDXFReadThickness;
        private System.Windows.Forms.CheckBox checkBox_MakeSpreadMap;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Button button4;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button button3;
        private System.Windows.Forms.TextBox textBox_PdsPath;
        private System.Windows.Forms.Button button2;
        private System.Windows.Forms.FolderBrowserDialog folderBrowserDialog2;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.RadioButton radioButton3;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel3;
        private System.Windows.Forms.CheckBox checkBox_IsDXFBendLines;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel4;
        private System.Windows.Forms.TextBox textBox_WorkPath;
        private System.Windows.Forms.CheckBox checkBox_MakeProjectMap;
        private System.Windows.Forms.CheckBox checkBox_BreakDimensionLines;
    }
}
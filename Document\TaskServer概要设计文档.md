# CADTaskServer 概要设计文档

## 1. 系统概述

### 1.1 项目背景
CADTaskServer是一个基于.NET Framework 4.0开发的CAD任务处理服务器组件，主要用于处理CAD相关的任务调度、文件管理、模型处理等功能。该系统作为一个子模块集成在更大的应用系统中，提供配置文件管理、任务获取与执行、文件缓存、日志记录、成果物上传、模型部件属性修改和任务状态回写等核心功能。

### 1.2 系统目标
- 提供稳定可靠的CAD任务处理服务
- 支持多种CAD文件格式的处理
- 实现高效的文件缓存机制
- 提供完整的日志记录和错误处理
- 支持多租户和多语言环境
- 提供灵活的配置管理机制

### 1.3 技术架构
- **开发语言**: C#
- **框架版本**: .NET Framework 4.0
- **架构模式**: 基于WCF的服务架构
- **UI框架**: Windows Forms
- **主要依赖**: 
  - Zxtech.EGI.PlatformService.Contract (平台服务契约)
  - Zxtech.EDS.ExcelAddIn.Core (Excel插件核心)
  - ICSharpCode.SharpZipLib (压缩解压)

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    CADTaskServer 系统                        │
├─────────────────────────────────────────────────────────────┤
│  表示层 (Presentation Layer)                                │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ FormCADTaskServer│ │ FormParameterSet │ │ FormTaskInfoSelect│ │
│  │                 │ │                 │ │                 │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  业务逻辑层 (Business Logic Layer)                           │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  CADTaskServer  │ │   WCFService    │ │  工具类模块      │ │
│  │   (核心业务)     │ │   (服务接口)     │ │ (ZipHelper等)   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  数据访问层 (Data Access Layer)                              │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ DBServiceClient │ │ RestClientPoxy  │ │ 配置文件管理     │ │
│  │                 │ │                 │ │                 │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件说明

#### 2.2.1 CADTaskServer (核心业务类)
- **职责**: 系统的核心业务逻辑处理
- **主要功能**:
  - 任务调度和管理
  - 文件缓存处理
  - 模型文件下载和处理
  - 任务状态管理
  - 事件处理和委托管理

#### 2.2.2 FormCADTaskServer (主窗体)
- **职责**: 用户界面和用户交互
- **主要功能**:
  - 配置信息读取和管理
  - 任务信息显示
  - 日志信息展示
  - 用户操作响应

#### 2.2.3 WCFService (服务接口)
- **职责**: 对外提供WCF服务接口
- **主要功能**:
  - CAD代码执行
  - 属性信息获取和设置
  - 模型信息查询
  - 服务状态检测

## 3. 功能模块设计

### 3.1 配置文件读取模块
**实现类**: FormCADTaskServer.GetServiceConfigInfo()
**配置文件**:
- CADTaskServerConfig.xml (服务器配置)
- CADTaskClientConfig.xml (客户端配置)

**主要配置项**:
- WCF服务配置 (EndpointUrl, MaxArrayLength等)
- 路径配置 (PdsPath, WorkPath, CachePath等)
- 功能开关 (MakeDRW, MakeDXF, UseModelCache等)
- 外部服务配置 (EGIService, LoginUrl等)

### 3.2 任务获取模块
**实现类**: CADTaskServer.GetTaskCadCodeList()
**主要流程**:
1. 通过WCF连接获取任务信息
2. 验证任务执行权限和次数
3. 解析任务命令列表
4. 设置任务运行状态

### 3.3 文件缓存模块
**实现类**: CADTaskServer.GetCacheFiles()
**缓存机制**:
- 基于DocumentId的文件缓存
- 支持ZIP压缩存储
- 缓存命中时跳过重复处理
- 缓存文件的MD5校验

### 3.4 日志记录模块
**实现类**: FormCADTaskServer.SendLogMessage()
**日志功能**:
- 分级日志记录 (信息、警告、错误)
- 日志文件自动分割
- 日志上传到服务器
- 实时日志显示

### 3.5 成果物上传模块
**实现类**: FormCADTaskServer.UploadResult()
**上传流程**:
1. 结果文件打包压缩
2. 上传到平台服务
3. 更新任务附件路径
4. 保存任务状态信息

### 3.6 模型部件属性修改模块
**实现类**: CADTaskServer.UpdateTaskPartPropWithModel()
**功能说明**:
- 根据模型更新部件属性
- 支持批量属性修改
- 属性值验证和转换

### 3.7 任务状态回写模块
**实现类**: FormCADTaskServer.SaveCadLogFile()
**状态管理**:
- 任务执行状态更新
- 失败次数统计
- 执行日志保存
- 任务完成通知

## 4. 接口设计

### 4.1 WCF服务接口 (ICADWCFService)
```csharp
public interface ICADWCFService
{
    CADTaskCode RunCADCode(CADTaskCode cadTask);
    bool TestServer();
    void SetCustomPropertyInfo(string filePath, string configName, Dictionary<string, string> propertyInfo);
    Dictionary<string, string> GetPropertys(string filePath, string refConfig = null);
    List<string> GetRefconfigList(string filePath, string configName);
    List<string> GetFeatureList(string filePath, string configName);
    List<string> GetDimlist(string filePath, string configName);
    List<string> GetChildPart(string filePath, string configName);
    Dictionary<string, Dictionary<string, string>> GetReferenceList(string filePath, string configName);
    void CloseModel(string filePath);
}
```

### 4.2 外部服务依赖接口

#### 4.2.1 平台服务接口 (IPlatformService)
- **用途**: 与EGI平台服务通信
- **主要方法**:
  - GetModelInfo(): 获取模型信息
  - UploadFile(): 文件上传
  - GetTaskCadCodeList(): 获取任务命令列表
  - SaveEpdTaskInfoEntities(): 保存任务信息

#### 4.2.2 文档服务接口
- **用途**: 文档和缓存管理
- **主要方法**:
  - findDocumentFileList(): 查找文档文件
  - saveDocumentList(): 保存文档列表

## 5. 数据结构设计

### 5.1 核心数据结构

#### 5.1.1 CADTaskCode (任务代码)
```csharp
public class CADTaskCode
{
    public Guid TaskId { get; set; }
    public int SortId { get; set; }
    public int PartId { get; set; }
    public int CadCodeId { get; set; }
    public string Para1-Para20 { get; set; }  // 参数1-20
    public Guid? EpdPartGuid { get; set; }
    public Guid? DocumentId { get; set; }
    public string ModelMd5 { get; set; }
    public bool? UseCache { get; set; }
    public FinishStatus Finished { get; set; }
}
```

#### 5.1.2 CADWorkStationInfo (工作站信息)
```csharp
public class CADWorkStationInfo
{
    public bool IsLoad { get; set; }
    public string PdsPath { get; set; }
    public string WorkPath { get; set; }
    public string CachePath { get; set; }
    public string BakPath { get; set; }
    // ... 其他配置属性
}
```

### 5.2 配置数据结构

#### 5.2.1 WCFServiceConfigInfo (服务配置信息)
包含所有从配置文件读取的配置项，如路径配置、功能开关、外部服务地址等。

## 6. 部署架构

### 6.1 部署组件
- **主程序**: Zxtech.CADTaskServer.dll
- **配置文件**: CADTaskServerConfig.xml, CADTaskClientConfig.xml
- **依赖组件**: 
  - Reference目录下的第三方DLL
  - 资源文件 (Resources目录)
  - 多语言资源文件

### 6.2 运行环境要求
- **.NET Framework**: 4.0或更高版本
- **操作系统**: Windows (支持x64)
- **网络**: 需要访问EGI平台服务
- **权限**: 需要文件系统读写权限

## 7. 安全性设计

### 7.1 身份认证
- 基于用户名密码的登录认证
- 支持多租户隔离
- JWT令牌管理

### 7.2 数据安全
- 配置文件敏感信息处理
- 文件传输加密
- 日志信息脱敏

## 8. 性能设计

### 8.1 缓存策略
- 模型文件缓存机制
- 配置信息内存缓存
- 任务结果缓存

### 8.2 并发处理
- 基于定时器的任务调度
- 异步文件操作
- 线程安全的状态管理

## 9. 错误处理和监控

### 9.1 异常处理
- 分层异常处理机制
- 异常信息记录和上报
- 自动重试机制

### 9.2 监控机制
- 任务执行状态监控
- 系统资源使用监控
- 服务连接状态监控

## 10. 扩展性设计

### 10.1 插件机制
- 支持外部CAD插件集成
- 可配置的处理流程
- 事件驱动的扩展点

### 10.2 配置扩展
- 灵活的配置文件结构
- 运行时配置更新
- 多环境配置支持

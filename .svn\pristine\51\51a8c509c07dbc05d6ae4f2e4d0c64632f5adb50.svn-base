<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="toolStripButton1.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 22</value>
  </data>
  <data name="&gt;&gt;toolStripUserName.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripButtonGetTaskSelect.Name" xml:space="preserve">
    <value>toolStripButtonGetTaskSelect</value>
  </data>
  <data name="toolStripStatusLabelTaskType.Text" xml:space="preserve">
    <value>任务类型:</value>
  </data>
  <data name="&gt;&gt;statusStrip1.Name" xml:space="preserve">
    <value>statusStrip1</value>
  </data>
  <data name="&gt;&gt;toolStripWorkStation.Name" xml:space="preserve">
    <value>toolStripWorkStation</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLabel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator4.Name" xml:space="preserve">
    <value>toolStripSeparator4</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator1.Name" xml:space="preserve">
    <value>toolStripSeparator1</value>
  </data>
  <data name="statusStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 407</value>
  </data>
  <data name="&gt;&gt;toolStripServerConnect.Name" xml:space="preserve">
    <value>toolStripServerConnect</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator3.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripWorkStation.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="&gt;&gt;labelInfo.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;toolStripButtonGetTask.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>623, 25</value>
  </data>
  <data name="toolStripServerConnect.Text" xml:space="preserve">
    <value>已连接</value>
  </data>
  <data name="btnStop.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 22</value>
  </data>
  <data name="&gt;&gt;btnExit.Name" xml:space="preserve">
    <value>btnExit</value>
  </data>
  <data name="&gt;&gt;toolStripButtonPriority.Name" xml:space="preserve">
    <value>toolStripButtonPriority</value>
  </data>
  <data name="&gt;&gt;toolStripWorkStation.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnStart.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="&gt;&gt;labelInfo.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="labelInfo.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 35</value>
  </data>
  <data name="statusStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>623, 22</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="&gt;&gt;rtbRunInfo.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator4.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStrip1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="toolStripStatusLabelUserName.Text" xml:space="preserve">
    <value>用户:</value>
  </data>
  <data name="&gt;&gt;toolStripTaskType.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripButton1.Text" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLabelUserName.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripButtonPriority.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        wgAADsIBFShKgAAAAgNJREFUOE+lkutLk2EYxvePhJaHShFBUTEPiCieQOmDiEZgrEbpYGiCQxkqA80I
        RUkUEZ0YRMS+DDI70MkTmzJnDc8iU8QT2mAEItnle90wHwTDyBduXnie5/rd133Q4ZKfAJqHFs9E08A8
        zD1emDpmYXjqRlnTFIrMk8iuGkNaxWekGEaQrHecBfw+/vPXODw6hj9whN2fh/Bt/8LSRgA3cxr/DeBZ
        9kvmTNMoHGNbIrYN+3Aj23IxgJkpds0doP3VstgmsNexhutZ9QrAms8T0zYzU5xXM46kcjsmvfvoeL2i
        AeoUgA07T8yaaZuZKab1rzN74jgys1YB2O2gmBZzH49LODXrrJlnzEzxe9cOLH1ziMioVgCOiuIFX0CE
        Q+/WYf+yieiCFrRp9tkwxnP7Kt5MbKGm6wfC000KwDlTXNLgEvHMkh+Db32o7fYivtQm1l9+3IBOp5P7
        yjYPwlKNCsAluWudxgvtknb5t9oWcL/VLU2Mvd2JFq3uqs7vAtE/ceNayiMF4KiYMSjmY2O7B4Ua+NaD
        YQGEJumRbvyG1IcfBHI12aAAXM8R57aIWTNrLLY4tfNPiCvpR1SeVeu6WWzHFD6THSDwFMDdpmUG62NJ
        PKM4Or9ZxBxb8H8l7g5CEu8pAOfM3WanSeeS8CFHxW4zM2umbWamOCShXAH+/wNOANABIDUxWnDPAAAA
        AElFTkSuQmCC
</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="toolStripUserName.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>541, 23</value>
  </data>
  <data name="&gt;&gt;rtbRunInfo.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="rtbRunInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>614, 343</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLableWorkStation.Name" xml:space="preserve">
    <value>toolStripStatusLableWorkStation</value>
  </data>
  <data name="toolStripTaskType.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 17</value>
  </data>
  <data name="&gt;&gt;toolStripButtonGetTask.Name" xml:space="preserve">
    <value>toolStripButtonGetTask</value>
  </data>
  <data name="&gt;&gt;rtbRunInfo.Name" xml:space="preserve">
    <value>rtbRunInfo</value>
  </data>
  <data name="&gt;&gt;toolStripButtonGetTaskSelect.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelInfo.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripButtonPriority.Text" xml:space="preserve">
    <value>优先运算</value>
  </data>
  <data name="&gt;&gt;btnStop.Name" xml:space="preserve">
    <value>btnStop</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripUserName.Text" xml:space="preserve">
    <value>fdsadfsa</value>
  </data>
  <data name="btnStart.Text" xml:space="preserve">
    <value>启动</value>
  </data>
  <data name="toolStripButtonGetTaskSelect.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="&gt;&gt;toolStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripButtonPriority.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 22</value>
  </data>
  <data name="&gt;&gt;labelInfo.Name" xml:space="preserve">
    <value>labelInfo</value>
  </data>
  <data name="toolStripButtonPriority.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="toolStripTaskType.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleLeft</value>
  </data>
  <data name="toolStripButtonGetTask.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 22</value>
  </data>
  <data name="toolStripSeparator2.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 25</value>
  </data>
  <data name="btnStop.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="toolStripServerConnect.Size" type="System.Drawing.Size, System.Drawing">
    <value>44, 17</value>
  </data>
  <data name="rtbRunInfo.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 61</value>
  </data>
  <data name="rtbRunInfo.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLableWorkStation.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnStop.Text" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 12</value>
  </data>
  <data name="toolStripStatusLabel1.Text" xml:space="preserve">
    <value>服务连接状态:</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripStatusLableWorkStation.Text" xml:space="preserve">
    <value>  工作站:</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLabelUserName.Name" xml:space="preserve">
    <value>toolStripStatusLabelUserName</value>
  </data>
  <data name="toolStripStatusLabelTaskType.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 17</value>
  </data>
  <data name="toolStripStatusLabelUserName.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>FormCADTaskServer</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLabelTaskType.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripStatusLabelUserName.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 17</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator3.Name" xml:space="preserve">
    <value>toolStripSeparator3</value>
  </data>
  <data name="toolStripSeparator1.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 25</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>CAD任务服务</value>
  </data>
  <data name="&gt;&gt;btnExit.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripButton1.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="btnExit.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="btnStart.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 22</value>
  </data>
  <data name="&gt;&gt;btnStop.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;statusStrip1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="btnExit.Text" xml:space="preserve">
    <value>  退出</value>
  </data>
  <data name="toolStripButtonGetTaskSelect.Text" xml:space="preserve">
    <value>手动取任务(可选)</value>
  </data>
  <data name="&gt;&gt;toolStrip1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelInfo.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="toolStripWorkStation.Text" xml:space="preserve">
    <value>toolStripStatusLabel2</value>
  </data>
  <data name="&gt;&gt;toolStripButtonPriority.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;statusStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.StatusStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripUserName.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 17</value>
  </data>
  <data name="btnStart.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="toolStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="btnExit.ToolTipText" xml:space="preserve">
    <value>退出CAD任务服务</value>
  </data>
  <data name="&gt;&gt;statusStrip1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;toolStripButton1.Name" xml:space="preserve">
    <value>toolStripButton1</value>
  </data>
  <data name="toolStripWorkStation.Size" type="System.Drawing.Size, System.Drawing">
    <value>183, 17</value>
  </data>
  <data name="toolStripSeparator3.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 25</value>
  </data>
  <data name="toolStripButtonGetTaskSelect.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 22</value>
  </data>
  <data name="labelInfo.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="&gt;&gt;toolStripServerConnect.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripStatusLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rtbRunInfo.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="toolStripButtonPriority.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;toolStripUserName.Name" xml:space="preserve">
    <value>toolStripUserName</value>
  </data>
  <data name="toolStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="toolStripButtonGetTask.Text" xml:space="preserve">
    <value> 手动取任务</value>
  </data>
  <data name="&gt;&gt;btnStart.Name" xml:space="preserve">
    <value>btnStart</value>
  </data>
  <data name="statusStrip1.Text" xml:space="preserve">
    <value>statusStrip1</value>
  </data>
  <data name="&gt;&gt;toolStripTaskType.Name" xml:space="preserve">
    <value>toolStripTaskType</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>623, 429</value>
  </data>
  <data name="btnExit.Size" type="System.Drawing.Size, System.Drawing">
    <value>44, 22</value>
  </data>
  <data name="toolStripTaskType.Text" xml:space="preserve">
    <value>toolStabel1</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnStart.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnStop.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="toolStripSeparator4.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 25</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator2.Name" xml:space="preserve">
    <value>toolStripSeparator2</value>
  </data>
  <data name="&gt;&gt;rtbRunInfo.Type" xml:space="preserve">
    <value>System.Windows.Forms.RichTextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rtbRunInfo.Text" xml:space="preserve">
    <value />
  </data>
  <data name="toolStripStatusLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>83, 17</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLabelTaskType.Name" xml:space="preserve">
    <value>toolStripStatusLabelTaskType</value>
  </data>
  <data name="statusStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="toolStripStatusLableWorkStation.Size" type="System.Drawing.Size, System.Drawing">
    <value>55, 17</value>
  </data>
  <data name="toolStripButtonGetTask.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Magenta</value>
  </data>
  <data name="&gt;&gt;toolStrip1.Name" xml:space="preserve">
    <value>toolStrip1</value>
  </data>
  <data name="toolStrip1.Text" xml:space="preserve">
    <value>toolStrip1</value>
  </data>
  <data name="&gt;&gt;toolStripButton1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripStatusLabel1.Name" xml:space="preserve">
    <value>toolStripStatusLabel1</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="toolStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>0, 0</value>
  </metadata>
  <metadata name="statusStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>0, 407</value>
  </metadata>
</root>
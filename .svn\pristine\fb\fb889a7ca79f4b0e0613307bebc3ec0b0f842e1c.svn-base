﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="folderBrowserDialog1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>737, 17</value>
  </metadata>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="textBox_OutputFileType.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="textBox_OutputFileType.Location" type="System.Drawing.Point, System.Drawing">
    <value>203, 147</value>
  </data>
  <data name="textBox_OutputFileType.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 7, 3, 3</value>
  </data>
  <data name="textBox_OutputFileType.Size" type="System.Drawing.Size, System.Drawing">
    <value>377, 21</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="textBox_OutputFileType.TabIndex" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="&gt;&gt;textBox_OutputFileType.Name" xml:space="preserve">
    <value>textBox_OutputFileType</value>
  </data>
  <data name="&gt;&gt;textBox_OutputFileType.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox_OutputFileType.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;textBox_OutputFileType.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="textBox_EgiUrl.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="textBox_EgiUrl.Location" type="System.Drawing.Point, System.Drawing">
    <value>203, 112</value>
  </data>
  <data name="textBox_EgiUrl.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 7, 3, 3</value>
  </data>
  <data name="textBox_EgiUrl.Size" type="System.Drawing.Size, System.Drawing">
    <value>377, 21</value>
  </data>
  <data name="textBox_EgiUrl.TabIndex" type="System.Int32, mscorlib">
    <value>73</value>
  </data>
  <data name="&gt;&gt;textBox_EgiUrl.Name" xml:space="preserve">
    <value>textBox_EgiUrl</value>
  </data>
  <data name="&gt;&gt;textBox_EgiUrl.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox_EgiUrl.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;textBox_EgiUrl.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="EgiUrl.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="EgiUrl.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="EgiUrl.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="EgiUrl.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 105</value>
  </data>
  <data name="EgiUrl.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 35</value>
  </data>
  <data name="EgiUrl.TabIndex" type="System.Int32, mscorlib">
    <value>72</value>
  </data>
  <data name="EgiUrl.Text" xml:space="preserve">
    <value>系统地址</value>
  </data>
  <data name="EgiUrl.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;EgiUrl.Name" xml:space="preserve">
    <value>EgiUrl</value>
  </data>
  <data name="&gt;&gt;EgiUrl.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;EgiUrl.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;EgiUrl.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="textBox_SendbackUrl.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="textBox_SendbackUrl.Location" type="System.Drawing.Point, System.Drawing">
    <value>203, 77</value>
  </data>
  <data name="textBox_SendbackUrl.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 7, 3, 3</value>
  </data>
  <data name="textBox_SendbackUrl.Size" type="System.Drawing.Size, System.Drawing">
    <value>377, 21</value>
  </data>
  <data name="textBox_SendbackUrl.TabIndex" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="&gt;&gt;textBox_SendbackUrl.Name" xml:space="preserve">
    <value>textBox_SendbackUrl</value>
  </data>
  <data name="&gt;&gt;textBox_SendbackUrl.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox_SendbackUrl.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;textBox_SendbackUrl.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="SendbackUrl.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="SendbackUrl.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="SendbackUrl.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="SendbackUrl.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 70</value>
  </data>
  <data name="SendbackUrl.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 35</value>
  </data>
  <data name="SendbackUrl.TabIndex" type="System.Int32, mscorlib">
    <value>70</value>
  </data>
  <data name="SendbackUrl.Text" xml:space="preserve">
    <value>回调地址</value>
  </data>
  <data name="SendbackUrl.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;SendbackUrl.Name" xml:space="preserve">
    <value>SendbackUrl</value>
  </data>
  <data name="&gt;&gt;SendbackUrl.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;SendbackUrl.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;SendbackUrl.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="radioButton3.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton3.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 10</value>
  </data>
  <data name="radioButton3.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 0, 3, 3</value>
  </data>
  <data name="radioButton3.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 16</value>
  </data>
  <data name="radioButton3.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="radioButton3.Text" xml:space="preserve">
    <value>zh-CN</value>
  </data>
  <data name="radioButton3.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>TopCenter</value>
  </data>
  <data name="&gt;&gt;radioButton3.Name" xml:space="preserve">
    <value>radioButton3</value>
  </data>
  <data name="&gt;&gt;radioButton3.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton3.Parent" xml:space="preserve">
    <value>groupBox_Language</value>
  </data>
  <data name="&gt;&gt;radioButton3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="radioButton5.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton5.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton5.Location" type="System.Drawing.Point, System.Drawing">
    <value>95, 10</value>
  </data>
  <data name="radioButton5.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 16</value>
  </data>
  <data name="radioButton5.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="radioButton5.Text" xml:space="preserve">
    <value>en-US</value>
  </data>
  <data name="&gt;&gt;radioButton5.Name" xml:space="preserve">
    <value>radioButton5</value>
  </data>
  <data name="&gt;&gt;radioButton5.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton5.Parent" xml:space="preserve">
    <value>groupBox_Language</value>
  </data>
  <data name="&gt;&gt;radioButton5.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="radioButton6.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton6.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton6.Location" type="System.Drawing.Point, System.Drawing">
    <value>180, 10</value>
  </data>
  <data name="radioButton6.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 16</value>
  </data>
  <data name="radioButton6.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="radioButton6.Text" xml:space="preserve">
    <value>ja-JP</value>
  </data>
  <data name="&gt;&gt;radioButton6.Name" xml:space="preserve">
    <value>radioButton6</value>
  </data>
  <data name="&gt;&gt;radioButton6.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton6.Parent" xml:space="preserve">
    <value>groupBox_Language</value>
  </data>
  <data name="&gt;&gt;radioButton6.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="groupBox_Language.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="groupBox_Language.Location" type="System.Drawing.Point, System.Drawing">
    <value>203, 280</value>
  </data>
  <data name="groupBox_Language.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 0, 3, 3</value>
  </data>
  <data name="groupBox_Language.Size" type="System.Drawing.Size, System.Drawing">
    <value>377, 32</value>
  </data>
  <data name="groupBox_Language.TabIndex" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="&gt;&gt;groupBox_Language.Name" xml:space="preserve">
    <value>groupBox_Language</value>
  </data>
  <data name="&gt;&gt;groupBox_Language.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox_Language.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;groupBox_Language.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="groupBox1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="groupBox1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="tableLayoutPanel3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="tableLayoutPanel3.ColumnCount" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="checkBox_MakeSpreadMap.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="checkBox_MakeSpreadMap.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_MakeSpreadMap.CheckAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="checkBox_MakeSpreadMap.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_MakeSpreadMap.Location" type="System.Drawing.Point, System.Drawing">
    <value>79, 3</value>
  </data>
  <data name="checkBox_MakeSpreadMap.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 22</value>
  </data>
  <data name="checkBox_MakeSpreadMap.TabIndex" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="checkBox_MakeSpreadMap.Text" xml:space="preserve">
    <value>是否需要输出DXF文件</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeSpreadMap.Name" xml:space="preserve">
    <value>checkBox_MakeSpreadMap</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeSpreadMap.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeSpreadMap.Parent" xml:space="preserve">
    <value>tableLayoutPanel3</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeSpreadMap.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="checkBox_MakeProjectMap.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="checkBox_MakeProjectMap.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_MakeProjectMap.CheckAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="checkBox_MakeProjectMap.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_MakeProjectMap.Location" type="System.Drawing.Point, System.Drawing">
    <value>472, 31</value>
  </data>
  <data name="checkBox_MakeProjectMap.Size" type="System.Drawing.Size, System.Drawing">
    <value>108, 22</value>
  </data>
  <data name="checkBox_MakeProjectMap.TabIndex" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="checkBox_MakeProjectMap.Text" xml:space="preserve">
    <value>是否需要块分解</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeProjectMap.Name" xml:space="preserve">
    <value>checkBox_MakeProjectMap</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeProjectMap.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeProjectMap.Parent" xml:space="preserve">
    <value>tableLayoutPanel3</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeProjectMap.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="checkBox_MakeDWG.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="checkBox_MakeDWG.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_MakeDWG.CheckAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="checkBox_MakeDWG.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_MakeDWG.Location" type="System.Drawing.Point, System.Drawing">
    <value>79, 31</value>
  </data>
  <data name="checkBox_MakeDWG.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 22</value>
  </data>
  <data name="checkBox_MakeDWG.TabIndex" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="checkBox_MakeDWG.Text" xml:space="preserve">
    <value>是否需要输出DWG文件</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeDWG.Name" xml:space="preserve">
    <value>checkBox_MakeDWG</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeDWG.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeDWG.Parent" xml:space="preserve">
    <value>tableLayoutPanel3</value>
  </data>
  <data name="&gt;&gt;checkBox_MakeDWG.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="checkBox_MakePDF.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="checkBox_MakePDF.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_MakePDF.CheckAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="checkBox_MakePDF.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_MakePDF.Location" type="System.Drawing.Point, System.Drawing">
    <value>79, 59</value>
  </data>
  <data name="checkBox_MakePDF.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 24</value>
  </data>
  <data name="checkBox_MakePDF.TabIndex" type="System.Int32, mscorlib">
    <value>69</value>
  </data>
  <data name="checkBox_MakePDF.Text" xml:space="preserve">
    <value>是否需要输出PDF文件</value>
  </data>
  <data name="checkBox_MakePDF.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;checkBox_MakePDF.Name" xml:space="preserve">
    <value>checkBox_MakePDF</value>
  </data>
  <data name="&gt;&gt;checkBox_MakePDF.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_MakePDF.Parent" xml:space="preserve">
    <value>tableLayoutPanel3</value>
  </data>
  <data name="&gt;&gt;checkBox_MakePDF.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.CheckAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.ImageAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.Location" type="System.Drawing.Point, System.Drawing">
    <value>242, 3</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 22</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.TabIndex" type="System.Int32, mscorlib">
    <value>67</value>
  </data>
  <data name="checkBox_IsDXFReadThickness.Text" xml:space="preserve">
    <value>钣金件是否需要输出厚度</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFReadThickness.Name" xml:space="preserve">
    <value>checkBox_IsDXFReadThickness</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFReadThickness.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFReadThickness.Parent" xml:space="preserve">
    <value>tableLayoutPanel3</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFReadThickness.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.CheckAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.Location" type="System.Drawing.Point, System.Drawing">
    <value>448, 59</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.Size" type="System.Drawing.Size, System.Drawing">
    <value>132, 24</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.TabIndex" type="System.Int32, mscorlib">
    <value>62</value>
  </data>
  <data name="checkBox_UseDocumentDbResult.Text" xml:space="preserve">
    <value>结果是否上传数据库</value>
  </data>
  <data name="&gt;&gt;checkBox_UseDocumentDbResult.Name" xml:space="preserve">
    <value>checkBox_UseDocumentDbResult</value>
  </data>
  <data name="&gt;&gt;checkBox_UseDocumentDbResult.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_UseDocumentDbResult.Parent" xml:space="preserve">
    <value>tableLayoutPanel3</value>
  </data>
  <data name="&gt;&gt;checkBox_UseDocumentDbResult.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="checkBox_IsDXFBendLines.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="checkBox_IsDXFBendLines.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_IsDXFBendLines.CheckAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="checkBox_IsDXFBendLines.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="checkBox_IsDXFBendLines.Location" type="System.Drawing.Point, System.Drawing">
    <value>254, 31</value>
  </data>
  <data name="checkBox_IsDXFBendLines.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="checkBox_IsDXFBendLines.TabIndex" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="checkBox_IsDXFBendLines.Text" xml:space="preserve">
    <value>展开图是否显示折弯线</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFBendLines.Name" xml:space="preserve">
    <value>checkBox_IsDXFBendLines</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFBendLines.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFBendLines.Parent" xml:space="preserve">
    <value>tableLayoutPanel3</value>
  </data>
  <data name="&gt;&gt;checkBox_IsDXFBendLines.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="checkBox_BreakDimensionLines.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="checkBox_BreakDimensionLines.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="checkBox_BreakDimensionLines.CheckAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="checkBox_BreakDimensionLines.Location" type="System.Drawing.Point, System.Drawing">
    <value>472, 3</value>
  </data>
  <data name="checkBox_BreakDimensionLines.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="checkBox_BreakDimensionLines.Size" type="System.Drawing.Size, System.Drawing">
    <value>108, 22</value>
  </data>
  <data name="checkBox_BreakDimensionLines.TabIndex" type="System.Int32, mscorlib">
    <value>70</value>
  </data>
  <data name="checkBox_BreakDimensionLines.Text" xml:space="preserve">
    <value>是否打断尺寸线</value>
  </data>
  <data name="checkBox_BreakDimensionLines.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;checkBox_BreakDimensionLines.Name" xml:space="preserve">
    <value>checkBox_BreakDimensionLines</value>
  </data>
  <data name="&gt;&gt;checkBox_BreakDimensionLines.Type" xml:space="preserve">
    <value>System.Windows.Forms.CheckBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;checkBox_BreakDimensionLines.Parent" xml:space="preserve">
    <value>tableLayoutPanel3</value>
  </data>
  <data name="&gt;&gt;checkBox_BreakDimensionLines.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="tableLayoutPanel3.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 352</value>
  </data>
  <data name="tableLayoutPanel3.RowCount" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="tableLayoutPanel3.Size" type="System.Drawing.Size, System.Drawing">
    <value>583, 86</value>
  </data>
  <data name="tableLayoutPanel3.TabIndex" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel3.Name" xml:space="preserve">
    <value>tableLayoutPanel3</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel3.Type" xml:space="preserve">
    <value>System.Windows.Forms.TableLayoutPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel3.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tableLayoutPanel3.LayoutSettings" type="System.Windows.Forms.TableLayoutSettings, System.Windows.Forms">
    <value>&lt;?xml version="1.0" encoding="utf-16"?&gt;&lt;TableLayoutSettings&gt;&lt;Controls&gt;&lt;Control Name="checkBox_MakeSpreadMap" Row="0" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="checkBox_MakeProjectMap" Row="1" RowSpan="1" Column="2" ColumnSpan="1" /&gt;&lt;Control Name="checkBox_MakeDWG" Row="1" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="checkBox_MakePDF" Row="2" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="checkBox_IsDXFReadThickness" Row="0" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="checkBox_UseDocumentDbResult" Row="2" RowSpan="1" Column="2" ColumnSpan="1" /&gt;&lt;Control Name="checkBox_IsDXFBendLines" Row="1" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="checkBox_BreakDimensionLines" Row="0" RowSpan="1" Column="2" ColumnSpan="1" /&gt;&lt;/Controls&gt;&lt;Columns Styles="Absolute,220,Percent,50,Percent,50" /&gt;&lt;Rows Styles="Percent,33.33333,Percent,33.33333,Percent,33.33333,Absolute,20" /&gt;&lt;/TableLayoutSettings&gt;</value>
  </data>
  <data name="tableLayoutPanel1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="tableLayoutPanel1.AutoSizeMode" type="System.Windows.Forms.AutoSizeMode, System.Windows.Forms">
    <value>GrowAndShrink</value>
  </data>
  <data name="tableLayoutPanel1.ColumnCount" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="tableLayoutPanel4.ColumnCount" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="textBox_WorkPath.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="textBox_WorkPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="textBox_WorkPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>326, 21</value>
  </data>
  <data name="textBox_WorkPath.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="&gt;&gt;textBox_WorkPath.Name" xml:space="preserve">
    <value>textBox_WorkPath</value>
  </data>
  <data name="&gt;&gt;textBox_WorkPath.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox_WorkPath.Parent" xml:space="preserve">
    <value>tableLayoutPanel4</value>
  </data>
  <data name="&gt;&gt;textBox_WorkPath.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="button4.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="button4.Location" type="System.Drawing.Point, System.Drawing">
    <value>335, 3</value>
  </data>
  <data name="button4.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>40, 23</value>
  </data>
  <data name="button4.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>40, 23</value>
  </data>
  <data name="button4.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 23</value>
  </data>
  <data name="button4.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="button4.Text" xml:space="preserve">
    <value>...</value>
  </data>
  <data name="&gt;&gt;button4.Name" xml:space="preserve">
    <value>button4</value>
  </data>
  <data name="&gt;&gt;button4.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button4.Parent" xml:space="preserve">
    <value>tableLayoutPanel4</value>
  </data>
  <data name="&gt;&gt;button4.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tableLayoutPanel4.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="tableLayoutPanel4.Location" type="System.Drawing.Point, System.Drawing">
    <value>203, 38</value>
  </data>
  <data name="tableLayoutPanel4.RowCount" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="tableLayoutPanel4.Size" type="System.Drawing.Size, System.Drawing">
    <value>377, 29</value>
  </data>
  <data name="tableLayoutPanel4.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel4.Name" xml:space="preserve">
    <value>tableLayoutPanel4</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel4.Type" xml:space="preserve">
    <value>System.Windows.Forms.TableLayoutPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel4.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel4.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="tableLayoutPanel4.LayoutSettings" type="System.Windows.Forms.TableLayoutSettings, System.Windows.Forms">
    <value>&lt;?xml version="1.0" encoding="utf-16"?&gt;&lt;TableLayoutSettings&gt;&lt;Controls&gt;&lt;Control Name="textBox_WorkPath" Row="0" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="button4" Row="0" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;/Controls&gt;&lt;Columns Styles="Percent,100,Absolute,45" /&gt;&lt;Rows Styles="Percent,100" /&gt;&lt;/TableLayoutSettings&gt;</value>
  </data>
  <data name="tableLayoutPanel2.ColumnCount" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="textBox_PdsPath.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="textBox_PdsPath.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="textBox_PdsPath.Size" type="System.Drawing.Size, System.Drawing">
    <value>326, 21</value>
  </data>
  <data name="textBox_PdsPath.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;textBox_PdsPath.Name" xml:space="preserve">
    <value>textBox_PdsPath</value>
  </data>
  <data name="&gt;&gt;textBox_PdsPath.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox_PdsPath.Parent" xml:space="preserve">
    <value>tableLayoutPanel2</value>
  </data>
  <data name="&gt;&gt;textBox_PdsPath.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="button3.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="button3.Location" type="System.Drawing.Point, System.Drawing">
    <value>335, 3</value>
  </data>
  <data name="button3.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>40, 23</value>
  </data>
  <data name="button3.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>40, 23</value>
  </data>
  <data name="button3.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 23</value>
  </data>
  <data name="button3.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="button3.Text" xml:space="preserve">
    <value>...</value>
  </data>
  <data name="&gt;&gt;button3.Name" xml:space="preserve">
    <value>button3</value>
  </data>
  <data name="&gt;&gt;button3.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button3.Parent" xml:space="preserve">
    <value>tableLayoutPanel2</value>
  </data>
  <data name="&gt;&gt;button3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tableLayoutPanel2.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="tableLayoutPanel2.Location" type="System.Drawing.Point, System.Drawing">
    <value>203, 3</value>
  </data>
  <data name="tableLayoutPanel2.RowCount" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="tableLayoutPanel2.Size" type="System.Drawing.Size, System.Drawing">
    <value>377, 29</value>
  </data>
  <data name="tableLayoutPanel2.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel2.Name" xml:space="preserve">
    <value>tableLayoutPanel2</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel2.Type" xml:space="preserve">
    <value>System.Windows.Forms.TableLayoutPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel2.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tableLayoutPanel2.LayoutSettings" type="System.Windows.Forms.TableLayoutSettings, System.Windows.Forms">
    <value>&lt;?xml version="1.0" encoding="utf-16"?&gt;&lt;TableLayoutSettings&gt;&lt;Controls&gt;&lt;Control Name="textBox_PdsPath" Row="0" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="button3" Row="0" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;/Controls&gt;&lt;Columns Styles="Percent,100,Absolute,45" /&gt;&lt;Rows Styles="Percent,100" /&gt;&lt;/TableLayoutSettings&gt;</value>
  </data>
  <data name="label14.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label14.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="label14.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label14.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 280</value>
  </data>
  <data name="label14.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 35</value>
  </data>
  <data name="label14.TabIndex" type="System.Int32, mscorlib">
    <value>60</value>
  </data>
  <data name="label14.Text" xml:space="preserve">
    <value>语言</value>
  </data>
  <data name="label14.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label14.Name" xml:space="preserve">
    <value>label14</value>
  </data>
  <data name="&gt;&gt;label14.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label14.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;label14.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="radioButton2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton2.Location" type="System.Drawing.Point, System.Drawing">
    <value>95, 10</value>
  </data>
  <data name="radioButton2.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 16</value>
  </data>
  <data name="radioButton2.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="radioButton2.Text" xml:space="preserve">
    <value>测试</value>
  </data>
  <data name="&gt;&gt;radioButton2.Name" xml:space="preserve">
    <value>radioButton2</value>
  </data>
  <data name="&gt;&gt;radioButton2.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton2.Parent" xml:space="preserve">
    <value>groupBox_TaskType</value>
  </data>
  <data name="&gt;&gt;radioButton2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="radioButton1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="radioButton1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="radioButton1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 10</value>
  </data>
  <data name="radioButton1.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 16</value>
  </data>
  <data name="radioButton1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="radioButton1.Text" xml:space="preserve">
    <value>正式</value>
  </data>
  <data name="&gt;&gt;radioButton1.Name" xml:space="preserve">
    <value>radioButton1</value>
  </data>
  <data name="&gt;&gt;radioButton1.Type" xml:space="preserve">
    <value>System.Windows.Forms.RadioButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;radioButton1.Parent" xml:space="preserve">
    <value>groupBox_TaskType</value>
  </data>
  <data name="&gt;&gt;radioButton1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="groupBox_TaskType.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="groupBox_TaskType.Location" type="System.Drawing.Point, System.Drawing">
    <value>203, 246</value>
  </data>
  <data name="groupBox_TaskType.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 1, 0, 0</value>
  </data>
  <data name="groupBox_TaskType.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="groupBox_TaskType.Size" type="System.Drawing.Size, System.Drawing">
    <value>380, 34</value>
  </data>
  <data name="groupBox_TaskType.TabIndex" type="System.Int32, mscorlib">
    <value>54</value>
  </data>
  <data name="&gt;&gt;groupBox_TaskType.Name" xml:space="preserve">
    <value>groupBox_TaskType</value>
  </data>
  <data name="&gt;&gt;groupBox_TaskType.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox_TaskType.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;groupBox_TaskType.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="label11.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label11.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="label11.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label11.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 245</value>
  </data>
  <data name="label11.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 35</value>
  </data>
  <data name="label11.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="label11.Text" xml:space="preserve">
    <value>任务类型</value>
  </data>
  <data name="label11.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label11.Name" xml:space="preserve">
    <value>label11</value>
  </data>
  <data name="&gt;&gt;label11.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label11.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;label11.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="label2.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label2.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="label2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label2.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 0</value>
  </data>
  <data name="label2.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 35</value>
  </data>
  <data name="label2.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="label2.Text" xml:space="preserve">
    <value>模板位置</value>
  </data>
  <data name="label2.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label2.Name" xml:space="preserve">
    <value>label2</value>
  </data>
  <data name="&gt;&gt;label2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label2.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;label2.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="textBox_EGIPsw.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="textBox_EGIPsw.Location" type="System.Drawing.Point, System.Drawing">
    <value>203, 217</value>
  </data>
  <data name="textBox_EGIPsw.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 7, 3, 3</value>
  </data>
  <data name="textBox_EGIPsw.Size" type="System.Drawing.Size, System.Drawing">
    <value>377, 21</value>
  </data>
  <data name="textBox_EGIPsw.TabIndex" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="&gt;&gt;textBox_EGIPsw.Name" xml:space="preserve">
    <value>textBox_EGIPsw</value>
  </data>
  <data name="&gt;&gt;textBox_EGIPsw.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox_EGIPsw.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;textBox_EGIPsw.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="label13.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label13.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="label13.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label13.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 210</value>
  </data>
  <data name="label13.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 35</value>
  </data>
  <data name="label13.TabIndex" type="System.Int32, mscorlib">
    <value>58</value>
  </data>
  <data name="label13.Text" xml:space="preserve">
    <value>登录密码</value>
  </data>
  <data name="label13.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label13.Name" xml:space="preserve">
    <value>label13</value>
  </data>
  <data name="&gt;&gt;label13.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label13.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;label13.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="textBox_EGIUserNo.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="textBox_EGIUserNo.Location" type="System.Drawing.Point, System.Drawing">
    <value>203, 182</value>
  </data>
  <data name="textBox_EGIUserNo.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 7, 3, 3</value>
  </data>
  <data name="textBox_EGIUserNo.Size" type="System.Drawing.Size, System.Drawing">
    <value>377, 21</value>
  </data>
  <data name="textBox_EGIUserNo.TabIndex" type="System.Int32, mscorlib">
    <value>57</value>
  </data>
  <data name="&gt;&gt;textBox_EGIUserNo.Name" xml:space="preserve">
    <value>textBox_EGIUserNo</value>
  </data>
  <data name="&gt;&gt;textBox_EGIUserNo.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBox_EGIUserNo.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;textBox_EGIUserNo.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="label1.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="label1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label1.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 35</value>
  </data>
  <data name="label1.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 35</value>
  </data>
  <data name="label1.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="label1.Text" xml:space="preserve">
    <value>输出模型位置</value>
  </data>
  <data name="label1.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label1.Name" xml:space="preserve">
    <value>label1</value>
  </data>
  <data name="&gt;&gt;label1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label1.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;label1.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="label12.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label12.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="label12.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label12.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 175</value>
  </data>
  <data name="label12.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 35</value>
  </data>
  <data name="label12.TabIndex" type="System.Int32, mscorlib">
    <value>56</value>
  </data>
  <data name="label12.Text" xml:space="preserve">
    <value>登录用户名</value>
  </data>
  <data name="label12.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label12.Name" xml:space="preserve">
    <value>label12</value>
  </data>
  <data name="&gt;&gt;label12.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label12.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;label12.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="label10.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="label10.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="label10.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="label10.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 140</value>
  </data>
  <data name="label10.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 35</value>
  </data>
  <data name="label10.TabIndex" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="label10.Text" xml:space="preserve">
    <value>输出文件类型</value>
  </data>
  <data name="label10.TextAlign" type="System.Drawing.ContentAlignment, System.Drawing">
    <value>MiddleRight</value>
  </data>
  <data name="&gt;&gt;label10.Name" xml:space="preserve">
    <value>label10</value>
  </data>
  <data name="&gt;&gt;label10.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;label10.Parent" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;label10.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="tableLayoutPanel1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="tableLayoutPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 17</value>
  </data>
  <data name="tableLayoutPanel1.RowCount" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="tableLayoutPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>583, 335</value>
  </data>
  <data name="tableLayoutPanel1.TabIndex" type="System.Int32, mscorlib">
    <value>75</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel1.Name" xml:space="preserve">
    <value>tableLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.TableLayoutPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel1.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;tableLayoutPanel1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="tableLayoutPanel1.LayoutSettings" type="System.Windows.Forms.TableLayoutSettings, System.Windows.Forms">
    <value>&lt;?xml version="1.0" encoding="utf-16"?&gt;&lt;TableLayoutSettings&gt;&lt;Controls&gt;&lt;Control Name="tableLayoutPanel4" Row="1" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="tableLayoutPanel2" Row="0" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="groupBox_Language" Row="8" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="label14" Row="8" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="groupBox_TaskType" Row="7" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="label11" Row="7" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="label2" Row="0" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="textBox_OutputFileType" Row="4" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="textBox_EgiUrl" Row="3" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="textBox_EGIPsw" Row="6" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="label13" Row="6" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="EgiUrl" Row="3" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="textBox_EGIUserNo" Row="5" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="label1" Row="1" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="label12" Row="5" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="textBox_SendbackUrl" Row="2" RowSpan="1" Column="1" ColumnSpan="1" /&gt;&lt;Control Name="SendbackUrl" Row="2" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;Control Name="label10" Row="4" RowSpan="1" Column="0" ColumnSpan="1" /&gt;&lt;/Controls&gt;&lt;Columns Styles="Absolute,200,Percent,100" /&gt;&lt;Rows Styles="Absolute,35,Absolute,35,Absolute,35,Absolute,35,Absolute,35,Absolute,35,Absolute,35,Absolute,35,Absolute,35,Absolute,20" /&gt;&lt;/TableLayoutSettings&gt;</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 8</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>589, 443</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>配置</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="button2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="button2.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="button2.Location" type="System.Drawing.Point, System.Drawing">
    <value>529, 457</value>
  </data>
  <data name="button2.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 22</value>
  </data>
  <data name="button2.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="button2.Text" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="&gt;&gt;button2.Name" xml:space="preserve">
    <value>button2</value>
  </data>
  <data name="&gt;&gt;button2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="folderBrowserDialog2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>917, 17</value>
  </metadata>
  <data name="button1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="button1.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="button1.Location" type="System.Drawing.Point, System.Drawing">
    <value>436, 457</value>
  </data>
  <data name="button1.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 22</value>
  </data>
  <data name="button1.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="button1.Text" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="&gt;&gt;button1.Name" xml:space="preserve">
    <value>button1</value>
  </data>
  <data name="&gt;&gt;button1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;button1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;button1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>25</value>
  </metadata>
  <data name="$this.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>613, 491</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>工作站配置</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog1.Name" xml:space="preserve">
    <value>folderBrowserDialog1</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog1.Type" xml:space="preserve">
    <value>System.Windows.Forms.FolderBrowserDialog, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog2.Name" xml:space="preserve">
    <value>folderBrowserDialog2</value>
  </data>
  <data name="&gt;&gt;folderBrowserDialog2.Type" xml:space="preserve">
    <value>System.Windows.Forms.FolderBrowserDialog, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>FormParameterSet</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>
﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{AEEB3226-313F-44C6-BDF6-535D42D62363}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Zxtech.CADTaskServer</RootNamespace>
    <AssemblyName>Zxtech.CADTaskServer</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <SignAssembly>false</SignAssembly>
    <AssemblyOriginatorKeyFile>CADTask.snk</AssemblyOriginatorKeyFile>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation />
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>D:\CADworkstationSW_v1.0.0\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <PlatformTarget>x64</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisLogFile>..\Release\Zxtech.CADTaskServer.dll.CodeAnalysisLog.xml</CodeAnalysisLogFile>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRuleSetDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\\Rule Sets</CodeAnalysisRuleSetDirectories>
    <CodeAnalysisRuleDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\FxCop\\Rules</CodeAnalysisRuleDirectories>
    <CodeAnalysisIgnoreBuiltInRules>true</CodeAnalysisIgnoreBuiltInRules>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisLogFile>..\Release\Zxtech.CADTaskServer.dll.CodeAnalysisLog.xml</CodeAnalysisLogFile>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRuleSetDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\\Rule Sets</CodeAnalysisRuleSetDirectories>
    <CodeAnalysisRuleDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\FxCop\\Rules</CodeAnalysisRuleDirectories>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>Resources\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Serialization">
      <RequiredTargetFramework>3.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.ServiceModel">
      <RequiredTargetFramework>3.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CADTaskServer.cs" />
    <Compile Include="FormCADTaskServer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormCADTaskServer.Designer.cs">
      <DependentUpon>FormCADTaskServer.cs</DependentUpon>
    </Compile>
    <Compile Include="FormParameterSet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormParameterSet.designer.cs">
      <DependentUpon>FormParameterSet.cs</DependentUpon>
    </Compile>
    <Compile Include="FormTaskInfoPriority.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormTaskInfoPriority.Designer.cs">
      <DependentUpon>FormTaskInfoPriority.cs</DependentUpon>
    </Compile>
    <Compile Include="FormTaskInfoSelect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormTaskInfoSelect.Designer.cs">
      <DependentUpon>FormTaskInfoSelect.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Login.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Login.Designer.cs">
      <DependentUpon>Frm_Login.cs</DependentUpon>
    </Compile>
    <Compile Include="ShareMemoryHelper.cs" />
    <Compile Include="SingleQuoteStringParser.cs" />
    <Compile Include="WCFService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="ZipHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="FormCADTaskServer.en-US.resx">
      <DependentUpon>FormCADTaskServer.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FormCADTaskServer.ja-JP.resx">
      <DependentUpon>FormCADTaskServer.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FormCADTaskServer.resx">
      <DependentUpon>FormCADTaskServer.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FormParameterSet.en-US.resx">
      <DependentUpon>FormParameterSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormParameterSet.ja-JP.resx">
      <DependentUpon>FormParameterSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormParameterSet.resx">
      <DependentUpon>FormParameterSet.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FormTaskInfoPriority.ja-JP.resx">
      <DependentUpon>FormTaskInfoPriority.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormTaskInfoPriority.resx">
      <DependentUpon>FormTaskInfoPriority.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormTaskInfoSelect.en-US.resx">
      <DependentUpon>FormTaskInfoSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormTaskInfoSelect.ja-JP.resx">
      <DependentUpon>FormTaskInfoSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormTaskInfoSelect.resx">
      <DependentUpon>FormTaskInfoSelect.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Login.en-US.resx">
      <DependentUpon>Frm_Login.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Login.ja-JP.resx">
      <DependentUpon>Frm_Login.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Login.resx">
      <DependentUpon>Frm_Login.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.jp-JA.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.en-US.resx">
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="CADTask.snk" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\branches\EGIVersion\EPDCodeSubVersion\Zxtech.EDS.ExcelAddIn.Core\Zxtech.EDS.ExcelAddIn.Core.csproj">
      <Project>{a9983621-62e2-4b20-9476-dbc5067123c8}</Project>
      <Name>Zxtech.EDS.ExcelAddIn.Core</Name>
    </ProjectReference>
    <ProjectReference Include="..\CADTaskServer.Contract\CADTaskServer.Contract.csproj">
      <Project>{D3E2FE3E-7FC3-496F-B6C8-8DB042657EC2}</Project>
      <Name>CADTaskServer.Contract</Name>
    </ProjectReference>
    <ProjectReference Include="..\Neuxa.EDS.ExcelAddIn.LanguagePackage\LanguagePackage.csproj">
      <Project>{8C3761FA-F0E9-4671-9327-F02D7105EF06}</Project>
      <Name>LanguagePackage</Name>
    </ProjectReference>
    <ProjectReference Include="..\PdsConstant\PdsConstant.csproj">
      <Project>{4D7C99D1-729C-403E-8330-CCF434221CFD}</Project>
      <Name>PdsConstant</Name>
    </ProjectReference>
    <ProjectReference Include="..\Zxtech.EGI.PlatformService.Contract\Zxtech.EGI.PlatformService.Contract.csproj">
      <Project>{f0ca42e2-4993-4406-bfa6-f0886f7fddf9}</Project>
      <Name>Zxtech.EGI.PlatformService.Contract</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\InsertRow.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AutoQuitBtn.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AutoQuitBtn1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AutoRunBtn.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AutoRunBtn1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AutoRunBtn2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\GetTaskBtn.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\GetTaskSelectBtn.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\QuitBtn.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\QuitBtn1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\SetupBtn.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\SetupBtn1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\SetupBtn2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\SetupBtn3.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
# CADTaskServer 功能测试用例

## 1. 测试用例概述

### 1.1 文档说明
本文档包含CADTaskServer系统7个核心功能模块的详细测试用例，涵盖正常流程、异常流程和边界条件测试。

### 1.2 测试用例编号规则
- **TC_CONFIG_XXX**: 配置文件读取模块测试用例
- **TC_TASK_XXX**: 任务获取模块测试用例  
- **TC_CACHE_XXX**: 文件缓存模块测试用例
- **TC_LOG_XXX**: 日志记录模块测试用例
- **TC_UPLOAD_XXX**: 成果物上传模块测试用例
- **TC_PROPERTY_XXX**: 模型部件属性修改模块测试用例
- **TC_STATUS_XXX**: 任务状态回写模块测试用例

### 1.3 测试用例状态
- **设计完成**: 测试用例已编写完成
- **执行中**: 测试用例正在执行
- **通过**: 测试用例执行通过
- **失败**: 测试用例执行失败
- **阻塞**: 测试用例因环境问题无法执行

## 2. 配置文件读取模块测试用例

### TC_CONFIG_001: 正常配置文件读取
**测试目标**: 验证系统能正确读取标准格式的配置文件
**前置条件**: 
- 配置文件CADTaskServerConfig.xml存在且格式正确
- 配置文件CADTaskClientConfig.xml存在且格式正确

**测试步骤**:
1. 启动CADTaskServer应用程序
2. 系统自动读取配置文件
3. 检查ServiceConfigInfo对象是否正确初始化

**预期结果**:
- 配置文件读取成功
- 所有配置项正确加载到ServiceConfigInfo对象
- 系统正常启动，无错误日志

**测试数据**:
```xml
<Configuration>
    <PdsPath>D:\PDS\Templates</PdsPath>
    <WorkPath>D:\CAD\Work</WorkPath>
    <UseModelCache>true</UseModelCache>
    <EGIService>http://platform.example.com/api</EGIService>
</Configuration>
```

### TC_CONFIG_002: 配置文件不存在
**测试目标**: 验证配置文件不存在时的错误处理
**前置条件**: 删除或重命名配置文件

**测试步骤**:
1. 删除CADTaskServerConfig.xml文件
2. 启动CADTaskServer应用程序
3. 观察系统行为和错误信息

**预期结果**:
- 系统显示配置文件不存在的错误信息
- 应用程序优雅退出或使用默认配置
- 错误信息记录到日志文件

### TC_CONFIG_003: 配置文件格式错误
**测试目标**: 验证XML格式错误时的处理
**前置条件**: 准备格式错误的配置文件

**测试步骤**:
1. 修改配置文件，引入XML语法错误
2. 启动CADTaskServer应用程序
3. 检查错误处理机制

**预期结果**:
- 系统检测到XML格式错误
- 显示具体的错误信息
- 系统不会崩溃，能够优雅处理

**测试数据**:
```xml
<Configuration>
    <PdsPath>D:\PDS\Templates</PdsPath>
    <!-- 缺少结束标签 -->
    <WorkPath>D:\CAD\Work
</Configuration>
```

### TC_CONFIG_004: 配置项缺失处理
**测试目标**: 验证必要配置项缺失时的处理
**前置条件**: 准备缺少关键配置项的配置文件

**测试步骤**:
1. 删除配置文件中的关键配置项（如PdsPath）
2. 启动应用程序
3. 检查默认值处理机制

**预期结果**:
- 系统使用默认值或提示用户配置
- 记录配置项缺失的警告信息
- 系统能够继续运行

### TC_CONFIG_005: 配置值类型转换
**测试目标**: 验证配置值的类型转换功能
**前置条件**: 准备包含各种数据类型的配置文件

**测试步骤**:
1. 配置文件包含布尔值、整数、字符串等类型
2. 启动应用程序
3. 验证类型转换是否正确

**预期结果**:
- 布尔值正确转换（true/false）
- 整数值正确转换
- 字符串值正确处理
- 无效值使用默认值并记录警告

**测试数据**:
```xml
<Configuration>
    <UseModelCache>true</UseModelCache>
    <ErrRerunCount>4</ErrRerunCount>
    <MaxLogFileSize>5.5</MaxLogFileSize>
    <InvalidBool>invalid</InvalidBool>
</Configuration>
```

## 3. 任务获取模块测试用例

### TC_TASK_001: 正常任务获取流程
**测试目标**: 验证系统能正常获取和处理任务
**前置条件**: 
- WCF服务连接正常
- 数据库中存在待处理任务

**测试步骤**:
1. 启动CADTaskServer并建立WCF连接
2. 触发任务获取操作
3. 验证任务列表获取和解析

**预期结果**:
- 成功连接到WCF服务
- 获取到任务列表
- 任务信息正确解析为CADTaskCode对象
- 任务状态设置为运行中

### TC_TASK_002: 无任务时的处理
**测试目标**: 验证无可用任务时的系统行为
**前置条件**: 数据库中无待处理任务

**测试步骤**:
1. 清空数据库中的待处理任务
2. 执行任务获取操作
3. 观察系统行为

**预期结果**:
- 系统正确识别无任务状态
- 记录相应的日志信息
- 定时器继续运行，等待新任务
- 向消息队列写入[Done!]标记

### TC_TASK_003: 任务失败重试机制
**测试目标**: 验证任务执行失败后的重试机制
**前置条件**: 
- 存在执行失败的任务
- 失败次数未超过限制

**测试步骤**:
1. 模拟任务执行失败
2. 检查失败次数统计
3. 验证重试机制

**预期结果**:
- 系统正确统计失败次数
- 失败次数未超过限制时允许重试
- 超过限制时标记任务为失败状态
- 记录详细的失败日志

### TC_TASK_004: 任务优先级处理
**测试目标**: 验证任务按优先级排序处理
**前置条件**: 数据库中存在不同优先级的任务

**测试步骤**:
1. 创建不同优先级的测试任务
2. 执行任务获取操作
3. 验证任务处理顺序

**预期结果**:
- 任务按SortId字段排序
- 高优先级任务优先处理
- 任务顺序符合业务规则

### TC_TASK_005: WCF连接异常处理
**测试目标**: 验证WCF服务连接异常时的处理
**前置条件**: WCF服务不可用或网络异常

**测试步骤**:
1. 停止WCF服务或断开网络连接
2. 尝试获取任务
3. 检查异常处理机制

**预期结果**:
- 系统检测到连接异常
- 记录CommunicationException异常
- 设置连接状态为断开
- 向消息队列写入[Done!]标记

## 4. 文件缓存模块测试用例

### TC_CACHE_001: 缓存命中测试
**测试目标**: 验证文件缓存命中时的处理逻辑
**前置条件**: 
- 缓存功能已启用
- 目标文件已存在于缓存中

**测试步骤**:
1. 配置UseModelCache=true
2. 请求已缓存的模型文件
3. 验证缓存命中逻辑

**预期结果**:
- 系统从缓存中获取文件
- 设置UseCache=true标志
- 跳过文件下载过程
- 记录缓存命中日志

### TC_CACHE_002: 缓存未命中测试
**测试目标**: 验证缓存未命中时的处理逻辑
**前置条件**: 
- 缓存功能已启用
- 目标文件不在缓存中

**测试步骤**:
1. 请求未缓存的模型文件
2. 验证文件下载和缓存存储过程
3. 检查缓存文件创建

**预期结果**:
- 系统执行文件下载
- 下载完成后存储到缓存
- 生成正确的缓存文件
- 记录缓存存储日志

### TC_CACHE_003: 缓存文件完整性验证
**测试目标**: 验证缓存文件的MD5校验机制
**前置条件**: 缓存中存在文件

**测试步骤**:
1. 获取缓存文件
2. 计算文件MD5值
3. 与存储的MD5值比较

**预期结果**:
- MD5值计算正确
- 文件完整性验证通过
- 损坏文件被重新下载
- 记录完整性检查日志

### TC_CACHE_004: 缓存空间管理
**测试目标**: 验证缓存空间不足时的处理
**前置条件**: 缓存目录空间接近满

**测试步骤**:
1. 填满缓存目录空间
2. 尝试添加新的缓存文件
3. 验证空间管理机制

**预期结果**:
- 系统检测到空间不足
- 清理旧的缓存文件
- 成功存储新文件
- 记录空间管理日志

### TC_CACHE_005: 并发缓存访问
**测试目标**: 验证多线程并发访问缓存的安全性
**前置条件**: 多个任务同时访问相同缓存文件

**测试步骤**:
1. 启动多个并发任务
2. 同时访问相同的缓存文件
3. 验证线程安全性

**预期结果**:
- 并发访问不会导致文件损坏
- 线程安全机制正常工作
- 所有任务都能正确获取文件
- 无死锁或竞态条件

## 5. 日志记录模块测试用例

### TC_LOG_001: 不同级别日志记录
**测试目标**: 验证不同级别日志的正确记录
**前置条件**: 日志系统已初始化

**测试步骤**:
1. 记录信息级别日志（messageType=1）
2. 记录警告级别日志（messageType=2）
3. 记录普通日志（messageType=其他）
4. 检查日志格式和内容

**预期结果**:
- 不同级别日志格式正确
- 时间戳格式正确
- 日志内容完整准确
- 日志文件正确创建

**测试数据**:
```
期望格式:
2024-01-15 10:30:25 !    信息级别日志内容
2024-01-15 10:30:26 !!   警告级别日志内容  
2024-01-15 10:30:27 普通日志内容
```

### TC_LOG_002: 日志文件轮转
**测试目标**: 验证日志文件大小超限时的轮转机制
**前置条件**: 配置MaxLogFileSize参数

**测试步骤**:
1. 设置MaxLogFileSize=5MB
2. 生成大量日志直到超过限制
3. 验证文件轮转机制

**预期结果**:
- 日志文件大小超限时自动轮转
- 旧文件重命名为带时间戳的文件
- 新日志文件正常创建
- 文件轮转过程无日志丢失

### TC_LOG_003: 日志并发写入
**测试目标**: 验证多线程并发写入日志的安全性
**前置条件**: 多个线程同时写入日志

**测试步骤**:
1. 启动多个线程同时写入日志
2. 验证日志内容完整性
3. 检查是否有日志丢失或混乱

**预期结果**:
- 并发写入不会导致日志混乱
- 所有日志内容都被正确记录
- 文件锁机制正常工作
- 无死锁或性能问题

### TC_LOG_004: 日志上传功能
**测试目标**: 验证日志文件上传到服务器的功能
**前置条件**: 
- 网络连接正常
- 服务器可访问

**测试步骤**:
1. 生成测试日志文件
2. 执行日志上传操作
3. 验证上传结果

**预期结果**:
- 日志文件成功上传到服务器
- 返回正确的文件ID
- 上传过程记录到日志
- 上传失败时有重试机制

### TC_LOG_005: 日志格式验证
**测试目标**: 验证日志格式的标准化和一致性
**前置条件**: 系统正常运行

**测试步骤**:
1. 触发各种日志记录场景
2. 检查日志格式一致性
3. 验证特殊字符处理

**预期结果**:
- 日志格式符合预定义标准
- 时间格式统一
- 特殊字符正确转义
- 多语言内容正确显示

## 6. 成果物上传模块测试用例

### TC_UPLOAD_001: 正常文件上传
**测试目标**: 验证正常情况下的文件上传功能
**前置条件**: 
- 网络连接正常
- 有效的上传文件路径

**测试步骤**:
1. 准备测试文件和目录
2. 执行UploadResult方法
3. 验证上传过程和结果

**预期结果**:
- 文件成功打包为ZIP格式
- ZIP文件成功上传到服务器
- 返回正确的文件ID
- 任务附件路径正确更新

### TC_UPLOAD_002: 大文件上传处理
**测试目标**: 验证大文件上传的处理能力
**前置条件**: 准备大于100MB的测试文件

**测试步骤**:
1. 创建大文件测试数据
2. 执行上传操作
3. 监控上传进度和性能

**预期结果**:
- 大文件能够成功上传
- 上传过程不会超时
- 内存使用合理
- 上传进度正确显示

### TC_UPLOAD_003: 网络中断恢复
**测试目标**: 验证网络中断时的错误处理和恢复
**前置条件**: 可控制的网络环境

**测试步骤**:
1. 开始文件上传
2. 在上传过程中断开网络
3. 恢复网络连接
4. 验证错误处理机制

**预期结果**:
- 系统检测到网络中断
- 记录相应的错误日志
- 网络恢复后能够重试
- 或者优雅地处理失败情况

### TC_UPLOAD_004: 文件完整性验证
**测试目标**: 验证上传文件的完整性检查
**前置条件**: 上传功能正常

**测试步骤**:
1. 上传测试文件
2. 计算本地文件校验和
3. 验证服务器端文件完整性

**预期结果**:
- 上传前后文件大小一致
- 文件内容完整无损
- 校验和验证通过
- 损坏文件被检测并重传

### TC_UPLOAD_005: 并发上传处理
**测试目标**: 验证多个文件并发上传的处理
**前置条件**: 多个任务同时上传文件

**测试步骤**:
1. 启动多个并发上传任务
2. 监控系统资源使用
3. 验证上传结果

**预期结果**:
- 并发上传不会相互干扰
- 系统资源使用合理
- 所有文件都能成功上传
- 无死锁或资源竞争问题

## 7. 模型部件属性修改模块测试用例

### TC_PROPERTY_001: 属性读取功能
**测试目标**: 验证模型部件属性的读取功能
**前置条件**: 
- 模型文件存在
- WCF服务正常

**测试步骤**:
1. 打开测试模型文件
2. 调用GetPropertys方法
3. 验证返回的属性信息

**预期结果**:
- 成功读取模型属性
- 属性信息格式正确
- 属性值类型正确
- 无异常抛出

### TC_PROPERTY_002: 属性修改功能
**测试目标**: 验证模型部件属性的修改功能
**前置条件**: 
- 模型文件可写
- 有效的属性修改数据

**测试步骤**:
1. 读取原始属性值
2. 调用SetCustomPropertyInfo修改属性
3. 重新读取验证修改结果

**预期结果**:
- 属性值成功修改
- 修改后的值正确保存
- 其他属性不受影响
- 模型文件完整性保持

### TC_PROPERTY_003: 批量属性操作
**测试目标**: 验证批量修改多个属性的功能
**前置条件**: 准备批量属性修改数据

**测试步骤**:
1. 准备多个属性的修改数据
2. 执行批量属性修改
3. 验证所有属性修改结果

**预期结果**:
- 所有属性都被正确修改
- 批量操作具有事务性
- 部分失败时能够回滚
- 操作结果正确返回

### TC_PROPERTY_004: 属性值验证
**测试目标**: 验证属性值的类型和范围验证
**前置条件**: 准备各种类型的测试数据

**测试步骤**:
1. 尝试设置无效的属性值
2. 验证输入验证机制
3. 检查错误处理

**预期结果**:
- 无效值被正确拒绝
- 返回明确的错误信息
- 原始值保持不变
- 验证规则正确执行

### TC_PROPERTY_005: 并发属性修改
**测试目标**: 验证多线程并发修改属性的安全性
**前置条件**: 多个线程同时修改属性

**测试步骤**:
1. 启动多个线程修改同一模型属性
2. 验证并发控制机制
3. 检查最终结果一致性

**预期结果**:
- 并发修改不会导致数据损坏
- 最后的修改操作生效
- 无死锁或竞态条件
- 操作结果可预测

## 8. 任务状态回写模块测试用例

### TC_STATUS_001: 正常状态更新
**测试目标**: 验证任务状态的正常更新功能
**前置条件**: 
- 任务执行完成
- 数据库连接正常

**测试步骤**:
1. 执行完成一个测试任务
2. 调用SaveCadLogFile方法
3. 验证状态更新结果

**预期结果**:
- 任务状态正确更新为完成
- 执行时间正确记录
- 日志文件正确上传
- 数据库记录正确更新

### TC_STATUS_002: 状态回写失败处理
**测试目标**: 验证状态回写失败时的处理机制
**前置条件**: 数据库连接异常或服务不可用

**测试步骤**:
1. 模拟数据库连接失败
2. 尝试更新任务状态
3. 验证失败处理机制

**预期结果**:
- 系统检测到回写失败
- 记录详细的错误日志
- 实施重试机制
- 或者标记为待重试状态

### TC_STATUS_003: 状态一致性验证
**测试目标**: 验证任务状态在各个组件间的一致性
**前置条件**: 多个组件涉及状态管理

**测试步骤**:
1. 更新任务状态
2. 检查各个组件的状态显示
3. 验证状态一致性

**预期结果**:
- 所有组件显示相同状态
- 状态变更及时同步
- 无状态不一致情况
- 状态变更有审计记录

### TC_STATUS_004: 并发状态更新
**测试目标**: 验证多个任务并发更新状态的处理
**前置条件**: 多个任务同时完成

**测试步骤**:
1. 启动多个任务并发执行
2. 任务同时完成并更新状态
3. 验证并发处理结果

**预期结果**:
- 所有任务状态都正确更新
- 无状态更新丢失
- 数据库操作无冲突
- 性能表现良好

### TC_STATUS_005: 状态历史记录
**测试目标**: 验证任务状态变更的历史记录功能
**前置条件**: 任务经历多个状态变更

**测试步骤**:
1. 创建任务并经历多个状态
2. 查询状态变更历史
3. 验证历史记录完整性

**预期结果**:
- 所有状态变更都被记录
- 变更时间准确
- 变更原因明确
- 历史记录可查询

---

**文档版本**: v1.0
**编写日期**: 2024年1月
**编写人**: 测试团队
**审核人**: 测试经理

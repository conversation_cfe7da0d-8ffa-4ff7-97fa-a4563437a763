SCC = This is a Source Code Control file.

[AppConfig.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/AppConfig

[EdsConfigFileModify.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/EdsConfigFileModify

[PdsClientConfig.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsClientConfig

[PdsServerConfig.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsServerConfig

[Zxtech.PdsBomExportServiceConfig.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsBomExportServiceCofig

[AutoUpdateServer.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/AutoUpdateServer

[AutoUpdateClient.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/AutoUpdateClient

[AutoUpdateServer.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/AutoUpdateServer

[AutoUpdateService.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/AutoUpdateService

[AutoUpdateServiceHost.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/AutoUpdateServiceHost

[DatabaseTool.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool

[DBUpdateTwoJDT.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool

[Zxtech.SqlServerAccessTrans.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/Zxtech.SqlServerAccessTrans

[ScriptConverter.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/ScriptConverter

[CollectInfoPlugIn.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/CollectInfo

[CollectInfo.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/CollectInfo

[CommonDbLayer.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/CommonDbLayer

[DatabaseUpdate.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/DatabaseUpdate

[Zxtech.DatabaseUpdateItem.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/DatabaseUpdateItem

[Zxtech.DatabaseUpdateMain.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/DatabaseUpdateItem

[DbCompareLib.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/DbCompare

[DbCompareprogram.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/DbCompareprogram

[DBRestore.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/DBRestore

[DBUpdateTwo.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/DBUpdate

[Tools.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/tools

[ToolsHelp.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/ToolsHelp

[ExcelInfo.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/ToolsItemPlugIn

[ToolsItemPlugIn.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/ToolsItemPlugIn

[UpdateItemOne.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/UpdateItemOne

[Zxtech.UpdateItemPlugInOne.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/UpdateItemOne

[UpdateItemPlugInOne.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/UpdateItemOne

[WindowsFormsDbUpdate.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/WindowsFormsDbUpdate

[Zxtech.CsvFormat.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/Zxtech.CsvFormat

[Zxtech.GZHitachiPlugIn.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/Zxtech.GZHitachiPlugIn

[ZXTECH.JNDBTool.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/ZXTECH.JNDBTool

[Zxtech.JNScriptPlugIn.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/Zxtech.JNScriptPlugIn

[Zxtech.LsPlugIn.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/Zxtech.LsPlugIn

[Zxtech.ParentSonReport.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/Zxtech.ParentSonReport

[Zxtech.UpdateScriptPlugIn.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/DatabaseTool/Zxtech.UpdateScriptPlugIn

[EPDUI.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/EPDUI

[Ctl_ParaAll.vbproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/EPDUI/old/Ctl_ParaAll_old

[ECCSServiceHostForWin.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/EPDUI/ECCSServiceHostForWin

[ECCSWindowsService.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/EPDUI/ECCSWindowsService

[ECCSService.vbproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/EPDUI/EPDService

[Ctl_ParaAll.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/EPDUI/old/Ctl_ParaAll_old

[Zxtech.PdsParameterCollectConnect.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsParameterCollectConnect

[ParamsForm.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/EPDUI/ParamsForm

[WindowsApplication2.vbproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/EPDUI/WindowsApplication2

[Import Data.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/ImportDataRL/old/ImportData(tableScript)

[Zxtech.Import Data.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/ImportDataRL/old/ImportData(tableScript)/Import Data

[SetupImportData.vdproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/ImportDataRL/old/ImportData(tableScript)/SetupImportData

[PdsReprotingServices.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/PdsReprotingServices

[SetupEdis.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/SetupEdis

[SetupEdsCadWorkStation.vdproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/SetupEdis/SetupEdsCadWorkStation

[SetupPdsCadWorkstation.vdproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/SetupEdis/SetupPdsCadWorkstation

[SetupPdsClient.vdproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/SetupEdis/SetupPdsClient

[SetupPdsDataWorkstation.vdproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/SetupEdis/SetupPdsDataWorkstation

[SetupPdsServer.vdproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/SetupEdis/SetupPdsServer

[Zxtech.EdisClient.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient

[BomToExcelPlugIn.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/BomToExcelPlugIn

[ComplieAfx.vcproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/ComplieAfx

[ComplieAfx.dsp]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/ComplieAfx

[ComplieAfx_en.dsp]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/ComplieAfx_en

[ComplieAfxCsPackage.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/ComplieAfxCsPackage

[ConvertSingleQuoteString.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/ConvertSingleQuoteString

[EdisApplicationLib.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/EdisApplicationLib

[EdisIPrefabManager.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/EdisIPrefabManager

[EdisIMainFace.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/EdisMainFace

[EdisUtilityLib.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/EdisUtilityLib

[HendsIOTest.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/HendsIOTest

[PdsApplicationLib.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsApplicationLib

[PdsConstant.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsConstant

[PdsIChildTable.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsIChildTable

[PdsICommonPara.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsICommonPara

[PdsICustomFunc.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsICustomFunc

[PdsILoginSer.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsILoginSer

[PdsITNonstdConfigList.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsINonstdConfigList

[PdsIPropManage.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsIPropManage

[PdsIRunTestData.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsIRunTestData

[PdsITBomManage.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsITBomManage

[PdsITemplateFind.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsITemplateFind

[PdsITemplateRun.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsITemplateRun

[PdsITestData.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsITestData

[PdsITMainPara.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsITMainPara

[PdsITModifyLog.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsITModifyLog

[PdsITRefConfigList.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsITRefConfigList

[PdsITRefTemplateList.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsITRefTemplateList

[PdsITStandard.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsITStandard

[PdsIUserManager.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsIUserManager

[PdsLoginSer.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsLoginSer

[PdsIMainFace.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsMainFace

[PdsISystemOption.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsSystemOption

[PdsITBaseInfo.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsTemplateBaseInfo

[PdsTemplateLib.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsTemplateLib

[PdsUserManagerLib.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/PdsUserManager

[Zxtech.ScriptConverter.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/ScriptConverter

[TestProjectForPdsTemplate.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/TestProjectForPdsTemplate

[Zxtech.EdisPdsManager.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.EdisPdsManager

[Zxtech.EdisService.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.EdisService

[Zxtech.EdisService.Contract.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.EdisService.Contract

[Zxtech.EdisServiceHost.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.EdisServiceHost

[Zxtech.EdisWCFClient.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.EdisWCFClient

[Zxtech.EdisWCFServer.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.EdisWCFServer

[Zxtech.EdisWinHost.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.EdisWinHost

[Zxtech.EdisWinSvrMgr.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.EdisWinSvrMgr

[Zxtech.EdsPBomReProcess.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.EdsPBomReProcess

[Zxtech.NDS2PDSInterface.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.NDS2PDSInterface

[Zxtech.PdsBomExportService.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.PdsBomExportService

[Zxtech.PdsBomExportView.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsBomExportView

[Zxtech.PdsIDynamicTemplateType.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsDynamicTemplateType

[Zxtech.PdsExtractNumber.Client.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.PdsExtractNumber.Client

[Zxtech.PdsExtractNumber.Contract.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.PdsExtractNumber.Contract

[Zxtech.PdsExtractNumber.Service.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.PdsExtractNumber.Service

[Zxtech.PdsIGlobalTable.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsIGlobalTable

[Zxtech.PdsIPropertyValueListExpend.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsIPdsPropertyValueListExpend

[Zxtech.PdsITPropertyFind.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsITPropertyFind

[Zxtech.PdsITRunLog.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsITRunLog

[Zxtech.PdsParameterCollect.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsParameterCollect

[Zxtech.PdsPcPuiTest.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsPcPuiTest

[Zxtech.PdsScriptEditor.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsScriptEditor

[Zxtech.PdsTaskReport.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsTaskReport

[Zxtech.PdsUIScript.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisClient/Zxtech.PdsUIScript

[Zxtech.PdsWorkflow.Base.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.PdsWorkflow.Base

[Zxtech.PdsWorkflow.Host.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.PdsWorkflow.Host

[Zxtech.EdisParaCheck.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisParaCheck

[SetupEdisParaCheck.vdproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisParaCheck/SetupEdisParaCheck

[Zxtech.EdisLogInUserValidateContract.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisParaCheck/Zxtech.EdisLogInUserValidateContract

[Zxtech.EdisLogInUserValidateService.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisParaCheck/Zxtech.EdisLogInUserValidateService

[Zxtech.EdisParaCheckContract.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisParaCheck/Zxtech.EdisParaCheckContract

[Zxtech.EdisParaCheckService.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisParaCheck/Zxtech.EdisParaCheckService

[Zxtech.EdisParaCheckServiceHost.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisParaCheck/Zxtech.EdisParaCheckServiceHost

[Zxtech.EdisParaCheckUI.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisParaCheck/Zxtech.EdisParaCheckUI

[Zxtech.EdisParaCheckUserLogIn.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisParaCheck/Zxtech.EdisParaCheckUserLogIn

[Zxtech.EdisPcTestWin.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisParaCheck/Zxtech.EdisPcTestWin

[Zxtech.WinHost.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisParaCheck/Zxtech.WinHost

[Zxtech.EdisService.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService

[Zxtech.EbdNdsService.Client.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.EbdNdsService.Client

[Zxtech.EdbNdsService.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.EdbNdsService

[Zxtech.EbdNdsService.Contract.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.EdbService.Contract

[Zxtech.PdsContractService.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.PdsContractService

[Zxtech.PdsContractService.Contract.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.PdsContractService.Contract

[Zxtech.PdsContractServiceClient.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.EdisService/Zxtech.PdsContractServiceClient

[Zxtech.PdsCADTask.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask

[Cad2dAutocad.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad2dAutocad

[Cad2dAutocad.vcproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad2dAutocad/Cad2dAutocad

[Cad2dCommandExpand.vcproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad2dAutocad/Cad2dCommandExpand

[Dimensiontidy.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad2dAutocad/Dimensiontidy

[netlib.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad2dAutocad/netlib

[Cad3dProe.dsp]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad3dProe

[Cad3dProeTest.dsp]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad3dProe/Cad3dProeTest

[Cad3dProe_en.dsp]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad3dProe_en

[Cad3dProeProe4.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad3dProeProe4

[Cad3dProe.vcproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad3dProeVc8/Cad3dProeVc8

[Cad3dProe_en.vcproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad3dProeProe4/Cad3dProe_en

[Cad3dProeTest.vcproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad3dProeVc8

[CadWorkStation.vcproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad3dProeVc8/CadWorkStationVc8

[CadWorkStation_en.vcproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad3dProeProe4/CadWorkStation_en

[Setup1.vdproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad3dProeProe4/Setup1

[Cad3dProeVc8.sln]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/Cad3dProeVc8

[CADTaskServer.csproj]
SCC_Aux_Path = ***********:8877:EIMS_EDIM_1.0V
SCC_Project_Name = $/EDIM/Zxtech.PdsCADTask/CADTaskServer


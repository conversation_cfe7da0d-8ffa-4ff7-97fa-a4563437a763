﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Zxtech.EGI.PlatformService.Contract.Dto;

namespace Zxtech.CADTaskServer
{
    public partial class FormTaskInfoPriority : Form
    {
        List<EpdTaskInfoDto> taskInfos;
        List<EpdTaskInfoDto> UpdateList = new List<EpdTaskInfoDto>();
        string orderby = "Pri descending,CreateTime";
        FormCADTaskServer FrmCADTaskServer { get; set; }
        string WhereString{get;set;}
        public FormTaskInfoPriority(FormCADTaskServer formCADTaskServer)
        {
            this.FrmCADTaskServer = formCADTaskServer;
            
            InitializeComponent();
        }

        private void buttonOk_Click(object sender, EventArgs e)
        {
            try
            {
                string sql = GetSelectSql();
                if (string.IsNullOrEmpty(sql))
                {

                    return;
                }
                //this.FrmCADTaskServer.CADDbConnect.ExecuseTaskSql(sql, new object[0]);
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }
        }
        private void FormTaskInfoPriority_Load(object sender, EventArgs e)
        {
            UpdateList.Clear();
            this.toolStripComboBoxPri.SelectedIndex=0;
        
           //WhereString= this.FrmCADTaskServer.GetHandWereString(false);
           //var stream = this.FrmCADTaskServer.CADDbConnect.QueryTaskInfo(WhereString, orderby);
           // taskInfos = this.FrmCADTaskServer.DeserializeDataContractFromStream<List<PdsQueryTaskInfo>>(stream);
           // SetListView();
        }
        private string GetSelectSql()
        {
            StringBuilder sb = new StringBuilder();
            //string ids = string.Empty;
            //foreach (PdsQueryTaskInfo taskInfo in UpdateList)
            //{
            //    string sql = string.Format("UPDATE [dbo].[PT_TASK] SET [PC_PRI] ={0}  WHERE PC_TASK_ID ={1}", taskInfo.Pri, taskInfo.TaskId);

            //    sb.AppendLine(sql);

            //}
            return sb.ToString();
        }

        private void SetListView()
        {
            this.listViewTaskInfo.Items.Clear();
            for (int i = 0; i < taskInfos.Count; i++)
            {
                var lvi = new ListViewItem();
                lvi.Tag = taskInfos[i];
                for (int n = 0; n < this.listViewTaskInfo.Columns.Count - 1; n++)
                {
                    lvi.SubItems.Add("");
                }
                this.listViewTaskInfo.Items.Add(lvi);
                SetListItemText(i);
            }
        }
        private void SetListViewSort()
        {
            //taskInfos = taskInfos.OrderByDescending(p => p.Pri).ToList();
            //SetListView();
        }

        private void SetListItemText(int index)
        {
            //int i = index + 1;
            //var taskInfo = this.listViewTaskInfo.Items[index].Tag as PdsQueryTaskInfo;
            //if (taskInfo != null)
            //{
            //    ListViewItem lvi = this.listViewTaskInfo.Items[index];
            //    lvi.SubItems[this.chId.Index].Text = i.ToString();
            //    lvi.SubItems[this.chTaskName.Index].Text = taskInfo.TaskName;
            //    lvi.SubItems[this.chCreateTime.Index].Text = taskInfo.CreateTime.ToString();
            //    lvi.SubItems[this.chTemplateName.Index].Text = taskInfo.NameTemplate + "(" + taskInfo.TemplateId + ")";
            //    lvi.SubItems[this.chContractName.Index].Text = taskInfo.TaskShowName;
            //    lvi.SubItems[this.chCreator.Index].Text = taskInfo.CreatorUserNo;
            //    lvi.SubItems[this.chPriority.Index].Text = taskInfo.Pri.ToString();
            //}
        }

        private void toolStripButton1_Click(object sender, EventArgs e)
        {
            //if (string.IsNullOrEmpty(this.toolStripTextBoxFind.Text))
            //{
            //    FormTaskInfoPriority_Load(null, null);
            //    return;
            //}
            //UpdateList.Clear();

            //string whereStr = string.Format("TaskName.Contains( \"{0}\") && ({1})", this.toolStripTextBoxFind.Text, WhereString);
            //var stream = this.FrmCADTaskServer.CADDbConnect.QueryTaskInfo(whereStr, orderby);
            //taskInfos = this.FrmCADTaskServer.DeserializeDataContractFromStream<List<PdsQueryTaskInfo>>(stream);
            //SetListView();

        }

        private void ToolStripMenuItemSelectAll_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in this.listViewTaskInfo.Items)
            {
                item.Checked = true;
            }
        }

        private void toolStripMenuItemSelectNone_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in this.listViewTaskInfo.Items)
            {
                item.Checked = false;
            }
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void toolStripButtonSetPri_Click(object sender, EventArgs e)
        {
            //foreach (ListViewItem item in this.listViewTaskInfo.Items)
            //{
            //    if (item.Checked)
            //    {
            //        var taskInfo = item.Tag as PdsQueryTaskInfo;
            //        taskInfo.Pri = int.Parse(this.toolStripComboBoxPri.Text);
            //        SetListItemText(item.Index);
            //        if (!UpdateList.Contains(taskInfo))
            //        {
            //            UpdateList.Add(taskInfo);
            //        }
            //    }
            //}
            //SetListViewSort();

        }

       
    }
}

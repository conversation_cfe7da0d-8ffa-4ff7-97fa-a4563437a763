﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.ServiceModel;
using Zxtech.CADTaskServer.Contract;
using Zxtech.PdsConstant;

namespace Zxtech.CADTaskServer
{
    [SerializableAttribute]
    [ServiceBehavior(InstanceContextMode = InstanceContextMode.Single)]
    public class CADWCFServer : ICADWCFService
    {
        readonly CADTaskServer cad;
        public CADWCFServer(CADTaskServer taskServer)
        {
            this.cad = taskServer;
        }

        public CADTaskCode RunCADCode(CADTaskCode cadTask)
        {
            var cadTaskCode = cad.RunCADCode(cadTask);
            return cadTaskCode;
        }

        public bool TestServer()
        {
            return true;
        }

        public void SetCustomPropertyInfo(string filePath, string configName, Dictionary<string, string> propertyInfo)
        {
            cad.OnSetCustomPropertyInfoEvent(filePath, configName, propertyInfo);
        }

        public Dictionary<string, string> GetPropertys(string filePath, string refConfig = null)
        {
            return cad.OnGetPropertysEvent(filePath, refConfig);
        }

        public Dictionary<string, string> GetPropertys2ForSw(string filePath, string refConfig = null)
        {

           /* var code1 = GetOpenModelCommand(filePath, refConfig);
            code1 = cad.RunCADCode(code1);
            */


            var code = new CADTaskCode();
            code.CadCodeId = 6;
            code.Para1 = filePath;
            code.Para2 = CadItemType.propItem1 + "";
            code.Para4 = refConfig;
            code = cad.RunCADCode(code);
            var result = code.Para3;
            var lst = new List<string>();
            var dic = new Dictionary<string, string>();
            SingleQuoteStringParser.Unpack(result, out lst);
            for (int i = 0; i < lst.Count; i = i + 2)
            {
                dic[lst[i]] = lst[i + 1];
            }

            /*  foreach (string s in lst)
              {
                  dic[s] = "";
              }*/
            return dic;
            return cad.OnGetPropertysEvent(filePath, refConfig);
        }


        public List<string> GetFeatureList(string filePath, string configName)
        {
            //return cad.OnGetFeatureListEvent(filePath, configName);
            var code1 = GetOpenModelCommand(filePath, configName);
            code1 = cad.RunCADCode(code1);


            var code = new CADTaskCode();
            code.CadCodeId = 6;
            code.Para2 = CadItemType.featureItem+"";
            code = cad.RunCADCode(code);
            var result = code.Para3;
            var lst = new List<string>();
            SingleQuoteStringParser.Unpack(result, out lst);
            return lst;
            if (!string.IsNullOrEmpty(result))
            {
                var tempArr = result.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                return tempArr.ToList();
            }
            return new List<string>();
        }

        private static CADTaskCode GetOpenModelCommand(string filePath, string configName)
        {
            var code1 = new CADTaskCode();
            code1.CadCodeId = 1;
            code1.Para1 = filePath;
            code1.Para2 = configName;
            //ModelId
            code1.Para3 = "0";
            //FileType
            code1.Para4 = "1";
            //ShowWin
            code1.Para5 = "Y";
            return code1;
        }

        private static CADTaskCode GetCloseModelCommand(string filePath)
        {
            var code1 = new CADTaskCode();
            code1.CadCodeId = 7;
            code1.Para1 = filePath;
            //code1.Para2 = configName;
            //ModelId
            //code1.Para3 = "0";
            //FileType
            //code1.Para4 = "1";
            //ShowWin
           // code1.Para5 = "Y";
            return code1;
        }


        public List<string> GetDimlist(string modelFileName, string configName)
        {
            //return cad.OnGetDimlistEvent(modelFileName, configName);
               var code1 = GetOpenModelCommand(modelFileName, configName);
            code1 = cad.RunCADCode(code1);
            var code = new CADTaskCode();
            code.CadCodeId = 6;
            code.Para1 = modelFileName;
            code.Para2 = CadItemType.dimItem+"";
            code = cad.RunCADCode(code);
            var result = code.Para3;
            var lst = new List<string>();
            SingleQuoteStringParser.Unpack(result, out lst);
            return lst;
        }

        public List<string> GetChildPart(string filePath, string configName)
        {
            var lst1 = new List<string>();
            try
            {
                var code1 = GetOpenModelCommand(filePath, configName);
                code1 = cad.RunCADCode(code1);


                var code = new CADTaskCode();
                code.CadCodeId = 6;
                code.Para2 = CadItemType.asmComp + "";
                code.Para1 = filePath;
                code.Para4 = configName;
                /* code = cad.RunCADCode(code);
                 var result = code.Para3;
                 var lst = new List<string>();
                 SingleQuoteStringParser.Unpack(result, out lst);
                 code.Para2 = CadItemType.asmCompModelItem + "";*/
                code = cad.RunCADCode(code);
                var result = code.Para3;
                SingleQuoteStringParser.Unpack(result, out lst1);

                //lst1 = result.Split(';').ToList();
            }
            catch (Exception ee)
            {

            }

            // return cad.OnGetChildPartEvent(filePath, configName);
            return lst1;
        }

        public List<string> GetChildPartForSw(string filePath, string configName) //SW 提取
        {

            var code1 = GetOpenModelCommand(filePath, configName);
            code1 = cad.RunCADCode(code1);


            var code = new CADTaskCode();
            code.CadCodeId = 6;
            code.Para2 = CadItemType.asmComp1 + "";
            code.Para1 = filePath;
            code.Para4 = configName;
            /* code = cad.RunCADCode(code);
             var result = code.Para3;
             var lst = new List<string>();
             SingleQuoteStringParser.Unpack(result, out lst);
             code.Para2 = CadItemType.asmCompModelItem + "";*/
            code = cad.RunCADCode(code);
            var result = code.Para3;
            var lst1 = new List<string>();
            // SingleQuoteStringParser.Unpack(result, out lst1);

            lst1 = result.Split(';').ToList();
            return lst1;
            if (!string.IsNullOrEmpty(result))
            {
                var tempArr = result.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                return tempArr.ToList();
            }
            return new List<string>();
            return cad.OnGetChildPartEventForSw(filePath, configName);
        }

        public List<string> GetRefconfigList(string filePath, string configName)
        {
            var code1 = GetOpenModelCommand(filePath, configName);
            code1 = cad.RunCADCode(code1);

            var code = new CADTaskCode();
            code.CadCodeId = 6;
            code.Para2 = CadItemType.configItem + ""; 
            code.Para1 = filePath;
            code = cad.RunCADCode(code);
            var result = code.Para3;
            var lst = new List<string>();
            lst = result.Split(';').ToList();
            return lst;
            /* code.Para2 = CadItemType.asmCompModelItem + "";
            code = cad.RunCADCode(code);
            result = code.Para3;
            var lst1 = new List<string>();
            // SingleQuoteStringParser.Unpack(result, out lst1);

            lst1 = result.Split(',').ToList();
            return lst1;*/
        }

        public void CloseModel(string filePath)
        {
            var code1 = GetCloseModelCommand(filePath);
            code1 = cad.RunCADCode(code1);
        }


        public Dictionary<string, Dictionary<string, string>> GetReferenceList(string filePath, string componentNo = null)
        {
            return cad.OnGetReferenceListEvent(filePath, componentNo);
        }

        public bool ExsitsProcess(string processName)
        {
            return Process.GetProcessesByName(processName).Length > 0;
        }
    }
}

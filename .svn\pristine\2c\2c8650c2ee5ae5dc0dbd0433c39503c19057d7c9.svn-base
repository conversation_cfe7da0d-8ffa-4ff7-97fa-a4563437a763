<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:tns="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/Neuxa.EDS.Task.Service" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" />
  <xs:complexType name="Parameter">
    <xs:sequence>
      <xs:element minOccurs="0" name="DataType" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DecimalDigits" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="ID" type="xs:int" />
      <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ParameterDes" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Source" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Value" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ValueDes" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="parameterValues" nillable="true" type="tns:ArrayOfParameterValue" />
    </xs:sequence>
    <xs:attribute ref="ser:Id" />
    <xs:attribute ref="ser:Ref" />
  </xs:complexType>
  <xs:element name="Parameter" nillable="true" type="tns:Parameter" />
  <xs:complexType name="ArrayOfParameterValue">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="ParameterValue" nillable="true" type="tns:ParameterValue" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfParameterValue" nillable="true" type="tns:ArrayOfParameterValue" />
  <xs:complexType name="ParameterValue">
    <xs:sequence>
      <xs:element minOccurs="0" name="ID" type="xs:int" />
      <xs:element minOccurs="0" name="Value" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ValueDesc" nillable="true" type="xs:string" />
    </xs:sequence>
    <xs:attribute ref="ser:Id" />
    <xs:attribute ref="ser:Ref" />
  </xs:complexType>
  <xs:element name="ParameterValue" nillable="true" type="tns:ParameterValue" />
  <xs:complexType name="ArrayOfParameter">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Parameter" nillable="true" type="tns:Parameter" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfParameter" nillable="true" type="tns:ArrayOfParameter" />
  <xs:complexType name="PCBaseInfo">
    <xs:sequence>
      <xs:element minOccurs="0" name="ID" type="xs:int" />
      <xs:element minOccurs="0" name="MRD" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="ParentProCode" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PcNo" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Subject" nillable="true" type="xs:string" />
    </xs:sequence>
    <xs:attribute ref="ser:Id" />
    <xs:attribute ref="ser:Ref" />
  </xs:complexType>
  <xs:element name="PCBaseInfo" nillable="true" type="tns:PCBaseInfo" />
  <xs:complexType name="ArrayOfPCBaseInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PCBaseInfo" nillable="true" type="tns:PCBaseInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfPCBaseInfo" nillable="true" type="tns:ArrayOfPCBaseInfo" />
  <xs:complexType name="ArrayOfTaskInfoView">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="TaskInfoView" nillable="true" type="tns:TaskInfoView" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfTaskInfoView" nillable="true" type="tns:ArrayOfTaskInfoView" />
  <xs:complexType name="TaskInfoView">
    <xs:sequence>
      <xs:element minOccurs="0" name="CreateTime" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Creator" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ID" type="ser:guid" />
      <xs:element minOccurs="0" name="Istest" type="xs:boolean" />
      <xs:element minOccurs="0" name="Name" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PCNo" nillable="true" type="xs:string" />
    </xs:sequence>
    <xs:attribute ref="ser:Id" />
    <xs:attribute ref="ser:Ref" />
  </xs:complexType>
  <xs:element name="TaskInfoView" nillable="true" type="tns:TaskInfoView" />
  <xs:complexType name="ArrayOfWorkstation">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Workstation" nillable="true" type="tns:Workstation" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfWorkstation" nillable="true" type="tns:ArrayOfWorkstation" />
  <xs:complexType name="Workstation">
    <xs:sequence>
      <xs:element minOccurs="0" name="CurrentReportTime" type="xs:dateTime" />
      <xs:element minOccurs="0" name="IP" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ThreadCount" type="xs:int" />
    </xs:sequence>
    <xs:attribute ref="ser:Id" />
    <xs:attribute ref="ser:Ref" />
  </xs:complexType>
  <xs:element name="Workstation" nillable="true" type="tns:Workstation" />
  <xs:complexType name="BOM">
    <xs:sequence>
      <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="FM" nillable="true" type="q1:ArrayOfEdsFMOrg" />
      <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="Parents" nillable="true" type="q2:ArrayOfEdsBomRootOrg" />
      <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="PartSupplier" nillable="true" type="q3:ArrayOfEdsPartSupplierOrg" />
      <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="Parts" nillable="true" type="q4:ArrayOfEdsPartOrg" />
      <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="Routing" nillable="true" type="q5:ArrayOfEdsPartToRouting" />
      <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="Sons" nillable="true" type="q6:ArrayOfEdsBomOrg" />
      <xs:element minOccurs="0" name="TaskID" type="ser:guid" />
      <xs:element minOccurs="0" name="TaskRootID" type="ser:guid" />
    </xs:sequence>
    <xs:attribute ref="ser:Id" />
    <xs:attribute ref="ser:Ref" />
  </xs:complexType>
  <xs:element name="BOM" nillable="true" type="tns:BOM" />
  <xs:complexType name="BOMSO">
    <xs:sequence>
      <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="FM" nillable="true" type="q7:ArrayOfEdsFMOrgSO" />
      <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="Parents" nillable="true" type="q8:ArrayOfEdsBomRootOrgSO" />
      <xs:element xmlns:q9="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="PartSupplier" nillable="true" type="q9:ArrayOfEdsPartSupplierOrgSO" />
      <xs:element xmlns:q10="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="Parts" nillable="true" type="q10:ArrayOfEdsPartOrgSO" />
      <xs:element xmlns:q11="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="Routing" nillable="true" type="q11:ArrayOfEdsPartToRoutingOrgSO" />
      <xs:element xmlns:q12="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" minOccurs="0" name="Sons" nillable="true" type="q12:ArrayOfEdsBomOrgSO" />
      <xs:element minOccurs="0" name="TaskID" type="ser:guid" />
      <xs:element minOccurs="0" name="TaskRootID" type="ser:guid" />
    </xs:sequence>
    <xs:attribute ref="ser:Id" />
    <xs:attribute ref="ser:Ref" />
  </xs:complexType>
  <xs:element name="BOMSO" nillable="true" type="tns:BOMSO" />
</xs:schema>
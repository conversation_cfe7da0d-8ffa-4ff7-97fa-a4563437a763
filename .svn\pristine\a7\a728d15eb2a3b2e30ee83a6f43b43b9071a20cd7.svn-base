﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.NetTcpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data hostNameComparisonMode=&quot;StrongWildcard&quot; listenBacklog=&quot;10&quot; maxBufferSize=&quot;65536&quot; maxConnections=&quot;10&quot; name=&quot;NetTcpBinding_ITaskService&quot; transactionFlow=&quot;false&quot; transactionProtocol=&quot;OleTransactions&quot; transferMode=&quot;Streamed&quot;&gt;&lt;readerQuotas maxArrayLength=&quot;16384&quot; maxBytesPerRead=&quot;4096&quot; maxDepth=&quot;32&quot; maxNameTableCharCount=&quot;16384&quot; maxStringContentLength=&quot;8192&quot; /&gt;&lt;reliableSession enabled=&quot;false&quot; inactivityTimeout=&quot;00:10:00&quot; ordered=&quot;true&quot; /&gt;&lt;security mode=&quot;None&quot;&gt;&lt;message clientCredentialType=&quot;Windows&quot; /&gt;&lt;transport clientCredentialType=&quot;Windows&quot; protectionLevel=&quot;EncryptAndSign&quot; /&gt;&lt;/security&gt;&lt;/Data&gt;" bindingType="netTcpBinding" name="NetTcpBinding_ITaskService" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;net.tcp://************:602/Task/TaskService&quot; binding=&quot;netTcpBinding&quot; bindingConfiguration=&quot;NetTcpBinding_ITaskService&quot; contract=&quot;WCFEdsServer.ITaskService&quot; name=&quot;NetTcpBinding_ITaskService&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;net.tcp://************:602/Task/TaskService&quot; binding=&quot;netTcpBinding&quot; bindingConfiguration=&quot;NetTcpBinding_ITaskService&quot; contract=&quot;WCFEdsServer.ITaskService&quot; name=&quot;NetTcpBinding_ITaskService&quot; /&gt;" contractName="WCFEdsServer.ITaskService" name="NetTcpBinding_ITaskService" />
  </endpoints>
</configurationSnapshot>
﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

using Zxtech.EGI.PlatformService.Contract.Dto;

namespace Zxtech.CADTaskServer
{
    public partial class FormTaskInfoSelect : Form
    {
        private List<EpdTaskInfoDto> taskInfos;
        //public EpdTaskInfoDto selectedTaskInfo { get; private set; }
        public List<EpdTaskInfoDto> selectedTaskInfos { get; private set; } = new List<EpdTaskInfoDto>();
        public FormTaskInfoSelect(List<EpdTaskInfoDto> taskInfos)
        {
            this.taskInfos = taskInfos;
            InitializeComponent();
        }

        private void FormTaskInfoSelect_Load(object sender, EventArgs e)
        {
            if (this.taskInfos == null) return;
            for (int i = 0; i < this.taskInfos.Count; i++)
            {
                var lvi = new ListViewItem();
                lvi.Tag = taskInfos[i];
                for (int n = 0; n < this.listViewTaskInfo.Columns.Count - 1; n++)
                {
                    lvi.SubItems.Add("");
                }
                this.listViewTaskInfo.Items.Add(lvi);
                SetListItemText(i);
              
            }
        }

        private void SetListItemText(int index)
        {
             int i = index + 1;
             var taskInfo = this.listViewTaskInfo.Items[index].Tag as EpdTaskInfoDto;
            if (taskInfo != null)
            {
               
                ListViewItem lvi = this.listViewTaskInfo.Items[index];
                lvi.SubItems[this.chId.Index].Text = i.ToString();
                lvi.SubItems[this.chTaskName.Index].Text = taskInfo.Name;
                lvi.SubItems[this.chCreateTime.Index].Text = taskInfo.CreateTime.ToString();
                lvi.SubItems[this.chTemplateName.Index].Text = taskInfo.ContractNo; 
                //lvi.SubItems[this.chContractName.Index].Text = taskInfo.Name;
                lvi.SubItems[this.chCreator.Index].Text = taskInfo.CreateUser;
                lvi.SubItems[this.chProduct.Index].Text = taskInfo.ProductNo;
            }
        }
        //提取
        private void buttonOK_Click(object sender, EventArgs e)
        {
          if(  this.listViewTaskInfo.SelectedItems.Count <= 0)return;
            //if (this.listViewTaskInfo.SelectedItems.Count==1)
            //{
            //    this.selectedTaskInfo = this.listViewTaskInfo.SelectedItems[0].Tag as EpdTaskInfoDto;
            //}
            //else
            {
                for (int i = 0; i < this.listViewTaskInfo.SelectedItems.Count; i++)
                {
                    var selectEpdTaskInfo = this.listViewTaskInfo.SelectedItems[i].Tag as EpdTaskInfoDto;
                    this.selectedTaskInfos.Add(selectEpdTaskInfo);
                } 
                    
            }
          


        }

        private void listViewTaskInfo_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (this.listViewTaskInfo.SelectedItems.Count > 0)
                this.buttonOK.Enabled = true;
            else
                this.buttonOK.Enabled = false;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnQuery_Click(object sender, EventArgs e)
        {
            var tasks = this.taskInfos.ToList();
            if (!string.IsNullOrEmpty(txtTaskName.Text))
            {
                tasks = tasks.Where(o =>o.Name != null && o.Name.ToLower().Contains(txtTaskName.Text.Trim().ToLower())).ToList();
            }
            if (!string.IsNullOrEmpty(txtContractNo.Text))
            {
                tasks = tasks.Where(o => o.ContractNo != null && o.ContractNo.ToLower().Contains(txtContractNo.Text.Trim().ToLower())).ToList();
            }

            listViewTaskInfo.Items.Clear();
            for (int i = 0; i < tasks.Count; i++)
            {
                var lvi = new ListViewItem();
                lvi.Tag = tasks[i];
                for (int n = 0; n < this.listViewTaskInfo.Columns.Count - 1; n++)
                {
                    lvi.SubItems.Add("");
                }
                this.listViewTaskInfo.Items.Add(lvi);
                SetListItemText(i);

            }
        }
    }
}

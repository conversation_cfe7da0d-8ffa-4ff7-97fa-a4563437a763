﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.ServiceModel;
using System.Timers;
using System.Windows.Forms;
using System.Xml;
using Zxtech.CADTaskServer.Contract;
using Timer = System.Timers.Timer;
using System.Threading;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using ICSharpCode.SharpZipLib.Zip;
using Zxtech.CADTaskServer.Properties;
using Zxtech.EDS.ExcelAddIn.Core;
using Zxtech.EGI.PlatformService.Contract;
using Zxtech.EGI.PlatformService.Contract.Dto;
using Neuxa.EDS.ExcelAddIn.LanguagePackage;

namespace Zxtech.CADTaskServer
{
    public delegate void CADEventHander(CADArgs args);   //委托里面 方法返回类型
    public delegate void RunCADCodeEventHander(RunCADCodeArgs args);
    public delegate void ErasesAllModelOnSessionEventHander();

    [Serializable]
    public class CADTaskServer
    {
        private readonly System.Windows.Forms.Timer GetTaskTimer = new System.Windows.Forms.Timer();
        private readonly Timer GetTaskTimerOnTaskRunEnd = new Timer();
        public FormCADTaskServer formCADTaskServer;
        private ServiceHost host;
        private bool isLoad;
        private CADWorkStationInfo workStationInfo;
        public event CADEventHander GetCADTask;
        public event Func<bool> IsCanGetNewTask;
        public event RunCADCodeEventHander RunCADCodeEvent;
        public event ErasesAllModelOnSessionEventHander ErasesAllModelOnSession;
        public event System.ComponentModel.CancelEventHandler Closing;

        public static FormCADTaskServer InstanceFormCADTaskServer;
        public static CADWorkStationInfo InstanceWorkStationInfo;

        public CADWorkStationInfo Load(int language)
        {
            return Load(language, -1);
        }
        public CADWorkStationInfo Load()
        {
            return Load(0);
        }
        private bool IsHaveWindow()
        {
            if (CADTaskServer.InstanceFormCADTaskServer == null)
            {
                return false;
            }

            if (CADTaskServer.InstanceFormCADTaskServer.IsDisposed)
            {
                return false;
            }
            CADTaskServer.InstanceFormCADTaskServer.Activate();

            return true;


        }
        public CADWorkStationInfo Load(int language, int CadType,FormCADTaskServer mainForm=null)
        {
            try
            {
                if (IsHaveWindow())
                {
                    return CADTaskServer.InstanceWorkStationInfo;
                }

                if (language == 1)
                {
                    Thread.CurrentThread.CurrentUICulture = new CultureInfo("en-US");
                }


                if (this.formCADTaskServer == null)
                {
                    //if (CADTaskServer.InstanceFormCADTaskServer == null)
                    //{
                    //    CADTaskServer.InstanceFormCADTaskServer = new FormCADTaskServer();

                    //}
                    if (mainForm != null)
                    {
                        formCADTaskServer = mainForm;
                    }
                    else
                    {
                        this.formCADTaskServer = new FormCADTaskServer();
                    }
                    this.workStationInfo = new CADWorkStationInfo();
                    CADTaskServer.InstanceWorkStationInfo = this.workStationInfo;
                    CADTaskServer.InstanceFormCADTaskServer = this.formCADTaskServer;
                    this.formCADTaskServer.CadType = CadType;
                    //this.formCADTaskServer.Closed += this.formCADTaskServer_Closed;
                    formCADTaskServer.Closing += formCADTaskServer_Closing;
                    this.formCADTaskServer.GetNewCADTask += this.formCADTaskServer_GetCADTask;
                    this.formCADTaskServer.AutoGetNewCADTask += this.formCADTaskServer_AutoGetNewCADTask;
                    this.formCADTaskServer.CovertHandGet += this.formCADTaskServer_CovertHandGet;
                    if (mainForm == null)
                    {
                        this.formCADTaskServer.Show();
                    }
                    this.isLoad = true;

                    if (formCADTaskServer == null)
                    {
                        this.workStationInfo.IsLoad = false;
                    }
                    else
                    {
                        this.workStationInfo.IsLoad = true;
                        this.workStationInfo.ExpendViewName = this.formCADTaskServer.ServiceConfigInfo.ExpendViewName;
                        this.workStationInfo.PdsPath = this.formCADTaskServer.ServiceConfigInfo.PdsPath;
                        this.workStationInfo.WorkPath = this.formCADTaskServer.ServiceConfigInfo.WorkPath;
                        this.workStationInfo.CachePath = this.formCADTaskServer.ServiceConfigInfo.CachePath;
                        this.workStationInfo.BakPath = this.formCADTaskServer.ServiceConfigInfo.BakPath;
                        this.workStationInfo.PocResultPath = this.formCADTaskServer.ServiceConfigInfo.PocResultPath;
                        this.workStationInfo.nFontStrokeMode = this.formCADTaskServer.ServiceConfigInfo.nFontStrokeMode;
                        this.workStationInfo.bRebuildBaseMode = this.formCADTaskServer.ServiceConfigInfo.bRebuildBaseMode;
                        this.workStationInfo.bRebuildBaseModelInstTable = this.formCADTaskServer.ServiceConfigInfo.bRebuildBaseModelInstTable;
                        this.workStationInfo.bRebuildNewInst = this.formCADTaskServer.ServiceConfigInfo.bRebuildNewInst;
                        this.workStationInfo.bIsBackupModel = this.formCADTaskServer.ServiceConfigInfo.bIsBackupModel;
                        this.workStationInfo.bMakeDRW = this.formCADTaskServer.ServiceConfigInfo.bMakeDRW;
                        this.workStationInfo.bMakeDWG = this.formCADTaskServer.ServiceConfigInfo.bMakeDWG;
                        this.workStationInfo.UseModelCache = this.formCADTaskServer.ServiceConfigInfo.UseModelCache;
                        this.workStationInfo.KeepOpenLastDoc = this.formCADTaskServer.ServiceConfigInfo.KeepOpenLastDoc;//是否在任务执行完成后保持最后一个文档是打开的方便做脚本测试人员查看
                        this.workStationInfo.UseDocumentDbResult = this.formCADTaskServer.ServiceConfigInfo.UseDocumentDbResult;
                        this.workStationInfo.UseDocumentDbTemplate = this.formCADTaskServer.ServiceConfigInfo.UseDocumentDbTemplate;
                        this.workStationInfo.bMakeDXF = this.formCADTaskServer.ServiceConfigInfo.bMakeDXF;
                        this.workStationInfo.hasSheetPattern= this.formCADTaskServer.ServiceConfigInfo.hasSheetPattern;
                        this.workStationInfo.bMakeEDrawing = this.formCADTaskServer.ServiceConfigInfo.bMakeEDrawing;
                        this.workStationInfo.bIsBendLine = this.formCADTaskServer.ServiceConfigInfo.bDXFBendLine;
                        this.workStationInfo.bIsReadThickness = this.formCADTaskServer.ServiceConfigInfo.bDXFReadThickness;
                        this.workStationInfo.bCADReturnStatus = this.formCADTaskServer.ServiceConfigInfo.bCADReturnStatus;
                        this.workStationInfo.IsCopyDxf = this.formCADTaskServer.ServiceConfigInfo.IsCopyDxf;
                        this.workStationInfo.IsUpdateModel = this.formCADTaskServer.ServiceConfigInfo.IsUpdateModel;
                        this.workStationInfo.bMakePDF = this.formCADTaskServer.ServiceConfigInfo.bMakePDF;

                        this.workStationInfo.IsCutlineStr = this.formCADTaskServer.ServiceConfigInfo.IsCutline;
                        this.workStationInfo.bDeleteConfig = this.formCADTaskServer.ServiceConfigInfo.bDeleteConfig;
                        this.workStationInfo.bSendMail = this.formCADTaskServer.ServiceConfigInfo.bSendMail;
                        this.workStationInfo.bSend3dError = this.formCADTaskServer.ServiceConfigInfo.bSend3dError;
                        this.workStationInfo.DxfPath = this.formCADTaskServer.ServiceConfigInfo.DxfPath;
                        this.workStationInfo.DXFCopyPath = this.formCADTaskServer.ServiceConfigInfo.DxfCopyPath;
                        this.workStationInfo.EDrawingPath = this.formCADTaskServer.ServiceConfigInfo.EDrawingPath;//轻量化模型在任务文件夹下子文件夹的名称
                        this.workStationInfo.DWGVersion = this.formCADTaskServer.ServiceConfigInfo.DWGVersion;
                        this.workStationInfo.PuTimes = this.formCADTaskServer.ServiceConfigInfo.PuTimes;
                        this.workStationInfo.MaxLogFileSize = this.formCADTaskServer.ServiceConfigInfo.MaxLogFileSize;
                        this.workStationInfo.DeleteSubRevitModel = this.formCADTaskServer.ServiceConfigInfo.DeleteSubRevitModel;
                        
                        this.workStationInfo.AutoAlignDimension = this.formCADTaskServer.ServiceConfigInfo.AutoAlignDimension;
                        this.workStationInfo.InterFerenceDetection = this.formCADTaskServer.ServiceConfigInfo.InterFerenceDetection;
                        this.workStationInfo.DeleteNoStandardParam = this.formCADTaskServer.ServiceConfigInfo.DeleteNoStandardParam; 
                        this.workStationInfo.NoStandardLayer = this.formCADTaskServer.ServiceConfigInfo.NoStandardLayer;
                        this.workStationInfo.NostandardJsonPath = this.formCADTaskServer.ServiceConfigInfo.NostandardJsonPath;
                        
                        this.workStationInfo.CreoExePath = this.formCADTaskServer.ServiceConfigInfo.CreoExePath;
                        this.StartCADWCF(this, this.formCADTaskServer.ServiceConfigInfo.WCFCADServer);

                        // 计时器
                        this.GetTaskTimer.Interval = 10 * 1000;
                        this.GetTaskTimer.Tick += new EventHandler(GetTaskTimer_Tick);  // this.GetTaskTimer_Elapsed;
                        this.GetTaskTimerOnTaskRunEnd.Interval = 5000;
                        this.GetTaskTimerOnTaskRunEnd.Elapsed += this.GetTaskTimerOnTaskRunEnd_Elapsed;

                        return this.workStationInfo;
                    }
                }
                return this.workStationInfo;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
                throw;
            }

        }

        void formCADTaskServer_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            if (Closing != null)
                Closing(sender, e);
            UnLoad();
        }

        void GetTaskTimer_Tick(object sender, EventArgs e)
        {
            if (IsCanGetNewTask != null)
            {
                if (!IsCanGetNewTask())
                {
                    return;
                }
            }
            this.GetTaskTimer.Stop();
            this.InvokeGetCADTask();
        }

        private void GetTaskTimerOnTaskRunEnd_Elapsed(object sender, ElapsedEventArgs e)
        {

            // 停表
            this.GetTaskTimerOnTaskRunEnd.Stop();

            if (this.ErasesAllModelOnSession != null)
            {
                this.ErasesAllModelOnSession();
            }

            //this.GetTaskTimer.Start();20110817
        }

        public void ActivateForm()
        {
            if (this.formCADTaskServer != null)
            {
                Form activeFrom = Form.ActiveForm;
                if (activeFrom != this.formCADTaskServer)
                {
                    this.formCADTaskServer.WindowState = FormWindowState.Normal;
                    this.formCADTaskServer.Activate();
                }
            }
        }

        private void formCADTaskServer_CovertHandGet(object sender, EventArgs e)
        {
            this.GetTaskTimer.Stop();
            this.GetTaskTimerOnTaskRunEnd.Stop();
        }


        //自动获得cad任务
        private void formCADTaskServer_AutoGetNewCADTask(object sender, EventArgs e)
        {
            var tempFile = Path.Combine(Path.GetDirectoryName(this.GetType().Assembly.Location), "RabbiMQ", "TaskInfo", "taskInfo_" + Process.GetCurrentProcess().Id + ".info");
            if (Process.GetProcesses().Any(o => o.ProcessName.ToLower().StartsWith("zxtech.cad.mqlistener")) && File.Exists(tempFile) && (DateTime.Now - new FileInfo(tempFile).LastWriteTime).TotalSeconds < 180)
            {
                this.formCADTaskServer.RabbiMqFileName = tempFile;
            }
            else
            {
                string mqAppFilePath = Path.Combine(Path.GetDirectoryName(this.GetType().Assembly.Location), "RabbiMQ", "Zxtech.CAD.MQListener.exe");
                if (File.Exists(mqAppFilePath))
                {
                    var fileName = this.formCADTaskServer.TaskType + "_" + Guid.NewGuid().ToString() + ".msg";
                    this.formCADTaskServer.RabbiMqFileName = Path.Combine(Path.Combine(Path.GetDirectoryName(this.GetType().Assembly.Location), "RabbiMQ", "TaskLog", fileName));

                    var process = Process.Start(mqAppFilePath, string.Format("{0} {1} {2}", fileName, this.formCADTaskServer.TaskType, Process.GetCurrentProcess().Id));
                    process.EnableRaisingEvents = true;
                    if (process != null) process.Exited += Process_Exited;
                }
                else
                {
                    this.formCADTaskServer.RabbiMqFileName = null;
                }
            }
            this.GetTaskTimer.Start();
        }

        private void Process_Exited(object sender, EventArgs e)
        {
            GetTaskTimer.Stop();
            formCADTaskServer_AutoGetNewCADTask(sender, e);
        }

        //private void GetTaskTimer_Elapsed(object sender, ElapsedEventArgs e)
        //{
        //    this.GetTaskTimer.Stop();
        //    this.InvokeGetCADTask();
        //    //this.GetTaskTimer.Start();
        //}
        //响应获得cad任务
        private void InvokeGetCADTask()
        {
            // SendLogMessage(1, "InvokeGetCADTask()", 1);//
                if (this.GetCADTask != null)
                {
                    try
                    {
                        var args = new CADArgs { IsOpenRootModel = false };
                        this.GetCADTask(args);
                    }
                    catch (Exception e)
                    {
                        formCADTaskServer.SentLogErrorMessage(e.ToString());
                    }
                }
                if (this.formCADTaskServer.isAuto)
                {
                    if (formCADTaskServer.taskInfo != null)
                    {
                        //todo: 向中间文件中写入[Done!]命令
                        if (this.formCADTaskServer.taskInfo.Failed.HasValue && this.formCADTaskServer.taskInfo.Failed.Value)
                        {
                            this.formCADTaskServer.WriteCmd("[Failed!]");
                        }
                        else
                        {
                            this.formCADTaskServer.WriteCmd("[Done!]");
                        }

                    }
                    this.GetTaskTimer.Start();
                }
            }
        private void formCADTaskServer_Closed(object sender, EventArgs e)
        {
            this.UnLoad();
        }


        public List<CADTaskCode> GetCacheFiles(List<CADTaskCode> ls, Guid taskId, List<CADCommandCache> totalCommands, CADWorkStationInfo stationInfo, string taskFoldName)
        {

            for (int i = 0; i < ls.Count; i++)
            {
                if ((!ls[i].UseCache.HasValue || !ls[i].UseCache.Value) && stationInfo.UseModelCache)
                {
                    IList<string> cacheFiles = formCADTaskServer.GetDocoumentById(ls[i].Para3, ls[i].DocumentId?.ToString(), stationInfo.WorkPath + "\\" + taskFoldName);
                    if (cacheFiles != null && cacheFiles.Any())
                    {
                        SendLogMessage(taskId, string.Format("Find cache document!modelName:{0}  id:{1}", ls[i].Para3, ls[i].DocumentId), 1);
                        //WriteLog(string.Format("Find cache document!modelName:{0}  id:{1}", ls[i].Para3, ls[i].DocumentId), 0);
                        //使用缓存则完成状态就是finished
                        ls[i].UseCache = true;
                        ls[i].Finished = FinishStatus.Finished;
                        var initCode = ls.FirstOrDefault(o => o.PartId == ls[i].PartId && o.CadCodeId == 1300);
                        if (initCode != null)
                        {
                            initCode.UseCache = true;
                            initCode.Finished = FinishStatus.Finished;
                            initCode.Files = cacheFiles;
                        }

                        var cadCommand = totalCommands.FirstOrDefault(o => o.InitCommand == ls[i]);
                        if (cadCommand != null)
                        {
                            var childrenCmds = cadCommand.TotalChildren();
                            foreach (var childrenCmd in childrenCmds)
                            {
                                childrenCmd.InitCommand.UseCache = true;
                                childrenCmd.DriveCommand.UseCache = true;
                                childrenCmd.InitCommand.Finished = FinishStatus.Finished;
                                childrenCmd.DriveCommand.Finished = FinishStatus.Finished;
                            }
                        }
                    }
                }
            }
            return ls;

        }
        public void GetModelFiles(List<CADTaskCode> codes, Guid taskId, string path)
        {
            if (this.workStationInfo.UseDocumentDbTemplate)
            {
                var initCodes = codes.Where(p => p.CadCodeId == 1200 && p.Para3 != null).ToList();
                //"开始下载图纸...共{0}个"
                SendLogMessage(taskId, string.Format(LanguageHelper.GetString("StartDownloadDrawings"), initCodes.Count), 1);
                int count = 0;
                foreach (var code in initCodes)
                {
                    try
                    {
                        var filePath = Path.Combine(path, code.Para3.TrimStart('\\', '/'));
                        var dirPath = Path.GetDirectoryName(filePath);
                        if (!Directory.Exists(dirPath))
                        {
                            Directory.CreateDirectory(dirPath);
                        }
                        //({0}/{1})  开始查找图纸...(drawingNo:{2} docName:{3} modelType:{4} runTimePoint:{5} pcNo: {6})
                        SendLogMessage(taskId, string.Format(LanguageHelper.GetString("StartFindingDrawings"), initCodes.IndexOf(code), initCodes.Count, code.Para1, Path.GetFileName(filePath), null, this.formCADTaskServer.taskInfo.RunTimePoint.ToString("yyyy-MM-dd HH:mm:ss"), null), 1);

                        var modelInfo = DBServiceClient.Instance.EGIPlatformService.GetModelInfo(null, code.Para1, Path.GetFileName(filePath), null, this.formCADTaskServer.taskInfo.RunTimePoint);
                        if (modelInfo != null)
                        {
                            //"获取到图纸：{0}"
                            SendLogMessage(taskId, string.Format(LanguageHelper.GetString("GetDrawing"), modelInfo), 1);
                            var file = modelInfo.ModelFile;
                            if (file!=null)
                            {
                                File.WriteAllBytes(filePath, file);
                            }
                            else if (modelInfo.FileName!=null)
                            {
                                File.Copy(modelInfo.FileName, filePath);
                            }
                            count++;
                        }
                        else
                        {
                            //({0}/{1})  开始查找图纸...(drawingNo:{2} docName:{3} modelType:{4} runTimePoint:{5} pcNo: {6})
                            SendLogMessage(taskId, string.Format(LanguageHelper.GetString("StartFindingDrawings"), initCodes.IndexOf(code), initCodes.Count, code.Para1, Path.GetFileName(filePath) + ".zip", null, this.formCADTaskServer.taskInfo.RunTimePoint.ToString("yyyy-MM-dd HH:mm:ss"), null), 1);
                            modelInfo = DBServiceClient.Instance.EGIPlatformService.GetModelInfo(null, code.Para1, Path.GetFileName(filePath) + ".zip", null, this.formCADTaskServer.taskInfo.RunTimePoint);
                            if (modelInfo != null)
                            {
                                //获取到图纸：{0}
                                SendLogMessage(taskId, string.Format(LanguageHelper.GetString("GetDrawing"), modelInfo), 1);
                                var file = modelInfo.ModelFile;
                                ZipHelper.UnZip(file, dirPath, log => SendLogMessage(formCADTaskServer.taskInfo.EpdTaskId, log, 2));
                                count++;
                            }
                            else
                            {
                                //图号:{0} 的模型文件：{1} 不存在({1}.zip也不存在)！
                                SendLogMessage(taskId, string.Format(LanguageHelper.GetString("DrawingModelNone"), code.Para1, Path.GetFileName(filePath)), 2);
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        //下载图纸失败！{0}
                        SendLogMessage(taskId, string.Format(LanguageHelper.GetString("DownloadDrawingFail"), e.ToString()), 2);
                    }
                }
                //下载图纸结束!({0}/{1})
                SendLogMessage(taskId, string.Format(LanguageHelper.GetString("DownloadDrawingEnd"), count, initCodes.Count), 0);
            }
            else
            {
                //使用本地图纸
                SendLogMessage(taskId, string.Format(LanguageHelper.GetString("UseLocalDrawings")), 0);
            }

        }

        //获得任务编码列表   ，获取任务（如果是自动获取第一个任务，并且获取CAD命令，如果不是自动获取已经得到的任务CAD命令）
        public List<CADTaskCode> GetTaskCadCodeList()
        {
            if (this.formCADTaskServer == null)
            {
                throw new Exception("formCADTaskServer==null");
                return null;
            }

            List<EpdCADCommandDto> taskCadCodes = null;
            try
            {
                taskCadCodes = this.formCADTaskServer.GetTaskCadCodeList();
            }
            catch (Exception e)
            {
                MessageBox.Show(e.ToString());
            }

            var listCADTaskCode = new List<CADTaskCode>();
            if (taskCadCodes != null)
            {
                foreach (EpdCADCommandDto taskCadcode in taskCadCodes)
                {
                    var taskCode = new CADTaskCode
                    {
                        TaskId = taskCadcode.TaskId,
                        SortId = taskCadcode.SortId,
                        PartId = taskCadcode.PartOrderId,
                        CadCodeId = taskCadcode.CommandType,
                        Para1 = taskCadcode.Para1,
                        Para2 = taskCadcode.Para2,
                        Para3 = taskCadcode.Para3,
                        Para4 = taskCadcode.Para4,
                        Para5 = taskCadcode.Para5,
                        Para6 = taskCadcode.Para6,
                        Para7 = taskCadcode.Para7,
                        Para8 = taskCadcode.Para8,
                        Para9 = taskCadcode.Para9,
                        Para10 = taskCadcode.Para10,
                        Para11 = taskCadcode.Para11,
                        Para12 = taskCadcode.Para12,
                        Para13 = taskCadcode.Para13,
                        Para14 = taskCadcode.Para14,
                        Para15 = taskCadcode.Para15,
                        Para16 = taskCadcode.Para16,
                        Para17 = taskCadcode.Para17,
                        Para18 = taskCadcode.Para18,
                        Para19 = taskCadcode.Para19,
                        Para20 = taskCadcode.Para20,
                        EpdPartGuid = taskCadcode.PartUniqId,
                        DocumentId = taskCadcode.DocumentId,
                        ModelMd5 = taskCadcode.ModelMd5,
                        UseCache = taskCadcode.UseCache
                    };
                    
                    listCADTaskCode.Add(taskCode);
                }
                // SendLogMessage(1, "取到了任务" + this.formCADTaskServer.TaskRunning + this.formCADTaskServer.TaskRunning, 1);//
                this.formCADTaskServer.TaskRunning = true;

                if (this.formCADTaskServer.isAuto)
                {
                    if (listCADTaskCode.Count > 0)
                    {
                        this.GetTaskTimer.Stop();
                        this.GetTaskTimerOnTaskRunEnd.Stop();
                    }
                    else
                    {
                        // this.GetTaskTimer.Start();//20110817
                    }
                }

                return listCADTaskCode;
            }
            return null;
        }
        //随着模型更新任务件属性
        public bool UpdateTaskPartPropWithModel(Guid taskId, List<CADTaskPropFromModel> listCADTaskPropFromModel, Guid epdTaskId, string filePath)
        {

            if (this.formCADTaskServer == null)
            {
                return false;
            }
            //20130327gd 修改;
            //  this.formCADTaskServer.TaskRunning = false;
            bool isUpdate = false;
            try
            {
                if (listCADTaskPropFromModel != null)
                {
                    isUpdate = this.formCADTaskServer.UpdateTaskPartPropWithModel(taskId, listCADTaskPropFromModel, epdTaskId, filePath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.ToString());
            }

            return isUpdate;
        }

        public void SendLogMessage(Guid taskId, string message, int messageType)
        {
            if (this.formCADTaskServer == null)
            {
                return;
            }
            this.formCADTaskServer.SendLogMessage(taskId, message, messageType);
        }

        public void UnLoad()
        {
            if (this.formCADTaskServer != null)
            {
                try
                {
                    this.GetTaskTimer.Stop();
                    this.GetTaskTimerOnTaskRunEnd.Close();

                    //this.formCADTaskServer.Close();
                    this.formCADTaskServer = null;
                    if (this.host != null)
                    {
                        this.host.Close();
                        host.Abort();
                        host = null;
                    }
                }
                catch (Exception ex)
                {
                    this.formCADTaskServer.SentLogErrorMessage(ex.ToString() + ex.StackTrace);
                }
            }
        }
        //退出任务
        //获得cad任务
        private void formCADTaskServer_GetCADTask(object sender, EventArgs e)
        {
            if (this.GetCADTask != null && this.formCADTaskServer.TaskRunning == false)
            {
                var args = new CADArgs { IsOpenRootModel = true };
                this.GetCADTask(args);
            }
        }
        //运行cad编码
        public CADTaskCode RunCADCode(CADTaskCode cadCode)
        {
            if (this.RunCADCodeEvent != null)
            {
                var runArgs = new RunCADCodeArgs { CADCode = cadCode };

                this.RunCADCodeEvent(runArgs);

                return runArgs.CADCode;
            }


            return null;
        }
        //设置任务运行结束
        public void SetTaskRunEnd(Guid taskId)
        {
            // SendLogMessage(1, "SetTaskRunEnd(int taskId)  taskid=" + taskId + this.formCADTaskServer.isAuto, 1);//

            if (this.formCADTaskServer.isAuto)
            {
                if (taskId == Guid.Empty)
                {
                    //this.GetTaskTimer.Start();20110817
                }
                else
                {
                    this.GetTaskTimerOnTaskRunEnd.Start();
                }
            }
            //20130327 gd lDS autocad 增加
            this.formCADTaskServer.TaskRunning = false;
        }
        public void StartTime()
        {
                this.GetTaskTimer.Start();


        }
        //启动cad wcf
        private bool StartCADWCF(CADTaskServer cadServer, String url)
        {
            try
            {
                string[] str = url.Split('/');
                string name = str[str.Length - 1];
                string urlName = url.Remove(url.Length - name.Length - 1);

                var ser = new CADWCFServer(cadServer);
                this.host = new ServiceHost(ser, new Uri(urlName));
                var binding = new NetTcpBinding();
                binding.Security.Mode = SecurityMode.None;


                binding.ReaderQuotas = new XmlDictionaryReaderQuotas
                {
                    MaxArrayLength = 655350000,
                    MaxDepth = 163840,
                    MaxBytesPerRead = 1024,
                    MaxNameTableCharCount = 163840,
                    MaxStringContentLength = 655350000
                };
                binding.TransferMode = TransferMode.Streamed;
                binding.MaxReceivedMessageSize = 653560000;
                binding.CloseTimeout = new TimeSpan(0, 10, 0);
                binding.OpenTimeout = new TimeSpan(0, 10, 0);
                binding.ReceiveTimeout = new TimeSpan(0, 10, 0);
                binding.SendTimeout = new TimeSpan(1, 0, 0);
                var endpoint = this.host.AddServiceEndpoint(typeof(ICADWCFService), binding, name);

                this.host.Open();

                return true;
            }
            catch (Exception ex)
            {
                this.formCADTaskServer.SentLogErrorMessage(ex.ToString() + ex.StackTrace);
                return false;
            }
        }

        public void SetModelError()
        {
            this.formCADTaskServer.SetModelError();
        }

        public event Func<string, string, List<string>> GetRefconfigListEvent;

        public List<string> OnGetRefconfigListEvent(string arg1, string arg2)
        {
            Func<string, string, List<string>> handler = GetRefconfigListEvent;
            if (handler != null) return handler(arg1, arg2);
            return null;
        }

        public event Func<string, string, Dictionary<string, string>> GetPropertysEvent;

        public Dictionary<string, string> OnGetPropertysEvent(string arg1, string arg2)
        {
            Func<string, string, Dictionary<string, string>> handler = GetPropertysEvent;
            if (handler != null) return handler(arg1, arg2);
            return null;
        }

        public event Func<string, string, List<string>> GetFeatureListEvent;

        public List<string> OnGetFeatureListEvent(string arg1, string arg2)
        {
            Func<string, string, List<string>> handler = GetFeatureListEvent;
            if (handler != null) return handler(arg1, arg2);
            return null;
        }

        public event Func<string, string, List<string>> GetDimlistEvent;

        public List<string> OnGetDimlistEvent(string arg1, string arg2)
        {
            Func<string, string, List<string>> handler = GetDimlistEvent;
            if (handler != null) return handler(arg1, arg2);
            return null;
        }
        public event Func<string, string, List<string>> GetChildPartEvent;

        public List<string> OnGetChildPartEvent(string arg1, string arg2)
        {
            Func<string, string, List<string>> handler = GetChildPartEvent;
            if (handler != null) return handler(arg1, arg2);
            return null;
        }

        public event Func<string, string, List<string>> GetChildPartEventForSw;

        public List<string> OnGetChildPartEventForSw(string arg1, string arg2)
        {
            Func<string, string, List<string>> handler = GetChildPartEventForSw;
            if (handler != null) return handler(arg1, arg2);
            return null;
        }

        public event Func<string, string, Dictionary<string, Dictionary<string, string>>> GetReferenceListEvent;

        public Dictionary<string, Dictionary<string, string>> OnGetReferenceListEvent(string arg1, string arg2)
        {
            Func<string, string, Dictionary<string, Dictionary<string, string>>> handler = GetReferenceListEvent;
            if (handler != null) return handler(arg1, arg2);
            return null;
        }

        public event Action<string, string, Dictionary<string, string>> SetCustomPropertyInfoEvent;
        private string TaskName;

        public void OnSetCustomPropertyInfoEvent(string arg1, string arg2, Dictionary<string, string> arg3)
        {
            Action<string, string, Dictionary<string, string>> handler = SetCustomPropertyInfoEvent;
            if (handler != null) handler(arg1, arg2, arg3);
        }
    }


    [Serializable]
    public class CADWorkStationInfo
    {
        private string _pdsPath;
        public int GetTaskCycle { get; set; }
        public bool IsLoad { get; set; }

        public string ExpendViewName { get; set; }

        public String PdsPath
        {
            get
            {
                if (!string.IsNullOrEmpty(RestClientPoxy.Session.OrgnizationNum))
                {
                    var path = Path.Combine(_pdsPath, RestClientPoxy.Session.OrgnizationNum);
                    if (Directory.Exists(path))
                    {
                        return path;
                    }
                }
                return _pdsPath;
            }
            set { _pdsPath = value; }
        }

        public String WorkPath { get; set; }
        public string PocResultPath { get; set; }//poc土建图的运行结果, 结果是一个合并的main图纸
        public String CachePath { get; set; }
        public String BakPath { get; set; }
        public String DxfPath { get; set; }
        public String DXFCopyPath { get; set; }
        public String EDrawingPath { get; set; }
        public String DWGVersion { get; set; }
        public string  MaxLogFileSize { get; set; }
        public bool DeleteSubRevitModel { get; set; }
        public  int PuTimes { get; set; }//pu的次数
        public bool IsCopyDxf { get; set; }
        public bool AutoAlignDimension { get; set; }
        public bool bRebuildNewInst { get; set; } // 新的实例
        public bool bRebuildBaseMode { get; set; } // 基本模型
        public bool bRebuildBaseModelInstTable { get; set; } // 基本模型的实例表
        public bool bIsBackupModel { get; set; }
        public int nFontStrokeMode { get; set; } // PDF输出字体
        public bool IsUpdateModel { get; set; }
        public bool bMakeDRW { get; set; }
        public bool bMakeDXF { get; set; }
        public bool hasSheetPattern { get; set; }//是否有钣金阵列, 默认是否.如果有钣金阵列, 炸开的方式和土建不同
        public bool bIsBendLine { get; set; }
        public bool bIsReadThickness { get; set; }
        public bool bCADReturnStatus { get; set; }
        public bool bMakePDF { get; set; }
        public bool DeleteNoStandardParam { get; set; } //删除非标参数, 通力需求
        public string NoStandardLayer { get; set; }
        public string NostandardJsonPath { get; set; }//非标依据的json

        public string CreoExePath { get; set; }

        //public bool IsCutline { set; get; }
        //20221202,配置文件要改成多个租户配置是否打断尺寸线 , 不直接获取布尔值, 根据租户名称来获取
        public bool IsCutline
        {
            get { return IsForceCutline ?? false; }
        }

        private IDictionary<string, bool?> isForceCutlineMap = null;
        public bool? IsForceCutline
        {
            get
            {
                return IsCutlineByTenant();
            }
        }
        /// <summary>
        /// 20221202,根据配置信息中的租户编号, 判断是否打断尺寸线
        /// </summary>
        /// <param name="IsCutlineInfo">配置信息中的iscutline字符串</param>
        /// <param name="Tenant">包含租户信息的字符串</param>
        /// <returns>是否打断尺寸线</returns>
        private bool? IsCutlineByTenant()
        {
            var IsCutlineInfo = this.IsCutlineStr;
            var tenantNo = RestClientPoxy.Session.OrgnizationNum;
            if (string.IsNullOrEmpty(IsCutlineInfo))
            {
                return null;
            }

            if (isForceCutlineMap == null)
            {
                isForceCutlineMap = new Dictionary<string, bool?>();
                if (IsCutlineInfo.Contains("="))
                {
                    var tempArr = IsCutlineInfo.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
                    var publicConfig = tempArr.FirstOrDefault(o => !o.Contains('='));

                    if (string.IsNullOrEmpty(publicConfig))
                    {
                        isForceCutlineMap["Default"] = null;
                    }
                    else
                    {
                        isForceCutlineMap["Default"] = publicConfig.ToLower() == "true";
                    }
                    foreach (var str in tempArr.Where(o => o.Contains('=')).ToList())
                    {
                        var arr = str.Split(new[] { '=' }, StringSplitOptions.RemoveEmptyEntries);
                        var code = arr[0];
                        var v = arr.Length > 1 ? arr[1] : null;
                        if (string.IsNullOrEmpty(v))
                        {
                            isForceCutlineMap[code] = null;
                        }
                        else
                        {
                            isForceCutlineMap[code] = v.ToLower() == "true";
                        }
                    }
                }
                else
                {
                    isForceCutlineMap["Default"] = IsCutlineInfo.ToLower() == "true";
                }
            }

            return isForceCutlineMap.ContainsKey(tenantNo) ? isForceCutlineMap[tenantNo] : isForceCutlineMap["Default"];
        }
        public string IsCutlineStr { set; get; }
        public bool bMakeDWG { get; set; }
        public int nSelectTemplateId { get; set; }
        public bool bDeleteConfig { get; set; }
        public bool bSendMail { get; set; }
        public bool bSend3dError { get; set; }
        public bool UseDocumentDbResult { get; set; }
        public bool UseDocumentDbTemplate { get; set; }
        public bool UseModelCache { get; set; }
        public bool bMakeEDrawing { get; set; }
        public  bool InterFerenceDetection { get; set; }
        public  bool KeepOpenLastDoc { get; set; }
    }

    [Serializable]
    public class CADTaskPropFromModel
    {
        public int PartId { get; set; }
        public string ProValues { get; set; }
        public string ProName { get; set; }
        public string ProValue { get; set; }
        public string StrNewCfgname { get; set; }
        public Guid? EpdPartGuid { get; set; }
    }

    [Serializable]
    public class RunCADCodeArgs
    {
        public CADTaskCode CADCode { get; set; }
    }

    [Serializable]
    public class CADArgs
    {
        public bool IsOpenRootModel { get; set; }
    }
    public class CADCommandCache
    {
        public CADTaskCode InitCommand { get; set; }
        public CADTaskCode DriveCommand { get; set; }
        public IList<CADCommandCache> Children { get; set; }
        public CADCommandCache Parent { get; set; }
        public IList<CADCommandCache> TotalChildren()
        {
            var children = new List<CADCommandCache>();
            if (this.Children != null)
            {
                foreach (var cadCommand in Children)
                {
                    children.AddRange(cadCommand.TotalChildren());
                    children.Add(cadCommand);
                }
            }
            return children;
        }
    }


}
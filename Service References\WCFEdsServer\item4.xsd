<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/Arrays" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:import schemaLocation="http://192.168.2.57:8112/?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" />
  <xs:complexType name="ArrayOfKeyValueOfstringstring">
    <xs:annotation>
      <xs:appinfo>
        <IsDictionary xmlns="http://schemas.microsoft.com/2003/10/Serialization/">true</IsDictionary>
      </xs:appinfo>
    </xs:annotation>
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="KeyValueOfstringstring">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Key" nillable="true" type="xs:string" />
            <xs:element name="Value" nillable="true" type="xs:string" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfKeyValueOfstringstring" nillable="true" type="tns:ArrayOfKeyValueOfstringstring" />
  <xs:complexType name="ArrayOfKeyValueOfstringguid">
    <xs:annotation>
      <xs:appinfo>
        <IsDictionary xmlns="http://schemas.microsoft.com/2003/10/Serialization/">true</IsDictionary>
      </xs:appinfo>
    </xs:annotation>
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="KeyValueOfstringguid">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Key" nillable="true" type="xs:string" />
            <xs:element name="Value" type="ser:guid" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfKeyValueOfstringguid" nillable="true" type="tns:ArrayOfKeyValueOfstringguid" />
  <xs:complexType name="ArrayOfstring">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfstring" nillable="true" type="tns:ArrayOfstring" />
  <xs:complexType name="ArrayOfanyType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="anyType" nillable="true" type="xs:anyType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfanyType" nillable="true" type="tns:ArrayOfanyType" />
  <xs:complexType name="ArrayOfKeyValueOfstringArrayOfKeyValueOfstringstringty7Ep6D1">
    <xs:annotation>
      <xs:appinfo>
        <IsDictionary xmlns="http://schemas.microsoft.com/2003/10/Serialization/">true</IsDictionary>
      </xs:appinfo>
    </xs:annotation>
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="KeyValueOfstringArrayOfKeyValueOfstringstringty7Ep6D1">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Key" nillable="true" type="xs:string" />
            <xs:element name="Value" nillable="true" type="tns:ArrayOfKeyValueOfstringstring" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfKeyValueOfstringArrayOfKeyValueOfstringstringty7Ep6D1" nillable="true" type="tns:ArrayOfKeyValueOfstringArrayOfKeyValueOfstringstringty7Ep6D1" />
  <xs:complexType name="ArrayOfguid">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="guid" type="ser:guid" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfguid" nillable="true" type="tns:ArrayOfguid" />
  <xs:complexType name="ArrayOfKeyValueOfguidint">
    <xs:annotation>
      <xs:appinfo>
        <IsDictionary xmlns="http://schemas.microsoft.com/2003/10/Serialization/">true</IsDictionary>
      </xs:appinfo>
    </xs:annotation>
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="KeyValueOfguidint">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Key" type="ser:guid" />
            <xs:element name="Value" type="xs:int" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfKeyValueOfguidint" nillable="true" type="tns:ArrayOfKeyValueOfguidint" />
  <xs:complexType name="ArrayOfKeyValueOfNullableOfintbase64BinaryRDHGY3MA">
    <xs:annotation>
      <xs:appinfo>
        <IsDictionary xmlns="http://schemas.microsoft.com/2003/10/Serialization/">true</IsDictionary>
      </xs:appinfo>
    </xs:annotation>
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="KeyValueOfNullableOfintbase64BinaryRDHGY3MA">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Key" nillable="true" type="xs:int" />
            <xs:element name="Value" nillable="true" type="xs:base64Binary" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfKeyValueOfNullableOfintbase64BinaryRDHGY3MA" nillable="true" type="tns:ArrayOfKeyValueOfNullableOfintbase64BinaryRDHGY3MA" />
  <xs:complexType name="ArrayOfKeyValueOfNullableOfintEdsDllFileHis63Db_SSqR">
    <xs:annotation>
      <xs:appinfo>
        <IsDictionary xmlns="http://schemas.microsoft.com/2003/10/Serialization/">true</IsDictionary>
      </xs:appinfo>
    </xs:annotation>
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="KeyValueOfNullableOfintEdsDllFileHis63Db_SSqR">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Key" nillable="true" type="xs:int" />
            <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/Neuxa.EDS.PlatformDBService.EntityAndInterface" name="Value" nillable="true" type="q1:EdsDllFileHis" />
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfKeyValueOfNullableOfintEdsDllFileHis63Db_SSqR" nillable="true" type="tns:ArrayOfKeyValueOfNullableOfintEdsDllFileHis63Db_SSqR" />
</xs:schema>